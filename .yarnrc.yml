cacheFolder: ./.yarn/cache

nodeLinker: node-modules

enableGlobalCache: false

npmRegistryServer: "https://registry.npmmirror.com"

npmScopes:
  shimo:
    npmRegistryServer: "http://registry.npm.shimo.run"
  shimosre:
    npmRegistryServer: "http://registry.npm.shimo.run"

unsafeHttpWhitelist:
  - registry.npm.shimo.run

# 默认不下载 window 架构下的包，如果是 windows 开发则会匹配 current 中进行下载
# linux 是提供为 ci 用的
supportedArchitectures: 
  os:
    - current
    - linux
    - darwin
  cpu:
    - current
    - x64
    - arm64
