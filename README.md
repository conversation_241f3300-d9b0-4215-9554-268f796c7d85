# Drive Lite

现代化的云文件管理解决方案，基于 Umi.js 构建的轻量级网盘应用

## 技术栈

- **框架**: Umi.js v4 + React
- **语言**: TypeScript 5.0
- **状态管理**: Zustand
- **UI 组件库**: Ant Design v5
- **构建工具**: Umi 内置 webpack
- **代码规范**: ESLint + Prettier + Stylelint

## 快速开始

### 环境要求
- Node.js 18

### 安装依赖
```bash
yarn install
```

### 开发模式
```bash
yarn dev
```

### 生产构建
```bash
yarn build
```

## 代码如何组织
[code.md](./docs/code.md)
## 分支管理
[branch.md](./docs/branch.md)
## 如何处理国际化？
[i18n.md](./docs/i18n.md)
## 如何定制主题/换肤？
[theme.md](./docs/theme.md)
## 如何在项目中使用图片？
[image.md](./docs/image.md)

