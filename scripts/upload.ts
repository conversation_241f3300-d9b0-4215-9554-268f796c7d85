/**
 * 把编译打包好的各种静态资源文件，包括 js、css、图片等，上传到 obs
 */
import { Driver } from '@shimo/oss-utilities';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import * as mime from 'mime';

const run = (command: string) =>
  execSync(command, {
    encoding: 'utf-8',
  }).trim();

const getFile = async (client: Driver, key: string) => {
  const res = await client.get(key);
  return res?.content;
};

const putFile = async (client: Driver, key: string, body: any) => {
  await client.put(key, body, {
    contentType: mime.getType(key)!,
  });
};

const upload = async (client: Driver, key: string, body: any, type: string) => {
  const existed = await getFile(client, key);
  if (existed && key.indexOf('.html') === -1) {
    global.console.info(`upload ${type}: ${key} -- exist, skipped`);
    return;
  }
  const putFileStart = Date.now();
  await putFile(client, key, body);
  global.console.info(
    `upload ${type}: ${key} -- success  ${Date.now() - putFileStart}`,
  );
};

const uploadFile = async (
  path: string,
  file: string,
  dryrun: boolean,
  awsClient: Driver,
  cacheSet: Set<string>,
): Promise<void> => {
  // global.console.log('read file', path);
  const content = readFileSync(path);
  if (dryrun) {
    global.console.log(`uploading to fake: `, file, content);
  } else {
    return upload(awsClient, file, content, 'aws').then(() => {
      cacheSet.add(file);
    });
  }
};

// 用于记录所有产出物文件的 key 的文件的 key
const CDN_FILE_LIST_CACHE_KEY = '/static/drive/file-list.txt';

const DIST_DIRECTORY = 'dist';

const loadCache = async (client: Driver): Promise<Set<string>> => {
  const cacheContentString =
    (await getFile(client, CDN_FILE_LIST_CACHE_KEY)) ?? '';
  return new Set<string>(cacheContentString.split('\n'));
};

// 更新并上传缓存文件
const pushCache = async (
  client: Driver,
  cacheSet: Set<string>,
  files: string[],
): Promise<void> => {
  const newCacheContent = [...files, ...Array.from(cacheSet)];
  newCacheContent.length = Math.min(newCacheContent.length, 3000); // 缓存文件列表中只保留最近使用的 3000 个文件
  await putFile(client, CDN_FILE_LIST_CACHE_KEY, newCacheContent.join('\n'));
  global.console.info(`upload cache success`);
};

/**
 * 生成 lizard-view 需要的 assets
 *
 * 需要先构建好
 */
const main = async () => {
  const startTime = Date.now();
  const dryrun = process.env.DRY_RUN;
  if (!dryrun && !process.env.CI_JOB_ID) {
    global.console.warn(
      '[upload to oss] not CI environment, skipping uploading',
    );
    return;
  }

  const includes = [new RegExp(DIST_DIRECTORY)];
  const excludes = [/\.map$/, /\.d.ts$/, /\.json$/];

  const files = run(`find ${DIST_DIRECTORY} -name '*.*'`)
    .trim()
    .split('\n')
    .filter((name) => {
      return (
        includes.some((i) => i.test(name)) &&
        !excludes.some((e) => e.test(name))
      );
    })
    .map((name) => name.replace(new RegExp(`^${DIST_DIRECTORY}`), ''));
  const clientConfig = {
    type: 'obs',
    accessKeyId: process.env['OBS_ACCESS_KEY'] || 'WFTA7APUAEXO4XWA5IC6',
    secretAccessKey:
      process.env['OBS_SECRET_KEY'] ||
      'Pmd64V1ToEfJVu57cxSpjof19WT6mzHKs0GWYKhL',
    bucket: process.env['OBS_BUCKET'] || 'mo-assets',
    region: process.env['OBS_REGION'] || 'cn-north-4',
    endpoint: process.env['OBS_ENDPOINT'] || 'obs.cn-north-4.myhuaweicloud.com',
    s3ForcePathStyle: true,
  } as const;
  global.console.log('init client', JSON.stringify(clientConfig));
  const awsClient = new Driver(clientConfig);

  global.console.log('load remote cache');
  const cacheSet = await loadCache(awsClient);
  // global.console.log(Array.from(cacheSet).join('\n'));

  // files 里面的数据，如果在缓存中就从缓存中删除，因为后面会把所有 files 数据插入缓存头部，如果不在缓存中就上传
  const filesNeedToUpload: string[] = [];
  files.forEach((file) => {
    // html 文件每次都要上传，因为可能会更新
    if (cacheSet.has(file) && file.indexOf('.html') === -1) {
      cacheSet.delete(file);
    } else {
      filesNeedToUpload.push(file);
    }
  });
  global.console.log(`uploading to oss and aws: `, filesNeedToUpload);
  global.console.log(`------------------------------------------------`);
  await Promise.all(
    filesNeedToUpload.map(async (file) => {
      const localFilePath = `${DIST_DIRECTORY}${file}`;
      await uploadFile(localFilePath, `/static/drive${file}`, !!dryrun, awsClient, cacheSet);
    }),
  );
  global.console.log(`------------------------------------------------`);
  await pushCache(awsClient, cacheSet, files);

  global.console.log('upload time cost: ', Date.now() - startTime);
};

main().catch((e) => {
  global.console.error(e);
  process.exit(-1);
});
