import type { CheerioAPI } from '@umijs/utils/compiled/cheerio';

abstract class Injector {
  constructor($: CheerioAPI) {
    this.$ = $;
  }
  $: CheerioAPI;
  isDev = process.env.NODE_ENV === 'development';
  abstract inject(): void;
}

export class HeadInjector extends Injector {
  inject(): void {
    // 先找到入口 js 文件的 script 标签
    const headNode = this.$('head');
    // inject before entry script
    if (this.isDev) {
      // todo
    } else {
      // headNode.prepend(`{{.before_head}}\n`);
      headNode.append(`{{.after_head}}\n`);
    }
  }
}

export class ExternalsInjector extends Injector {
  inject(): void {}
}

export class BodyInjector extends Injector {
  inject(): void {}
}

export class EntryScriptInjector extends Injector {
  inject(): void {
    // 先找到入口 js 文件的 script 标签
    const entryNode = this.$('script[src*="umi."][src$=".js"]');
    // inject before entry script
    if (this.isDev) {
      // todo
    } else {
      entryNode.before(`{{.before_entry_script}}\n`);
      entryNode.after(`{{.after_entry_script}}\n`);
    }
  }
}
