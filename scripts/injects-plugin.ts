/**
 * 这个 umi 插件会在构建好的 html 文件中插入一些给后端渲染服务注入内容的锚点
 * 同时，这个插件也负责在本地开发的场景下将注入锚点替换为一些 mock 的内容，方便本地开发
 * 目前考虑有如下几个注入点：
 * before_head // 插入在 head 标签的开始位置
 * after_head // 插入在 head 标签的结束位置
 * before_externals // 插入在 externals 标签之前
 * after_externals // 插入在 externals 标签之后
 * before_body // 插入在 body 标签的开始位置
 * after_body // 插入在 body 标签的结束位置
 * before_entry_script // 插入在入口 js 文件之前
 * after_entry_script // 插入在入口 js 文件之后
 *
 * 注入点的语法是 {{inject_point_name}}
 */
import { IApi } from 'umi';
import {
  BodyInjector,
  EntryScriptInjector,
  ExternalsInjector,
  HeadInjector,
} from './injector';

export default (api: IApi) => {
  api.describe({
    key: 'inject something for backend rendering',
  });
  api.modifyHTML(($) => {
    const headInjector = new HeadInjector($);
    const externalsInjector = new ExternalsInjector($);
    const bodyInjector = new BodyInjector($);
    const entryScriptInjector = new EntryScriptInjector($);

    headInjector.inject();
    externalsInjector.inject();
    bodyInjector.inject();
    entryScriptInjector.inject();
    return $;
  });
};
