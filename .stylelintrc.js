module.exports = {
  // Umi 项目
  extends: require.resolve('umi/stylelint'),

  // Umi Max 项目
  extends: require.resolve('@umijs/max/stylelint'),

  rules: {
    // 完全禁用value-keyword-case规则，这样就不会检查大小写问题，避免把 css 里面的动态值变成小写
    'value-keyword-case': null,
    // 禁止代码中使用 !important
    // 'declaration-no-important': true,
    'keyframe-declaration-no-important': true,
    // 禁止代码中使用 vh 单位
    // 'unit-disallowed-list': ['vh', 'vw'],

    // 禁用 gap 属性，避免 flexbox gap 兼容问题
    'property-disallowed-list': ['gap'],

    // 颜色相关规则
    'color-hex-length': 'short',
    // 禁用颜色
    'color-no-invalid-hex': true,
    // 'color-named': 'never',
    // 'color-no-hex': true,

    // 字体相关规则
    'font-family-name-quotes': 'always-where-recommended',
    'font-family-no-duplicate-names': true,
  },

  ignoreFiles: [
    '/externals/**/*',
    '**/assets/styles/**/*.less', // 忽略主题文件中的颜色规则
  ],
};
