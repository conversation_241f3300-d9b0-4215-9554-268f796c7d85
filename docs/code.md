# 代码规范

## 项目结构
```
drive/
├── src/
│   ├── components/    # 公共组件
│   ├── pages/         # 页面模块
│   ├── layouts/       # 全局布局
│   ├── store/         # Zustand 状态管理
│   ├── api/           # API 接口配置
│   ├── service/       # 数据存取逻辑
│   ├── hooks/         # 业务逻辑
│   └── modules/       # 一些相对独立的模块
├── .umirc.ts          # Umi 配置
└── package.json
```

项目基础结构基于 umi 搭建，请阅读 [https://umijs.org/docs/guides/getting-started](https://umijs.org/docs/guides/getting-started) 了解详细使用方法。

## 数据管理

项目使用 [Zustand](https://zustand.docs.pmnd.rs/getting-started/introduction) 进行数据管理，页面中使用的业务数据都应该保存在 src/store 中的各种 store 中。但这里面理论上不应该包含页面交互过程中使用的一些中间状态数据。

比如，页面上某个输入框内容为空时，页面上的提交按钮需要是禁用状态。这个场景下，输入框的内容、输入框是否为空、按钮是否是禁用，这样的状态就不应该放在 store 中，而应该放在页面组件上。如果涉及到跨组件的中间状态，也应该尽量放在更上层的页面组件上管理。

## API

项目使用 axios 进行网络请求，但不用直接在业务中使用 axios，而应该使用 src/api/Request.ts 中提供的封装之后的方法和实例进行网络请求。所有的基础网络请求相关的代码都应该在 src/api 目录下，不要在别的地方发请求。

## 服务

服务层 src/service 负责处理基础业务数据的增删改查。服务层会调用 api 的方法发送请求获取数据，根据服务端返回的结果修改 store 中的数据，并以此驱动页面状态的更新。可以理解为服务层负责领域数据管理，比如「用户」、「文件」这些就是领域。

## 业务
业务层负责以 store 中的领域数据为基础拼装具体的业务逻辑。相关代码都应该放在 src/hooks 目录中。页面组件理论上不应该直接和服务层进行交互，而应该通过业务层封装的各种方法和组件进行业务操作。