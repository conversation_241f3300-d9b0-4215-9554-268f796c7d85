# 分支管理

drive-fe 项目主分支是 `main` 分支，**不要直接往 main 分支提交代码**。

除了 main 分支外，还会有 `feat-xxx` 分支以及 `fix-xxx` 分支用于功能开发和 bug 修复。

注意分支名称不要太长，最好不要超过 20 个字符。名称采用 kebab-case 命名方式，所有字符全小写，单词之间用 `-` 连接。

## 功能开发流程
1. 从 `main` 分支切出 feat-xxx 的功能分支，在 feat-xxx 分支上进行功能开发。开发完成后提交给测试同学在 feat-xxx 分支上进行功能测试。
2. 测试并修复所有 bug 之后，提交合并到 main 分支的 merge request 并通知相关同学进行 code review。
3. review 并修复所有问题之后合并代码到 `main` 分支。如果 review 过程中需要大量修改代码，要考虑提交给测试同学补充测试。
4. 代码合并后通知测试同学进行回归测试。

## bug 修复流程
> 此流程仅适用于 修复 `main` 分支上发现的 bug，功能开发过程中发现的 bug 不在此流程适用范围内
1. 从 `main` 分支切出 fix-xxx 的功能分支，在 fix-xxx 分支上进行功能开发。开发完成后提交给测试同学在 fix-xxx 分支上进行功能测试。
2. 测试通过之后，提交合并到 main 分支的 merge request 并通知相关同学进行 code review。
3. review 并修复所有问题之后合并代码到 `main` 分支。如果 review 过程中需要大量修改代码，要考虑提交给测试同学补充测试。
4. 代码合并后通知测试同学进行回归测试。

