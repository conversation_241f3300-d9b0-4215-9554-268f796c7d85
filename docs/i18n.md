# 国际化

项目使用 umi 自带的国际化系统进行国际化相关的翻译资源配置，可以参考 [https://umijs.org/docs/max/i18n](https://umijs.org/docs/max/i18n) 这篇文档了解基础用法。

为了简化在项目内使用翻译资源的流程，src/modules/Locale.ts 中封装了一些简化操作的工具方法。比如 `useFormatMessage`。

之前的写法：
```typescript
import { useIntl } from 'umi';
//...
function HelloComponent(){
  const intl = useIntl();
  const message = intl.formatMessage({
    id: 'message-id'
  });
  // ...
}
```
用了 `useFormatMessage` 之后：
```typescript
import { useFormatMessage } from '@/util/locale';
//...
function HelloComponent(){
  const intl = useIntl();
  const message = formatMessage('message-id');
  // ...
}
```

关于翻译资源 id 如何取值，可以考虑用 `${ComponentName}.${VariableName}` 的格式。
比如在 LoginView.tsx 中有一个翻译文案需要作为密码输入框的占位符，那么这个文案的变量可以命名为 `passwordPlaceholder`，则相对应的翻译文案 id 就是 `LoginView.passwordPlaceholder`。
对于 service 中的方法，也可以用类似的方式来取 id，比如 service/Me.ts 中的需要定义一个匿名用户的默认名称，可以取 id 为 `service.Me.anonymousName`。