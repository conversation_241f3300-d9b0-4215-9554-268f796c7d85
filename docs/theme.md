# 主题

考虑到后续需要支持高精度的自定义主题（antd 的那套算法太简单了，无法满足设计精确到某一色值的需求），因此采用了自定义主题色配置的方式进行配置。再次基础上同样支持 antd 配置项进行配置。

在开发的过程中需要注意目前项目需要支持亮暗模式主题切换。

## 1、自定义主题

目前项目中存在亮暗色主题配置两套: /src/assets/styles 下的两个文件 dark.less 和 light.less 文件文件的前半部分定义了整个项目的基础色，这里我们把它叫色卡，后面我们用到是颜色都是从这里演变过来的

```typescript
  --brand-color: #41464b;
  --red10: #af3a37;
  --red9: #c04140;
  ...
  ...
  --transparency4: rgba(65, 70, 75, 4%);
  --transparency2: rgba(65, 70, 75, 2%);
  --theme-brand-color: var(--brand-color);

```

这部分内容是禁止修改的！这部分内容是禁止修改的！这部分内容是禁止修改的！重要的事情说三遍。

后半部分定义了我们在开发过程中可能会用到主题色，

```typescript
// 语义化
  // 基本配色
  --theme-basic-color-primary: var(--brand-color);
  --theme-basic-color-notice: var(--blue5);
  ...
  ...
  --theme-efficiency-card-color-bg: linear-gradient(272deg, #f1f5ff 0%, #f8faff 100%);
  --theme-efficiency-button-color-bg: linear-gradient(0deg, var(--gray120) -0.09%, var(--gray90) 100%);
```

这部分内容也只允许新增不允许删除，且在新增的时候一定注意在两个主题文件 dark.less 和 light.less 中同时新增。

在使用和配置颜色之前先通过 UI 给出的色值找到对应的色卡，再通过色卡找到对应功能的定义名称，可能会有多个，根据开发者自己需要配置的属性选择合适的主题定义 key

2、主题注入机制

```typescript
  //antd组件注入
  <ConfigProvider
    theme={{
      ...themeConfig,
      algorithm: currentTheme.isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
    }}
    wave={{ disabled: true }}
  >
  //自定义组件注入
  useInitTheme();
  const { isDark } = useThemeStore((state) => state.theme);

  useEffect(() => {
    if (isDark) {
      document.body.classList.add('drive-dark');
    } else {
      document.body.classList.remove('drive-dark');
    }
  }, [isDark]);
```

3、主题切换控制

```typescript
// 主题切换控制
const commonThemeFun = useCallback(
  (darkModeMedia: MediaQueryList) => {
    const newStoredTheme = getStoredTheme();
    if (!newStoredTheme || newStoredTheme === 'System') {
      setTheme({ isDark: darkModeMedia.matches });
    } else {
      setTheme({ isDark: newStoredTheme === 'Dark' ? true : false });
    }
  },
  [setTheme],
);
```

## 2、使用示例

例：需要将全局的 antd 的默认按钮背景色设置为主题色 ，在色卡中 是 --brand-color。

#### 方法一

在 /src/themes/config.ts components 下找到 Button 配置项，配置他的 defaultBg

```typescript
    Button: {
      defaultBg:'var(--brand-color)'
    },
```

#### 方法二

在 /src/global.less 添加全局样式

```typescript
.ant-btn-default {
  background: var(--brand-color);
}
```

### 自此，该按钮就能在不同的模式下正确显示对应的色值了。
