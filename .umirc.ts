import { defineConfig } from 'umi';

const HOST = 'drv-dev.shimorelease.com';
const ORIGIN = `https://${HOST}`;
const WS_ORIGIN = `https://ws.${HOST}`;
const OBS_UPLOAD_ORIGIN = 'smdev-svc-drive.obs.cn-north-4.myhuaweicloud.com';

const EditorPaths = ['/doc', '/spreadsheet', '/slides'];

const ProxyRules = [
  '/api/v1',
  '/lizard-api',
  '/lizard-one',
  '/perception',
  '/panda-api',
  '/static',
  '/agreements',
  '/create',
  '/logout',
  '/loginByPassword',
  '/pricing',
  '/sdk',
  '/edit',
  '/comment-api',
  '/aioa',
  '/uploader/f',
  '/minio',
  '/sdk',
  /^\/\w{16}\/collaborators/, // 协作者
  ...EditorPaths,
  `/${OBS_UPLOAD_ORIGIN}`,
];

function getProxyContext(path: string) {
  for (const rule of ProxyRules) {
    if (typeof rule === 'string' && new RegExp(`^${rule}(?!\\w)`).test(path)) {
      return true;
    } else if (rule instanceof RegExp && rule.test(path)) {
      return true;
    }
  }

  return false;
}

export default defineConfig({
  base: '/',
  publicPath: process.env.WEBPACK_PUBLIC_PATH,
  jsMinifier: 'esbuild', // 关键：指定使用 esbuild
  jsMinifierOptions: {
    // esbuild 专属配置
    minify: true,
    target: 'es2015',
    format: 'iife', // 强制 IIFE 格式
    minifyIdentifiers: true,
    minifySyntax: true,
    minifyWhitespace: true,
  },
  routes: [
    { path: '__DRIVE__/config', component: 'DriveConfig' },
    { path: '/', redirect: '/desktop' },
    { path: '/login', component: 'Login' },
    { path: '/desktop', component: 'Desktop' },
    { path: '/docs/:guid', component: 'Editor' },
    { path: '/docx/:guid', component: 'Editor' },
    { path: '/sheets/:guid', component: 'Editor' },
    { path: '/presentation/:guid', component: 'Editor' },
    { path: '/tables/:guid', component: 'Editor' },
    { path: '/forms/:guid', component: 'Editor' },
    { path: '/forms/:guid/fill-form', component: 'Editor' },
    { path: '/workbench', redirect: '/recent' },
    { path: '/folder/:guid', component: 'Desktop' },
    { path: '/space', component: 'Space' },
    { path: '/space/:guid', component: 'Desktop' },
    { path: '/recent', component: 'Recent' },
    { path: '/share', component: 'Share' },
    { path: '/favorites', component: 'Favorites' },
    { path: '/trash', component: 'Trash' },
    { path: '/profile/accountinfo', component: '@/pages/Profile/AccountInfo' },
    { path: '/profile/preference', component: '@/pages/Profile/Preference' },
    { path: '/audit', component: 'AuditLog' },
    { path: '/error', component: 'Error' },
  ],
  // 兼容性要求参考：https://shimo.im/docs/WkMZMxtQ2LQVUgF2
  targets: {
    chrome: 78,
    edge: 84,
    safari: '13.6',
  },
  hash: true,
  codeSplitting: {
    jsStrategy: 'depPerChunk',
  },
  npmClient: 'yarn',
  proxy: {
    context: getProxyContext, // 不支持的界面需要直接跳转到 lizard 项目中
    target: ORIGIN,
    secure: false,
    changeOrigin: true,
    cookieDomainRewrite: {
      [`.${HOST}`]: '',
      [`${HOST}`]: '',
    },
    onProxyRes: (proxyRes: any, req: any, res: any) => {
      //  后端 register 和 login 接口 set cookie 时设置了 Secure，用本地 ip 访问 dev server 时带 Secure 标识会导致 set cookie 失败
      if (
        ['/api/v1/auth/password/register', '/api/v1/auth/password/login', '/api/v1/auth/password/login'].includes(
          req.originalUrl,
        )
      ) {
        const setCookieHeader = proxyRes.headers['set-cookie'];
        if (typeof setCookieHeader === 'string') {
          proxyRes.headers['set-cookie'] = setCookieHeader.replace('Secure', '').replace('SameSite=None', '');
        } else if (Array.isArray(setCookieHeader)) {
          for (let index = 0; index < setCookieHeader.length; index++) {
            const cookie = setCookieHeader[index];
            setCookieHeader[index] = cookie.replace('Secure', '').replace('SameSite=None', '');
          }
        }
      }
    },
    headers: {
      Referer: ORIGIN,
      Origin: ORIGIN,
      'X-Request-From': 'Lizard-View-Proxy',
    },
  },
  plugins: [
    '@umijs/plugins/dist/locale',
    './scripts/build-meta-plugin.ts',
    './scripts/externals-plugin.ts',
    './scripts/prefetch-plugin.ts',
    './scripts/injects-plugin.ts',
  ],
  locale: {
    default: 'zh-CN',
    antd: true, // 如果使用 antd
  },
  copy: [
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
  ],
  externals: {
    react: 'window.React',
    'react-dom': 'window.ReactDOM',
    lodash: 'window._',
    dayjs: 'window.dayjs',
  },
});
