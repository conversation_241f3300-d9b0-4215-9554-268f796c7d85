export default {
  'hooks.Authorization.loginFailedMessage': 'Login failed',
  'service.Me.anonymousName': 'Anonymous',
  'Common.loadingText': 'System is processing, please wait...',
  'File.file': 'file',
  'File.document': 'document',
  'File.newdoc': 'Documents',
  'File.modoc': 'Classic Docs',
  'File.mosheet': 'Sheets',
  'File.presentation': 'Presentation',
  'File.table': 'Tables',
  'File.form': 'Forms',
  'File.normalForm': 'Normal Form',
  'File.tableViewForm': 'Table View Form',
  'File.quizForm': 'Quiz Form',
  'File.folder': 'Folder',
  'Login.loginTitle': 'Login',
  'Login.autoLanguageSetting': 'Auto',
  'Login.autoThemeSetting': 'Auto',
  'Login.lightThemeSetting': 'lightMode',
  'Login.darkThemeSetting': 'darkMode',
  'LoginView.userNameInputPlaceholder': 'Enter your account',
  'LoginView.passwordInputPlaceholder': 'Enter your password',
  'LoginView.loginButtonText': 'Login',
  'LoginView.userNameInputLabel': 'Email',
  'LoginView.passwordInputLabel': 'password',
  'LoginView.emailFormatInaccurate': 'The email format is inaccurate',
  'MessageCenter.onlyreadButtonText': 'Look read',
  'MessageCenter.onlyUnreadButtonText': 'Look unread',
  'MessageCenter.allMarkReadButtonText': 'All marks are not read',
  'UserCenter.settings': 'Settings',
  'UserCenter.myBusiness': 'My business',
  'UserCenter.switchLanguages': 'Switch languages',
  'UserCenter.logOut': 'Log out',
  'UserCenter.myDesktopCapacit': 'My desktop capacity',
  'UserCenter.totalEnterpriseCapacity': 'Total enterprise capacity',
  'SearchCenter.me': 'Me',
  'SearchCenter.update': 'Update',
  'SearchCenter.open': 'Open',
  'SearchCenter.searchFile': 'Search file',
  'SearchCenter.noData': 'No search results',
  'SearchCenter.used': 'Recently used',
  'SearchCenter.search': 'Search results',
  'Header.backButtonTipText': 'Back',
  'Header.backToButtonTipText': 'Back to',
  'Header.createButtonTipText': 'Create',
  'Header.inputPlaceholder': 'Untitled',
  'Header.teamButtonText': 'Collaborate',
  'Header.shareButtonText': 'Share',
  'Header.fileMenuButtonTipText': 'File menu',
  'Header.historyButtonText': 'History',
  'Header.demoButtonText': 'Presentation',
  'Editor.saveStatus.offlineSaving': 'Saving offline content',
  'Editor.saveStatus.offlinePersistSucceed': 'Edits saved offline, will sync when connected',
  'Editor.saveStatus.offline': 'No network connection, content will be saved offline',
  'Editor.saveStatus.offlinePersistFailed': 'Offline save failed',
  'Editor.saveStatus.online': 'Content will auto-save',
  'Editor.saveStatus.onlineSaving': 'Saving',
  'Editor.saveStatus.saveAccepted': 'Saving',
  'Editor.saveStatus.saveSucceed': 'Auto-save successfully',
  'Editor.saveStatus.applySucceed': 'Content auto-updated',
  'Editor.saveStatus.saveFailed': 'Save failed',
  'Editor.saveStatus.applyFailed': 'Save failed',
  'Editor.saveStatus.saveTimeout': 'Save failed',
  'Editor.saveStatus.acceptTimeout': 'Save failed',
  'Editor.syncStatus.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncStatus.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncStatus.syncFailed': 'Save failed',
  'Editor.noSupport': 'This feature is not currently supported',
  'Editor.ok': 'Ok',
  'Editor.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncFailed': 'Offline data sync failed',
  'Editor.noEditing': 'System has prohibited editing',
  'Editor.noEditingContent': 'You do not have permission to edit this document, please contact the file manager',
  'Editor.sorry': 'Sorry...',
  'Editor.fileDeleted': 'The file has been deleted',
  'Editor.saveFailed': 'Save failed',
  'Editor.saveFailedContent': 'Save failed, please copy the current editing data, refresh and continue to use',

  'Header.favorite': 'Favorite',
  'Header.unfavorite': 'Unfavorite',
  'Header.favorited': 'Favorited',
  'Header.unfavorited': 'Unfavorited',
  'BackToPopover.searchFiles': 'Search files...',
  'BackToPopover.backTo': 'Back to',
  'BackToPopover.myDesktop': 'My Desktop',
  'BackToPopover.workbench': 'Workbench',
  'BackToPopover.recentlyUsed': 'Recently Used',
  'BackToPopover.quickAccess': 'Quick Access',
  'AvatarGroup.restCount': '{count} more collaborators',
  'SiderMenu.siderMenuCreactText': 'create',
  'SiderMenu.siderMenuRecentText': 'Recent Files',
  'SiderMenu.siderMenuShareText': 'Share with me',
  'SiderMenu.siderMenuFavoritesText': 'My Collection',
  'SiderMenu.siderMenuDesktopText': 'Desktop',
  'SiderMenu.siderMenuSpaceText': 'Space',
  'SiderMenu.siderMenuTrashText': 'Trash',
  'SiderMenu.siderMenuBusinessText': 'Business',
  'SiderMenu.siderMenuCreateDocText': 'Doc',
  'SiderMenu.siderMenuCreateMoDocText': 'MoDoc',
  'SiderMenu.siderMenuCreateTableText': 'Sheet',
  'SiderMenu.siderMenuCreateMoTableText': 'Table',
  'SiderMenu.siderMenuCreatePptText': 'PPT',
  'SiderMenu.siderMenuCreateFormText': 'Form',
  'SiderMenu.siderMenuCreateOrdinaryFormText': 'Sheet',
  'SiderMenu.siderMenuCreateTableFormText': 'Table sheet',
  'SiderMenu.siderMenuCreateTestFormText': 'Test sheet',
  'SiderMenu.siderMenuCreateFolderText': 'Folder',
  'SiderMenu.siderMenuCreateSpaceText': 'Space',
  'SiderMenu.siderMenuCreateUploadFolderText': 'Upload folder',
  'SiderMenu.siderMenuCreateUploadFileText': 'Upload file',
  'SiderMenu.siderMenuCreateTemplateText': 'Create from Template Library',
  'CreateFileMenu.networkStatusTipText': 'Please check your network',
  'CreateWithNamePop.folderTitle': 'New folder',
  'CreateWithNamePop.saveVersionTitle': 'Save version',
  'CreateWithNamePop.spaceTitle': 'Create a new team space',
  'CreateWithNamePop.folderPlaceholder': 'Please enter the folder name',
  'CreateWithNamePop.spacePlaceholder': 'Please enter team space name',
  'CreateWithNamePop.ruleMessage': 'Please provide complete information',
  'CreateWithNamePop.saveVersionPlaceholder': 'Please enter save version name',
  'Editor.openInNewTab': 'Open in new tab',
  'AddNewPopover.templateLibrary': 'Template',
  'AddNewPopover.upload': 'Upload',
  'AddNewPopover.uploadFolder': 'Upload Folders',
  'Error.loginGuideTitle': 'Please log in to access',
  'Error.loginText': 'Login',
  'Error.file404Title': 'The page you visited does not exist',
  'Error.goBackHomePage': 'Back to Home',
  'Error.fileDeleteTitle': 'Error',
  'Error.fileDeleteSubTitle': '{fileName} Deleted',
  'Error.fileNoPermissionTitle': 'No access permission',
  'Error.fileNoPermissionSubTitle':
    'The current login account {name} does not have permission to access this {fileName}',
  'Error.switchAccount': 'Switch account',
  'Error.accessRestrictedTitle': 'Login required to access',
  'Error.noSeatsTitle':
    'You have not yet obtained a seat for this package. Please contact the administrator to allocate or purchase a seat.',
  'Error.unknownErrorTitle': 'The page did not load successfully',
  'Error.unknownErrorSubTitle': 'Please make sure that the network is good and refresh the page to try again.',
  'deleteConfirm.title': 'Confirm the deletion',
  'deleteConfirm.content':
    'Confirm the deletion of files? Once deleted, all collaborators will lose access to the file.',
  'deleteConfirm.cancel': 'Cancel',
  'deleteConfirm.success': 'The deletion is successful',
  'deleteConfirm.error': 'Deletion failed',
  'useFileDetail.creator': 'Creator',
  'useFileDetail.modocTitle': 'Document Information',
  'useFileDetail.mosheetTitle': 'Table Information',
  'useFileDetail.tableTitle': 'Application form information',
  'useFileDetail.pptTitle': 'Slide information',
  'useFileDetail.formTitle': 'Form Information',
  'useFileDetail.statisticWordCount': 'Total word count',
  'useFileDetail.charCount': 'Number of characters (including spaces)',
  'useFileDetail.charCountWithoutSpaces': 'Character count (including spaces)',
  'useFileDetail.page': 'Number of pages',
  'useFileDetail.total': 'Total number of words',
  'useFileDetail.paragraphCount': 'Number of paragraphs',
  'useFileDetail.sections': 'Number of sections',
  'useFileDetail.views': 'Number of reads',
  'useFileDetail.times': 'times',
  'RenameModal.edit': 'Edit',
  'RenameModal.editSuccess': 'Edit success',
  'RenameModal.editError': 'Edit error',
  'RenameModal.title': 'File rename',
  'RenameModal.InputPlaceholder': 'Please enter a name for the file',
  'RenameModal.validatorMessage': 'The file name must not contain the following illegal characters',
  'formatTime.justNow': '1 minute',
  'formatTime.minutesAgo': '{minutes} minutes ago',
  'formatTime.today': 'Today, {hhmm}',
  'formatTime.yesterday': 'Yesterday, {hhmm}',
  'File.setFilter': 'Set up filtering',
  'File.clearTrash': 'Empty the recycle bin',
  'File.clearTrashSuccess': 'The recycle bin was emptied successfully',
  'File.clearTrashError': 'Failed to empty the recycle bin',
  'File.clearTrashWarn':
    'Whether you confirm that the recycle bin is emptied, confirm that it will not be able to be retrieved, please operate with caution!',
  'File.clearTrashTips':
    'Tips: The files in the recycle bin are also digital assets of the enterprise, and the enterprise has the right to recycle',
  'File.resetFirst': 'The file has been restored to its original location',
  'File.resetError': 'Recovery failed',
  'File.checkedTotal': '{checked} items selected files',
  'File.fileName': 'file name',
  'File.createName': 'Created by',
  'File.updateTime': 'Last update',
  'File.updatedAt': 'Updated',
  'File.openTime': 'Open time',
  'File.editTime': 'Edit Time',
  'File.createdAt': 'Creation time',
  'File.fileSize': 'Size',
  'File.uncheck': 'Uncheck',
  'File.allCheck': 'Select all',
  'File.move': 'Move',
  'File.delete': 'Delete',
  'File.more': 'More',
  'File.deleteSuccess': 'The deletion is successful',
  'File.deleteError': 'Deletion failed',
  'File.deleteTips': 'The maximum limit for bulk deleting files is {max}',
  'File.newTabOpens': 'A new tab opens',
  'File.edit': 'Edit',
  'File.star': 'Add to my favorites',
  'File.starSuccess': 'Added to my favorites successfully',
  'File.starError': 'Failed to add to my favorites',
  'File.removeStar': 'Removed from my favorites',
  'File.removeSuccess': 'Removed from my favorites successfully',
  'File.removeError': 'Failed to remove from my favorites',
  'File.share': 'Share',
  'File.view': 'View',
  'File.shareInfo': 'Sharing Information',
  'File.collaboration': 'Collaboration',
  'File.download': 'Download',
  'File.downloadSuccess': 'The download was successful',
  'File.downloadError': 'The download failed',
  'File.png': 'Image',
  'File.reName': 'Rename',
  'File.moveTo': 'Move to',
  'File.copyTo': 'Create a copy to',
  'File.clearRecord': 'Clear the record',
  'File.clearFilter': 'Clear the filter',
  'File.recentlyOpened': 'Recently opened',
  'File.recentlyEdit': 'Recently edit',
  'File.deleteTime': 'Deletion time',
  'File.recover': 'Recover files',
  'File.deleteCompletely': 'Delete completely',
  'File.noShareTitle': 'I haven not received any invitations to collaborate yet',
  'File.noShareDescription': 'You can see the files and folders where you have been added as a writer here.',
  'File.noFavoritesTitle': 'No favorites yet',
  'File.noFavoritesDescription': 'You can see your favorite files and folders here.',
  'File.noDesktopTitle': 'No files have been created yet.',
  'File.noDesktopDescription': 'You can see the files you created here.',
  'File.noTrashTitle': 'There are still no garbage and files.',
  'File.noTrashDescription':
    'You can see the deleted files and folders here, and the files and folders here can be restored to their original location.',
  'MessageCenter.commented': 'commented on you',
  'MessageCenter.mentioned': 'You were mentioned',
  'MessageCenter.addCollaborator': 'Add you as a collaborator',
  'MessageCenter.setAdministrator': 'Set you up as an enterprise administrator',
  'MessageCenter.inviteJoinBusiness': 'Invite you to join the business',
  'MessageCenter.newMembersJoin': 'New members join',
  'MessageCenter.deleteDocument': 'Delete the document',
  'MessageCenter.remindsReviewTasks': 'Reminds you to review tasks',
  'MessageCenter.liked': 'Liked',
  'MessageCenter.NotificationToDoChanges': 'Removed you in',
  'MessageCenter.dateArrived': 'The date has arrived',
  'MessageCenter.mentionYou': 'Mention you',
  'MessageCenter.moveYouOfBusiness': 'Move you out of the business',
  'MessageCenter.handingOverBusinessToYou': 'Handing over the business to you',
  'MessageCenter.companyNameChanged': 'The company name has been changed"{name}"',
  'MessageCenter.openedBusinessLink': 'The comment opened the business invitation link',
  'MessageCenter.closedBusinessLink': 'The business invitation link is turned off',
  'MessageCenter.taskReminders': 'Task reminders',
  'MessageCenter.tableSelectionReminders': 'Table selection reminders',
  'MessageCenter.changeUserConfig': 'Changing the ID (Email, Nickname, Password) of a Member',
  'MessageCenter.systemNotifications': 'System notifications',
  'MessageCenter.application': 'Application',
  'MessageCenter.markRead': 'Mark as read',
  'MessageCenter.join': 'Join',
  'MessageCenter.discussion': 'Discussion',
  'MessageCenter.permissions': 'Permissions',
  'MessageCenter.basicInformationModification': 'Basic information modification',
  //团队空间
  'Space.countSpace': 'spaces',
  'Space.createSpace': 'New space',
  'Space.createTeamSpace': 'Create a new team space',
  'Space.sure': 'Sure',
  'Space.cancel': 'Cancel',
  'Space.enterSpaceName': 'please enter space name',
  'Space.SpaceNameHeader': 'Space name',
  'Space.infoSuccess': 'Created successfully',
  'Space.infoEmpty': 'The space name cannot be empty',
  'Space.infoWaring1': 'There should be no Spaces at the beginning. They have been automatically removed',
  'Space.infoWaring2': 'The space name cannot exceed 20 characters',
  'Space.rightClickShare': 'Share',
  'Space.rightClickCollaboration': 'Collaboration',
  'Space.rightClickSetting': 'Setting',
  'Space.rightClickDelete': 'Delete',
  'Space.infoEditSuccess': 'edit successfully',
  'Space.teamspaceSetting': 'Team Space Setting',
  'Space.teamspaceOwnership': 'Ownership',
  'Space.teamspaceWhatOwnership': 'What is ownership?',
  'Space.teamspaceBelongsto': 'The ownership of this space belongs to',
  'Space.teamspaceOwnershipExp':
    'Space Ownership determines who manages the files in the team space. If the ownership belongs to the enterprise, it will be affected by the enterprise is security Settings, be included in the audit system, and be incorporated into the statistics of the performance dashboard.',
  'Space.teamspaceConfirmDeletion': 'Confirm deletion',
  'Space.teamspaceDeleteSuccess': 'Deletion successful',
  'Space.teamspaceDeleteTipText':
    'Have you confirmed the deletion of the team space? After deletion, all collaborators will be unable to access this team space',
  'Space.noTeamspace': 'No Team Space Available',
  'Space.noTeamspaceTipText': 'Click the New button in the top-left corner to create a new space for your team',
  //个人设置
  'Profile.title': 'Personal Settings',
  'Profile.accountInfo': 'Account information',
  'Profile.preferenceSitting': 'Preference Settings',
  'Profile.accountID': 'Account ID',
  'Profile.modifyInfo': 'Modify the basic information',
  'Profile.safetySetting': 'Safety Settings',
  'Profile.accountPd': 'Password',
  'Profile.modify': 'Modify',
  'Profile.modifyImg': 'Modify the profile picture',
  'Profile.uploadImg': 'Upload the avatar',
  'Profile.nickName': 'Nickname',
  'Profile.enterNickname': 'Please enter the nickname',
  'Profile.nickNameRule': 'The nickname must not exceed 20 characters at most',
  'Profile.modifySuccess': 'The modification of user information was successful',
  'Profile.modifyFailed': 'Failed to modify the user information',
  'Profile.getUploadToken': 'Failed to obtain the uploaded image token',
  'Profile.uploadImgSuccess': 'The picture was uploaded successfully',
  'Profile.uploadImgFailed': 'Failed to upload the picture',
  'Profile.changePd': 'Change the password',
  'Profile.forgetPd': 'Forgot the password',
  'Profile.currentPassword': 'Current password',
  'Profile.newPassword': 'New password',
  'Profile.confirmNewPassword': 'Confirm the new password',
  'Profile.currentPasswordRequired': 'The current password must be filled in',
  'Profile.currentPasswordPlaceholder': 'Please enter the current password',
  'Profile.newPasswordPlaceholder': 'Please enter the new password',
  'Profile.newPasswordRequired': 'Enter a new password',
  'Profile.newPasswordLength8': 'The password must be no less than 8 characters',
  'Profile.newPasswordRule': 'The password must contain numbers, uppercase and lowercase English letters',
  'Profile.confirmNewPasswordPlaceholder': 'Please enter the new password again',
  'Profile.confirmNewPasswordRequired': 'Please confirm the new password',
  'Profile.confirmNewPasswordMatch': 'The passwords entered twice are inconsistent',
  'Profile.changePdSuccess': 'The password was modified successfully.',
  'Profile.changePdFailed': 'Failed to modify the password',
  'Profile.uploadImgRuleType': 'Only images in JPG, PNG, GIF and JPEG formats can be uploaded',
  'Profile.uploadImgRuleSize': 'The size of the picture must not exceed 2MB',
  'Profile.networkError': 'Abnormal network response',
  'ManagementSiderMenu.backDesktopTest': 'Return to the home page',
  'FileMenuPopover.favorite': 'Favorite',
  'FileMenuPopover.move': 'Move',
  'FileMenuPopover.createCopy': 'Create Copy',
  'FileMenuPopover.download': 'Download',
  'FileMenuPopover.print': 'Print',
  'FileMenuPopover.saveVersion': 'Save Version',
  'FileMenuPopover.viewHistory': 'View History',
  'FileMenuPopover.viewCommentList': 'View Comments',
  'FileMenuPopover.help': 'Help',
  'FileMenuPopover.docGuide': 'Document Guide',
  'FileMenuPopover.docShortcut': 'Document Shortcuts',
  'FileMenuPopover.mosheetGuide': 'Sheet Guide',
  'FileMenuPopover.mosheetShortcut': 'Sheet Shortcuts',
  'FileMenuPopover.delete': 'Delete',
  'FileMenuPopover.downImage': 'Image',
  'FileMenuPopover.downWord': 'Word',
  'FileMenuPopover.downPDF': 'PDF',
  'FileMenuPopover.downMarkdown': 'Markdown',
  'FileMenuPopover.downWPS': 'WPS',
  'FileMenuPopover.downImagePDF': 'Image PDF',
  'FileMenuPopover.downExcel': 'Excel',
  'FileMenuPopover.downZip': 'ZIP',
  'FileMenuPopover.downPPTX': 'PPTX',
  'FileMenuPopover.convertToMoSheet': 'Convert to ShiMoSheet',
  'FileMenuPopover.downToExcel': 'Download as Excel',
  'FileMenuPopover.tableHelp': 'Table Help Center',
  'FileMenuPopover.addComment': 'Add Comment',
  'FileMenuPopover.viewComment': 'View Comment',
  'FileMenuPopover.formHelp': 'Form Help Center',
  'FileMenuPopover.noPermissionTip': 'No permission to operate, please contact the file manager',

  'UploadBoard.uploadSuccessTitle': 'Upload completed',
  'UploadBoard.uploadingTitle': 'uploading',
  'UploadBoard.uploadFailTitle': 'Error occurred',
  'UploadBoard.uploadCancelTitle': 'items have been cancelled',
  'UploadBoard.uploadConfirmCancelTitle': 'Are you sure to cancel the upload?',
  'UploadBoard.uploadConfirmCancelContent':
    'Closing the panel will cancel the file being uploaded, and files that have been successfully uploaded will not be affected. Are you sure you want to cancel the upload?',
  'UploadBoard.uploadConfirmCancelOkText': 'Cancel Upload',
  'UploadBoard.uploadConfirmCancelCancelText': 'Continue uploading',
  'UploadBoard.uploadFailTipTitleText':
    'Partial file upload failed, please re upload or copy the following error message and submit it to customer service for processing:',
  'UploadBoard.uploadFailMessageText': 'error message',
  'UploadBoard.uploadCopyFailMessageText': 'Copy error messages',
  'UploadBoard.uploadCopySuccessText': 'Replicating Success',
  'UploadBoard.uploadCheckFailMessageText': 'View error messages',
  'UploadBoard.uploadRetryText': 'retry',
  'UploadBoard.uploadOpenFolderText': 'Open the folder where you are located',
  'UploadBoard.uploadStatusTipCancelText': 'Cancelled',
  'UploadBoard.uploadExpTipRetractText': 'Retract',
  'UploadBoard.uploadExpTipExpandText': 'Expand',
  'UploadBoard.uploadExpTipRetractErrorMessageText': 'Hide error messages',
  'UploadBoard.failTipNum': '{number}errors',
  'UploadBoard.failTipTitle':
    'Unknown error detected, please upload or copy the error information separately for the following {number} files and submit them to customer service for processing:',
  'UseFileUpload.networkError': 'Network anomaly',
  'UseFileUpload.noSpaceTitle': 'Insufficient space and memory',
  'UseFileUpload.noSpaceContent':
    'Your space memory is insufficient. Please contact customer service or sales to purchase additional space capacity;',
  'UseFileUpload.noSpaceOkText': 'Sure',

  // 企业管理
  'Management.backDesktop': 'Return to the main site',
  'Management.enterpriseManagementSystem': 'Enterprise management system',
  'Management.companyName': 'Company name',
  'Management.workingDays': 'Working days',
  'Management.performance': 'Performance board',
  'Management.board': 'Performance board',
  'Management.memberList': 'Member list',
  'Management.auditLog': 'Audit log',
  'Management.kitValuePack': 'Kit Value Pack',
  'Management.onlineSeatWhiteList': 'Online seat whitelist',
  'Management.settings': 'Settings',
  'FilePathPicker.createTitle': 'Select directory',
  'FilePathPicker.moveTitle': 'Select Move Location',
  'FilePathPicker.fileName': 'Filename',
  'FilePathPicker.createIn': 'Create In',
  'FilePathPicker.moveTo': 'Move To',
  'FilePathPicker.move': 'Move',
  'FilePathPicker.copy': 'Copy',
  'FilePathPicker.selectPlaceholder': 'Please Select',
  'FilePathPicker.createFolder': 'New Folder',
  'FilePathPicker.moveFailed': 'Move failed',
  'FilePathPicker.noMoveToTargetLocationTip': 'Move failed, no permission to move to the target location',
  'FilePathPicker.noMoveFilePermissionTip': 'No permission to move the file, only the file manager can move',
  'FilePathPicker.noSupportTip': 'Current location does not support creating folders',
  'FilePathPicker.files': 'files',
  'FilePathPicker.folders': 'folders',
  'FilePathPicker.noPermissionTip':
    'The current folder has no editing permissions and cannot create new {type}. Please contact the folder manager.',
  'FilePathPicker.createPlaceHolder': 'Please name the folder to be created in "{folderName}"',
  'FilePathPicker.createFolderSuccess': 'New folder created successfully',
  'FilePathPicker.createFolderFailed': 'Failed to create new folder',
  'FilePathPicker.cancel': 'Cancel',
  'FilePathPicker.confirm': 'Confirm',
  'FilePathPicker.createCopy': 'Create Copy',
  'FilePathPicker.quickEntry': 'Quick Access',
  'FilePathPicker.desktopSpace': 'Desktop & Team Spaces',
  'FilePathPicker.recent': 'Recent',
  'FilePathPicker.shared': 'Shared with Me',
  'FilePathPicker.favorites': 'My Favorites',
  'FilePathPicker.desktop': 'My Desktop',
  'FilePathPicker.space': 'Team Space',
  'FilePathPicker.createSuccess': 'Created successfully, copy saved to 「{folderName}」',
  'FilePathPicker.moveSuccess': 'Moved successfully',
  'FilePathPicker.emptyUsedTitle': 'No files have been moved yet',
  'FilePathPicker.emptyUsedSubTitle': 'You can see the destination folder of the recently moved files here',
  'FilePathPicker.emptySharedTitle': 'No shared folders yet',
  'FilePathPicker.emptySharedSubTitle': 'You can see the list of folders added as collaborators here',
  'FilePathPicker.emptyFavoritesTitle': 'No favorite folders yet',
  'FilePathPicker.emptyFavoritesSubTitle': 'You can see the list of your favorite folders here',
  'FilePathPicker.emptySpaceTitle': 'No team spaces yet',
  'FilePathPicker.emptySpaceSubTitle': 'You can see the list of team spaces you can edit here',
  'FilePathPicker.noSearchResult': 'No search results',
  'FilePathPicker.searchPlaceholder': 'Please enter a keyword',

  'TemplateReview.useTemp': 'Use this template',
  'TemplateReview.back': 'Return',
  'TabContent.preview': 'Preview',
  'TabContent.use': 'Use',
  'TabContent.testFormSubTitle': 'Verify answers/set scores',
  'TabContent.tableFormSubTitle': 'Batch fill in feedback',
  'TabContent.formSubTitle': 'Information collection/questionnaire survey',
  'TabContent.pptSubTitle': 'Work Report/Business Speech',
  'TabContent.tableSubTitle': 'Task management/information entry',
  'TabContent.sheetSubTitle': 'Work Report/Business Speech',
  'TabContent.docxSubTitle': 'Contract Notice/Standard Secretary',
  'TabContent.docSubTitle': 'Quick Secretary/Text Editing',
  'TabContent.empty': 'Blank ',
  //分享协作
  'ShareCollaboration.title': 'Sharing and Collaboration',
  'ShareCollaboration.copySuccess': 'The link has been copied.',
  'ShareCollaboration.copyFail': 'Copy Failed',
  'ShareCollaboration.back': 'Back',
  'ShareCollaboration.add': 'Add',
  'ShareCollaboration.coauthor': 'Co-author',
  'ShareCollaboration.admin': 'Administrator',
  'ShareCollaboration.noCollaborator': 'Not yet.',
  'ShareCollaboration.linkShare': 'Link Sharing',
  'ShareCollaboration.shareMethod': 'Sharing Method',
  'ShareCollaboration.qrCodeShare': 'Scan the code to share',
  'ShareCollaboration.copyLink': 'Copy Link',
  'ShareCollaboration.accessPassword': 'Access Password',
  'ShareCollaboration.setPermission': 'Set Permissions',
  'ShareCollaboration.linkReadOnly': 'People on the internet with the link can only read',
  'ShareCollaboration.linkInCompany': 'People within the company with the link',
  'ShareCollaboration.readOnly': 'Read Only',
  'ShareCollaboration.comment': 'Can Comment',
  'ShareCollaboration.commentAndEdit': 'Can Comment and Edit',
  'ShareCollaboration.linkInInternet': 'People on the internet with the link',
  'ShareCollaboration.linkInCompanyWithPassword': 'People within the company with the link and password',
  'ShareCollaboration.linkInternetWithPassword': 'People on the internet who obtain both the link and the password',
  'ShareCollaboration.day': 'Day(s)',
  'ShareCollaboration.searchAddCollab': 'Click here to search and add collaborators',
  'ShareCollaboration.open': 'Enabled',
  'ShareCollaboration.close': 'Not enabled: Collaborators and administrators still have access',
  'ShareCollaboration.linkWithPassword': 'and Password',
  'ShareCollaboration.linkPassword': 'Link Password',
  'ShareCollaboration.changePassword': 'Change Password',
  'ShareCollaboration.needPassword': 'Requires password to access',
  'ShareCollaboration.linkExpiration': 'Link Expiration',
  'ShareCollaboration.switchOff': 'Switch Off: The link will remain active indefinitely',
  'ShareCollaboration.switchOn': 'Switch On: After the set expiration date, the link will become invalid',
  'ShareCollaboration.expirationClose': 'Expiration is disabled, the link is permanent',
  'ShareCollaboration.remaining': 'Remaining',
  'ShareCollaboration.inheritPermission': 'Inherit Permissions',
  'ShareCollaboration.forbidAccess': 'Access Forbidden',
  'ShareCollaboration.removePermission': 'Remove Permissions',
  'ShareCollaboration.modifySuccess': 'Modification Successful',
  'ShareCollaboration.deleteSuccess': 'Deletion Successful',
  'ShareCollaboration.addCoauthor': 'Add Co-author',
  'ShareCollaboration.onlyManagerCanAddCoauthor': 'Only administrators can add co-authors',
  'ShareCollaboration.onlyManagerCanAddManager': 'Only administrators can add administrators',
  'ShareCollaboration.parentCoauthor': 'Parent Folder Collaborators',
  'ShareCollaboration.collapse': 'Collapse',
  'ShareCollaboration.expand': 'Expand',
  'ShareCollaboration.addManager': 'Add Administrator',
  'ShareCollaboration.removeManager': 'Remove Administrator Rights',
  'ShareCollaboration.removeManagerSuccess': 'Department Administrator Rights Removed Successfully',
  'ShareCollaboration.removeManagerSuccess2': 'User Administrator Rights Removed Successfully',
  'ShareCollaboration.addSuccess': 'Added Successfully',
  'ShareCollaboration.removeSuccess': 'Administrator Rights Removed Successfully',
  'ShareCollaboration.setManager': 'Set as Administrator',
  'ShareCollaboration.addPermission': 'Add Permission',
  'ShareCollaboration.deleteDepartmentSuccess': 'Department Deleted Successfully',
  'ShareCollaboration.deleteUserSuccess': 'User Deleted Successfully',
  'ShareCollaboration.operationSuccess': 'Operation Successful',
  'ShareCollaboration.recent': 'Recent contact',
  'ShareCollaboration.organization': 'Organization Structure',
  'ShareCollaboration.clickHereToSearchAndAdd': 'Click here to search and add',
  'ShareCollaboration.searchResult': 'Search Results',
  'ShareCollaboration.sendNotificationToTheOther': 'Send notification when adding collaborators or administrators',
};
