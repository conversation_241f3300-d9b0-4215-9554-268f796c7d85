import type { AxiosError } from 'axios';
import axios from 'axios';

const CommonApi = axios.create({ baseURL: '/api/v1' });

CommonApi.interceptors.response.use(undefined, async (error: AxiosError & { __fromCache?: any }) => {
  const { response, __fromCache } = error;
  // 如果是我们主动 reject 的缓存命中，直接返回缓存内容作为模拟响应
  if (__fromCache) {
    return Promise.resolve({ data: __fromCache, config: error.config, status: 200 });
  }

  if (response) {
    return Promise.reject(response);
  }
  return Promise.reject(error);
});

CommonApi.interceptors.request.use((config) => {
  const requestUrl = `${config.baseURL}${config.url}`;
  const prefetchPromise = window.__DRIVE_PREFETCH__[requestUrl];
  if (prefetchPromise) {
    return prefetchPromise.then(
      (data) => {
        delete window.__DRIVE_PREFETCH__[requestUrl];
        return Promise.reject({
          __fromCache: data,
          config,
          data: requestUrl,
        });
      },
      () => {
        // 如果数据预取的时候失败了，这里就不做任何处理，这样这个请求就会正常发送出去
        return config;
      },
    );
  }
  return config;
});

export { CommonApi };
