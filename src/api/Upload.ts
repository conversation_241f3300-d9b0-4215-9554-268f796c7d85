import { CommonApi } from './Request';
export interface UploadFileProp {
  fileName: string;
  file: File;
  size: number;
  parentGuid: string;
  fileType: string;
}

export interface UploadrequestPreProp {
  guid: string;
  selfCallback: any;
  uploadUrl: any;
}

export interface ResponseData<T = any> {
  status: number;
  data: T;
  statusText: string;
}

export async function uploadFileApi(prop: UploadFileProp): Promise<any> {
  const formData = new FormData();
  formData.append('file', prop.file);
  formData.append('encodedFileName', prop.fileName);

  return await CommonApi.post('/files/upload/postPolicy', formData, {
    headers: {
      'x-file-parent-guid': prop.parentGuid,
      'x-file-size': prop.size,
      'X-File-Type': prop.fileType,
    },
  });
}

export async function uploadPostForThirdPlant(url: string, file: File, formData: any): Promise<ResponseData> {
  // 处理三方文件上传逻辑
  const formData2 = new FormData();
  const formDataObj = formData;
  for (const key in formDataObj) {
    if (formDataObj.hasOwnProperty(key)) {
      formData2.append(key, formDataObj[key]);
    }
  }
  formData2.append('file', file);

  return await CommonApi.post(url, formData2, {});
}

export async function uploadPostPolicyCallback(selfCallbackData: any): Promise<ResponseData> {
  return await CommonApi.post('/files/upload/postPolicy/callback/s3', {
    ...selfCallbackData,
  });
}
