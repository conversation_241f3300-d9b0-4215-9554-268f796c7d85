import { CommonApi } from './Request';

/**
 * 创建新文件
 *
 * @param fileType - 要创建的文件类型，可选值:
 *   - "folder": 文件夹
 *   - "newdoc": 轻文档
 *   - "modoc": 传统文档
 *   - "mosheet": 专业表格
 *   - "table": 应用表格
 *   - "presentation": 专业幻灯片
 *   - "form": 表单
 * @returns
 */
export function createFile(fileType: string) {
  return CommonApi.get(`/files/create/${fileType}`);
}

/**
 * 收藏
 * @param fileGuid - 文件 id
 */
export function star(fileGuid: string) {
  return CommonApi.put(`/files/${fileGuid}/star`);
}

/**
 * 取消收藏
 * @param fileGuid - 文件 id
 */
export function cancelStar(fileGuid: string) {
  return CommonApi.delete(`/files/${fileGuid}/star`);
}

/**
 * 查看文件详情
 * @param fileGuid - 文件 id
 */
export function fileDetail(fileGuid: string | undefined) {
  return CommonApi.get(`/files/${fileGuid}`);
}

/**
 * 重命名文件
 * @param fileGuid - 文件 id
 * @param name - 文件标题
 */
export function rename(fileGuid: string | undefined, name: string) {
  return CommonApi.patch(`/files/${fileGuid}`, { name });
}

interface FilesQuery {
  children?: boolean; // 查看子文件 桌面不可用
  excerpt?: boolean;
  fileType?: string; // shared 查询用到
  folder?: string; // 文件夹 guid findChildren 查询用到
  limit?: number; // 限制数量
  lastId?: string;
  lastTimestamp?: number; // 最后一个文件的时间戳
  tagId?: string; // 标签查询用到
  tagType?: string;
  page?: number; // 页码
  pageSize?: number; // 每页数量
  /** used: 最近文件 shared: 共享给我 null: 我的桌面  */
  type?: 'used' | 'shared';
  orderBy?: 'updatedAt' | 'createdAt';
  /**  open:最近打开 edit:最近编辑 */
  lastAction?: 'open' | 'edit';
}

/**
 * 文件列表 query
 * @param query - 查询参数
 */
export function files(query: FilesQuery) {
  return CommonApi.get('/files', { params: query });
}

/**
 * 获取文档祖先
 * @param fileGuid - 文件 id
 */
export function getAncestors(fileGuid: string | undefined) {
  return CommonApi.get(`/files/${fileGuid}/ancestors`);
}

/**
 * 上传文件第一步
 * @param formData - 表单数据
 * @param headers - 请求头
 */
export function uploadPostPolicy(
  formData: FormData,
  headers: Record<string, string>,
  controller: AbortController | null,
) {
  return CommonApi.post(`/files/upload/postPolicy`, formData, { headers, signal: controller?.signal });
}

/**
 * 上传文件第二步
 * @param url - 上传地址
 * @param formData - 表单数据
 */
export function uploadMinio(
  url: string,
  formData: FormData,
  controller: AbortController | null,
  onProgress: (progress: number) => void,
) {
  return CommonApi.post(url, formData, {
    signal: controller?.signal,
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(percentCompleted);
    },
  });
}

/**
 * 上传文件第三步
 * @param data - 数据对象
 * @param data.bucket - 存储桶名称
 * @param data.fname - 文件名
 * @param data.size - 文件大小
 * @param data.key - 文件键值
 * @param data.token - 认证令牌
 */
export function uploadCallback(
  data: { bucket: string; fname: string; key: string; size: number; token: string },
  controller: AbortController | null,
) {
  return CommonApi.post(`/files/upload/postPolicy/callback/s3`, data, { signal: controller?.signal });
}

/**
 * 获取空间内存
 */
export function getFileQuota() {
  return CommonApi.get(`/quota`);
}

/**
 * 用户打开文件动作
 * @param fileGuid - 文件 guid
 */
export function userAction(
  fileGuid: string,
  data?: {
    /** 是否预览 */
    isPreview?: 0 | 1;
    /** 是否打开 */
    trackOpen?: 0 | 1;
  },
) {
  return CommonApi.post(`/files/${fileGuid}/user_action`, data);
}

/**
 * 导出表格tale文件
 * @param fileGuid - 文件 guid
 */
export function exportTable(fileGuid: string) {
  return CommonApi.post(`/files/export/table/${fileGuid}`);
}

interface Exportdata {
  type: string; // 'pdf' | 'docx' | 'jpg' | 'md' | 'xlsx' | 'wps' | 'pptx' | 'jpeg' | 'xmind';
  width?: number; // 导出图片的宽度 Default: 884
}

/**
 * 导出文件
 * @param fileGuid - 文件 guid
 * @param data - 导出数据
 */
export function exportFile(fileGuid: string, data: Exportdata) {
  return CommonApi.post(`/files/export/${fileGuid}`, data);
}

/**
 * 获取导出进度
 * @param data - 导出数据
 */
export function exportProgress(data: { taskId: string }) {
  return CommonApi.post(`/files/export/progress`, data);
}

/**
 * 删除文件
 * @param fileGuid - 文件 guid
 */
export function deleteFile(fileGuid: string) {
  return CommonApi.delete(`/files/${fileGuid}`);
}

/** 获取回收站文件列表 */
export function getTrashList() {
  return CommonApi.get('/trashes');
}

/** 彻底删除回收站文件 */
export function deleteTrash(guid: string) {
  return CommonApi.delete(`/trashes/${guid}`);
}

/** 清空回收站文件列表 */
export function deleteTrashes() {
  return CommonApi.delete('/trashes');
}

/** 恢复回收站文件 */
export function recoveryFile(guid: string) {
  return CommonApi.patch(`/trashes/${guid}`);
}

/** 删除最近文件(清空此条记录) */
export function deleteRecentFile(guid?: string) {
  return CommonApi.delete(`/files/recents/${guid}`);
}

/** 批量删除文件 */
export function deleteBulkFile(data: { fileGuids: string[] }) {
  return CommonApi.post('/files/delete_batch', data);
}

/** 获取我的收藏列表 */
export function getStarredFileList(params: { orderBy: 'updatedAt' | 'createdAt' }) {
  return CommonApi.get('/files/starred', { params });
}

/** 文档收藏 */
export function putStar(guid: string) {
  return CommonApi.put(`/files/${guid}/star`);
}

export function getDownloadUrl(guid: string) {
  return new Promise<void>((resolve, reject) => {
    CommonApi.get(`/files/${guid}/download`)
      .then((res) => {
        if (res.status === 200) {
          const {
            request: { responseURL },
          } = res;
          resolve();
          window.open(responseURL);
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
}

interface SearchFileQuery {
  keyword: string;
  size: number;
  type?: 'folder';
}

/** 搜索文件 */
export function searchFile(data: SearchFileQuery) {
  return CommonApi.post('/search/files', data);
}

/**
 * 创建副本
 * @param fileGuid - 文件 guid
 * @param data - 创建副本参数
 * @returns 创建副本的请求
 */
export function duplicate(fileGuid: string, data: Record<string, any>) {
  return CommonApi.post(`/files/${fileGuid}/action/duplicate`, data);
}

interface MoveFileEntry {
  /** 源文件夹 guid，如果在文件详情里面，则不传，其他情况需要传 */
  from?: string;
  /** 目标文件夹 guid */
  to: string;
  /** 文件 guid */
  fileGuid: string;
}

interface MoveFileData {
  entries: MoveFileEntry[];
}

/**
 * 移动文件
 * @param data - 移动文件参数
 */
export function move(data: MoveFileData) {
  return CommonApi.post(`/files/move_batch`, data);
}

/**
 * 获取最近位置
 */
export function recentLocation() {
  return CommonApi.get(`/files/last_locations`);
}
