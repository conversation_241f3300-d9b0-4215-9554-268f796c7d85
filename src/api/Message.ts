import { CommonApi } from './Request';

type IParams = {
  status?: 'unread' | 'all';
  action?: 'peek';
  limit?: number;
};

/** 消息通知列表 */
export async function getNotifications(params: IParams) {
  return CommonApi.get('/notifications', { params });
}

/** 全部标记为已读 */
export function readAll() {
  return CommonApi.post('/notifications/action/read_all');
}

/** 单个标记为已读 */
export function readAlon(id: string) {
  return CommonApi.patch(`/notifications/${id}`, {
    status: 'read',
  });
}

/** 未查看消息计数（某个用户在某个时间点以后未查看的消息计数） */
export async function getUnpeekedCount() {
  return CommonApi.get('/notifications/unpeeked_count');
}
