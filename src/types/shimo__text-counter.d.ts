declare module '@shimo/text-counter' {
  /**
   * 计算文本中的字符数。
   * @param text 要计算字符数的文本。
   * @returns 字符数量。
   */
  export function countChars(text: string): number;

  /**
   * 计算文本中的字符数，不包括空格。
   * @param text 要计算字符数的文本。
   * @returns 不含空格的字符数量。
   */
  export function countCharsWithoutSpaces(text: string): number;

  /**
   * 计算文本中的词数。
   * @param text 要计算词数的文本。
   * @returns 词数量。
   */
  export function countWords(text: string): number;
}
