import '@/utils/bootstrap';

import { useEffect } from 'react';

import { useEditorStore } from '@/store/Editor';
import { isFillForm } from '@/utils/url';

export default function Page() {
  const fileType = useEditorStore((state) => state.fileType);

  useEffect(() => {
    if (fileType) {
      // 根据文件类型动态导入对应的初始化脚本
      switch (fileType) {
        case 'newdoc':
          import('./SDK/Doc');
          break;
        case 'modoc':
          import('./SDK/Docx');
          break;
        case 'mosheet':
          import('./SDK/Sheet');
          break;
        case 'table':
          import('./SDK/Table');
          break;
        case 'presentation':
          import('./SDK/Presentation');
          break;
        case 'form':
          import('./SDK/Form');
          break;
      }
    } else if (isFillForm()) {
      // 表单填写页可能不会有这个文件的权限，此时也需要加载表单sdk,让表单sdk来处理权限问题
      import('./SDK/Form');
    }
  }, [fileType]);

  return null;
}
