import type { MenuProps } from 'antd';
import { message } from 'antd';

import { deleteRecentFile } from '@/api/File';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import type { DownLoadFileProps } from '@/hooks/useFileTypeDownload';
import { fileStar, openFile } from '@/utils/file';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

export const items = ({
  record,
  renameCallback,
  reload,
  i18nText,
  setShareVisible,
  downloadDiffFile,
}: {
  record: any;
  renameCallback: ({ visible, params }: { visible: boolean; params: { guid: string; fileName: string } }) => void;
  reload: () => void;
  i18nText: { [key: string]: string };
  setShareVisible: (visible: boolean) => void;
  downloadDiffFile: ({ type, guid, fileName }: DownLoadFileProps) => void;
}): MenuItem[] => {
  return [
    {
      label: i18nText.newTabOpens,
      key: 'newTabOpens',
      onClick: () => openFile({ type: record.type, guid: record.guid, url: record.url, model: 'new' }),
    },
    // {
    //   label: '打开空间',
    //   key: '2',
    // },
    {
      label: i18nText.view,
      key: 'view',
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      label: record.starred ? i18nText.removeStr : i18nText.star,
      key: 'star',
      onClick: () =>
        fileStar({ guid: record.guid, status: record.starred })
          .then(({ type }) => {
            if (type === 'star') {
              message.success(i18nText.starSuccess);
            } else {
              message.success(i18nText.removeStarSuccess);
            }
            reload();
          })
          .catch(({ type }) => {
            if (type === 'star') {
              message.error(i18nText.starError);
            } else {
              message.error(i18nText.removeStarError);
            }
          }),
    },
    {
      label: i18nText.share + i18nText.collaboration,
      key: 'share',
      onClick: () => setShareVisible(true),
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.download,
      key: 'downloadOther',
      hidden: !record.name?.includes('.') || record.type === 'folder',
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.download,
      key: 'download',
      hidden: ['board', 'form', 'table', 'folder', 'shortcut', 'zip'].includes(record.type),
      children: [
        {
          label: i18nText.png,
          key: 'jpeg',
          hidden: !['mindmap', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'Xmind',
          key: 'xmind',
          hidden: !['mindmap'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'PPTX',
          key: 'pptx',
          hidden: !['presentation'].includes(record.type) || ['img'].includes(record.type), // presentation
        },
        {
          label: 'PDF',
          key: 'pdf',
          hidden: !['presentation', 'newdoc', 'modoc'].includes(record.type) || ['img'].includes(record.type), //presentation
        },
        {
          label: 'Excel',
          key: 'xlsx',
          hidden: !['mosheet'].includes(record.type) || ['img'].includes(record.type), // mosheet
        },
        {
          label: 'WPS',
          key: 'wps',
          hidden: !['modoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'Word',
          key: 'word',
          hidden: !['modoc', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'MarkDown',
          key: 'md',
          hidden: !['newdoc'].includes(record.type) || ['img'].includes(record.type), // newdoc
        },
      ],
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.reName,
      key: 'reName',
      onClick: () => renameCallback({ visible: true, params: { fileName: record.name, guid: record.guid } }),
    },
    {
      label: i18nText.moveTo,
      key: 'moveTo',
      disabled: true,
    },
    {
      label: i18nText.copyTo,
      key: 'copyTo',
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.delete,
      key: 'delete',
      danger: true,
      disabled: true,
      onClick: () => deleteConfirm({ i18nText, data: record['guid'], api: deleteRecentFile, callback: reload }),
    },
  ];
};
