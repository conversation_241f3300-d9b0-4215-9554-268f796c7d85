import type { MenuProps } from 'antd';
import { message } from 'antd';

import { deleteTrash, recoveryFile } from '@/api/File';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

export const items = ({
  record,
  reload,
  i18nText,
}: {
  record: any;
  reload: () => void;
  i18nText: { [key: string]: string };
}): MenuItem[] => {
  return [
    {
      label: i18nText.recover,
      key: 'recover',
      onClick: () => {
        recoveryFile(record.guid)
          .then((res) => {
            if (res.status === 200) {
              message.success(i18nText.resetFirst);
              reload();
            }
          })
          .catch(() => {
            message.error(i18nText.resetError);
          });
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.deleteCompletely,
      key: 'deleteCompletely',
      danger: true,
      onClick: () => deleteConfirm({ i18nText, data: record['guid'], api: deleteTrash, callback: reload }),
    },
  ];
};
