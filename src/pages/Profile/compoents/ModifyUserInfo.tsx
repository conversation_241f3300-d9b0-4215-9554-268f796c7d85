import '../index.less';

import { CloudUploadOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Modal } from 'antd';
import FormItem from 'antd/es/form/FormItem';
import { useEffect, useState } from 'react';

import { modfiyUserInfo, uploadAvatarToken } from '@/api/profile';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
interface ModifyUserInfoProps {
  visible: boolean;
  setVisible: (value: boolean) => void;
  me: any;
}
const ModifyUserInfo: React.FC<ModifyUserInfoProps> = ({ visible, setVisible, me }) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm(); // 使用Form.useForm创建表单实例
  const [avatarUrl, setAvatarUrl] = useState(me.avatar);
  const [newAvatarUrl, setNewAvatarUrl] = useState('');
  // 修改用户信息，（stroe中的数据更新，不用去调接口）
  const setMe = useMeStore((state) => state.setMe);
  const i18nText: any = {
    modifyInfo: $t('Profile.modifyInfo'),
    sure: $t('Space.sure'),
    modifyImg: $t('Profile.modifyImg'),
    uploadImg: $t('Profile.uploadImg'),
    nickName: $t('Profile.nickName'),
    enterNickname: $t('Profile.enterNickname'),
    nickNameRule: $t('Profile.nickNameRule'),
    modifySuccess: $t('Profile.modifySuccess'),
    modifyFailed: $t('Profile.modifyFailed'),
    getUploadToken: $t('Profile.getUploadToken'),
    uploadImgSuccess: $t('Profile.uploadImgSuccess'),
    uploadImgFailed: $t('Profile.uploadImgFailed'),
    uploadImgRuleType: $t('Profile.uploadImgRuleType'),
    uploadImgRuleSize: $t('Profile.uploadImgRuleSize'),
    networkError: $t('Profile.networkError'),
  };
  const handleOk = () => {
    form.validateFields().then((values) => {
      setConfirmLoading(true);
      modfiyUserInfo({ name: values.username, avatar: avatarUrl })
        .then(() => {
          message.success(i18nText.modifySuccess);
          setVisible(false);
          setConfirmLoading(false);
          setMe({
            ...me,
            name: values.username,
            avatar: newAvatarUrl || me.avatar,
          });
        })
        .catch(() => {
          message.error(i18nText.modifyFailed);
          setConfirmLoading(false);
        });
    });
  };
  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      message.error(i18nText.uploadImgRuleType);
      return;
    }
    if (file.size > 2 * 1024 * 1024) {
      message.error(i18nText.uploadImgRuleSize);
      return;
    }
    const formData = new FormData();
    formData.append('avatar', file);
    const payload = [
      {
        bucket: 'avatar',
        filename: file.name,
        fileSize: file.size,
      },
    ];
    uploadAvatarToken(payload)
      .then((resToken) => {
        const res = resToken.data.datas[0];
        const formData = new FormData();
        formData.append('accessToken', res.formData.accessToken);
        formData.append('download', res.formData.download);
        formData.append('file', file);
        // 向这个地址https://drv-dev.shimorelease.com/uploader/upload上传图片(接口返回数据)
        fetch(resToken.data.url, {
          method: 'POST',
          body: formData,
        })
          .then((response: any) => {
            if (!response.ok) {
              message.error(i18nText.networkError);
            }
            return response.json();
          })
          .then((data) => {
            if (data.code !== 0) {
              message.error(i18nText.uploadImgFailed);
              return;
            }
            message.success(i18nText.uploadImgSuccess);
            const avatarUrl = data.data?.images;
            setAvatarUrl(avatarUrl);
            setNewAvatarUrl(avatarUrl);
          })
          .catch(() => {
            message.error(i18nText.uploadImgFailed);
          });
      })
      .catch(() => {
        message.error(i18nText.getUploadToken);
      });
  };
  useEffect(() => {
    form.setFieldsValue({ username: me.name });
    setAvatarUrl(me.avatar);
  }, [visible]);

  return (
    <Modal
      centered
      confirmLoading={confirmLoading}
      footer={[
        <Button key="submit" color="default" loading={confirmLoading} variant="solid" onClick={handleOk}>
          {i18nText.sure}
        </Button>,
      ]}
      open={visible}
      title={i18nText.modifyInfo}
      width={400}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={handleOk}
    >
      <div className="modifyTitleDiv">
        <div className="modifyTitle">{i18nText.modifyImg}</div>
        <label htmlFor="avatar-upload">
          <input accept=".jpg,.jpeg,.png,.gif" id="avatar-upload" type="file" onChange={handleUpload} />
          <Button
            icon={<CloudUploadOutlined />}
            onClick={() => {
              document.getElementById('avatar-upload')?.click();
            }}
          >
            {i18nText.uploadImg}
          </Button>
        </label>
      </div>
      <div className="modifyAvatarDiv">
        <img alt="" src={avatarUrl} />
        <div className="modifyAvatarTips">{i18nText.uploadImgRuleType}</div>
        <div className="modifyAvatarTips">{i18nText.uploadImgRuleSize}</div>
      </div>
      <div className="inputLabel">{i18nText.nickName}</div>
      <Form form={form} layout="vertical">
        <FormItem name="username" rules={[{ required: true, message: i18nText.enterNickname }]}>
          <Input maxLength={20} />
        </FormItem>
      </Form>
    </Modal>
  );
};
export default ModifyUserInfo;
