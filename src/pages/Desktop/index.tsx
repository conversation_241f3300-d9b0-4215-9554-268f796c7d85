import {
  CheckOutlined,
  CheckSquareOutlined,
  DeleteOutlined,
  DeliveredProcedureOutlined,
  DownOutlined,
  MinusSquareOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Breadcrumb, Button, Card, Dropdown, message, Space, Table, Tooltip } from 'antd';
import type { Key, ReactNode } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { history, useParams } from 'umi';

import { deleteBulkFile, fileDetail, files, getAncestors } from '@/api/File';
import { getSpacePermission } from '@/api/space';
import { ReactComponent as DesktopSvg } from '@/assets/images/empty/desktop.svg';
import { ReactComponent as Downward } from '@/assets/images/svg/downward.svg';
import { ReactComponent as ShareCollaboration } from '@/assets/images/svg/shareCollaboration.svg';
import CollaborationShare from '@/components/Collaboration';
import { createMenu } from '@/components/ContextMenu';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { FileName } from '@/components/fileList/components/FileName';
import { NoData } from '@/components/fileList/components/NoData';
import RenameModal from '@/components/fileList/components/RenameModal';
import styles from '@/components/fileList/index.less';
import { useFileTypeDownload } from '@/hooks/useFileTypeDownload';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { fm } from '@/modules/Locale';
import { items as spaceItems } from '@/pages/Space/item';
import { useMeStore } from '@/store/Me';
import { useThemeStore } from '@/store/Theme';
import { formatFileSizeHuman, openFile, useFormatTime } from '@/utils/file';

import SettingsModal from '../Space/components/SettingsModal';
import { items } from './items';

type FilterType = 'updatedAt' | 'createdAt';

type DataType = {
  guid: string;
  updatedAt: number;
  createdAt: number;
  name: string;
  type: string;
  subType: string; // 最新type 融合上传的文件type
  [key: string]: any;
};

const Desktop = () => {
  const { isDark } = useThemeStore((state) => state.theme);

  const meId = useMeStore((state) => state.me.id);

  const { downloadDiffFile } = useFileTypeDownload();

  const i18nText = {
    newTabOpens: fm('File.newTabOpens'),
    edit: fm('File.edit'),
    star: fm('File.star'),
    starSuccess: fm('File.starSuccess'),
    starError: fm('File.starError'),
    removeStr: fm('File.removeStar'),
    removeStarSuccess: fm('File.removeSuccess'),
    removeStarError: fm('File.removeError'),
    share: fm('File.share'),
    collaboration: fm('File.collaboration'),
    download: fm('File.download'),
    reName: fm('File.reName'),
    png: fm('File.png'),
    moveTo: fm('File.moveTo'),
    copyTo: fm('File.copyTo'),
    delete: fm('File.delete'),
    title: `${fm('deleteConfirm.title')}?`,
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.deleteSuccess'),
    error: fm('File.deleteError'),
    deleteTips: fm('File.deleteTips', { max: 50 }),
    recentlyOpened: fm('File.recentlyOpened'),
    recentlyEdit: fm('File.recentlyEdit'),
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
    noDesktopDescription: fm('File.noDesktopDescription'),
    noDesktopTitle: fm('File.noDesktopTitle'),
    rightClickShare: fm('Space.rightClickShare'),
    rightClickCollaboration: fm('Space.rightClickCollaboration'),
    rightClickSetting: fm('Space.rightClickSetting'),
    rightClickDelete: fm('Space.rightClickDelete'),
    teamspaceConfirmDeletion: fm('Space.teamspaceConfirmDeletion'),
    teamspaceDeleteSuccess: fm('Space.teamspaceDeleteSuccess'),
    teamspaceDeleteTipText: fm('Space.teamspaceDeleteTipText'),
  };
  const { guid } = useParams<{ guid: string }>();

  const [data, setData] = useState<DataType[]>([]);

  const [noData, setNoData] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [renameVisible, setRenameVisible] = useState(false);

  const [params, setParams] = useState<{ FileName?: string; guid?: string }>({ FileName: '', guid: '' });

  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});

  const [filterType, setFilterType] = useState<FilterType | string>('updatedAt');

  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend'>('descend'); // 降序

  const [lastMenuInfo, setLastFileInfo] = useState({});

  const [ancestors, setAncestors] = useState([]);

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const [opneSetting, setOpneSetting] = useState<boolean>(false);

  const [spaceData, setSpaceData] = useState({});
  const [spaceFileName, setSpaceFileName] = useState('');
  const [shareVisible, setShareVisible] = useState(false);
  const [shareData, setShareData] = useState<any>();
  const [typeSetting, setTypeSetting] = useState('');

  const checkedTotal = fm('File.checkedTotal', {
    checked: selectedRowKeys.length,
  });

  const { formatTime } = useFormatTime();

  const reload = () => {
    setLoading(true);
    files({ folder: guid })
      .then((res: any) => {
        if (res.status === 200) {
          const { list = [] } = res.data;
          setData(list || []);
          setNoData(list ? list.length === 0 : true);
        }
      })
      .catch((err) => {
        console.log(err);
        // message.error(err.data?.msg);
      })
      .finally(() => {
        setLoading(false);
        setSelectedRowKeys([]);
      });
  };

  /** 获取最后一个目录详情 */
  const getLastFileInfo = () => {
    if (!guid) return;
    fileDetail(guid).then((res) => {
      if (res.status === 200) {
        const { name, type, guid, url, starred } = res.data;
        setLastFileInfo({
          name,
          type,
          guid,
          url,
          starred,
        });
      }
    });
  };
  const fetchSpacePerm = () => {
    getSpacePermission(guid).then((res) => {
      if (res.status === 200) {
        const data = res.data;
        setSpaceData({ ...data });
      }
    });
  };

  const axiosFileMenu = () => {
    getAncestors(guid).then((res) => {
      if (res.status === 200) {
        const { ancestors } = res.data;
        setAncestors(ancestors);
      }
    });
  };

  const renameCallback = ({
    visible,
    params,
    refresh,
  }: {
    visible: boolean;
    params?: { fileName?: string; guid?: string };
    refresh?: boolean;
  }) => {
    setRenameVisible(visible);
    setParams(params || {});
    if (refresh) {
      reload();
      getLastFileInfo();
      axiosFileMenu(); // 重命名后也要重新获取文件夹目录
    }
  };
  const computedTabItems = useMemo(() => {
    if (!ancestors.length) {
      return [
        {
          title: (
            <span onClick={() => history.push({ pathname: '/desktop' })}>{fm('SiderMenu.siderMenuDesktopText')}</span>
          ),
        },
      ];
    }
    const isEmptyObject = (obj: Record<string, any>): boolean => {
      return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
    };
    const dataList: { title: ReactNode }[] = ancestors.map((item: any, index: number) => {
      if (item.isSpace && !item.isFolder) {
        setSpaceFileName(item.name);
        return {
          title:
            !isEmptyObject(spaceData) &&
            (ancestors.length === 1 ? (
              <Tooltip placement="bottom" title={spaceFileName}>
                <Dropdown
                  menu={{
                    items: spaceItems({
                      data: spaceData,
                      i18nText,
                      setShareVisible,
                      reload: () => {
                        reload();
                      },
                      setOpneSetting,
                      typeDelete: 'desktop',
                      setTypeSetting,
                    }),
                  }}
                  overlayStyle={{ minWidth: '130px' }}
                  placement="bottom"
                  trigger={['click']}
                  onOpenChange={(open) => {
                    if (open) {
                      setShareData(spaceData);
                    }
                  }}
                >
                  <span className="breadcrumb-ellipsis desktopCursor">
                    {spaceFileName} <DownOutlined />
                  </span>
                </Dropdown>
              </Tooltip>
            ) : (
              <Tooltip placement="bottom" title={item.name}>
                <span
                  className="breadcrumb-ellipsis desktopCursor"
                  onClick={() => history.push({ pathname: `/space/${item.guid}` })}
                >
                  {item.name}
                </span>
              </Tooltip>
            )),
        };
      }
      if (item.guid === 'Desktop') {
        return {
          title: (
            <Tooltip placement="bottom" title={item.name}>
              <span className="desktopCursor" onClick={() => history.push({ pathname: '/desktop' })}>
                {item.name}
              </span>
            </Tooltip>
          ),
        };
      } else {
        if (index === ancestors.length - 1) {
          return {
            title: (
              <Tooltip placement="bottom" title={item.name}>
                <Dropdown
                  className="desktopCursor"
                  menu={{
                    items: items({
                      record: { ...lastMenuInfo },
                      i18nText,
                      selectedRowKeys: [],
                      renameCallback,
                      setShareVisible,
                      setTypeSetting,
                      reload: () => {
                        if (ancestors.length > 1) {
                          history.push({ pathname: `/folder/${ancestors[ancestors.length - 2]['guid']}` });
                        }
                      },
                      meId,
                      hidden: true,
                      downloadDiffFile,
                    }).filter((item) => !item.hidden),
                  }}
                  placement="bottom"
                  trigger={['click']}
                >
                  <span
                    className="breadcrumb-ellipsis"
                    onClick={() => history.push({ pathname: `/folder/${item.guid}` })}
                  >
                    {item.name}
                    <DownOutlined />
                  </span>
                </Dropdown>
              </Tooltip>
            ),
          };
        } else {
          return {
            title: (
              <Tooltip placement="bottom" title={item.name}>
                <span
                  className="breadcrumb-ellipsis desktopCursor"
                  onClick={() => history.push({ pathname: `/folder/${item.guid}` })}
                >
                  {item.name}
                </span>
              </Tooltip>
            ),
          };
        }
      }
    });
    return dataList;
  }, [spaceData, ancestors, lastMenuInfo, spaceFileName]);
  const handleContextMenu = (e: React.MouseEvent, record: any) => {
    e.preventDefault();
    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({
        record: record,
        i18nText,
        setShareVisible,
        selectedRowKeys,
        renameCallback,
        reload,
        meId,
        setTypeSetting,
        downloadDiffFile,
      }),
      theme: isDark ? 'dark' : 'light',
    });
    setShareData(record);
  };

  const columns: TableColumnsType = [
    {
      title: fm('File.fileName'),
      dataIndex: 'name',
      minWidth: 160,
      render: (value: string, record: any) => {
        return <FileName name={value} record={record} />;
      },
    },
    {
      title: fm('File.createName'),
      dataIndex: ['user', 'name'],
      width: 160,
      ellipsis: true,
    },
    {
      title: () => {
        return (
          <>
            <Dropdown
              menu={{
                items: [
                  {
                    label: fm('File.updateTime'),
                    key: 'updatedAt',
                    icon: filterType === 'updatedAt' ? <CheckOutlined /> : <span />,
                  },
                  {
                    label: fm('File.createdAt'),
                    key: 'createdAt',
                    icon: filterType === 'createdAt' ? <CheckOutlined /> : <span />,
                  },
                ],
                onClick: ({ key }: { key: any }) => {
                  setFilterType(key);
                },
              }}
              overlayClassName="updateTimeFilter"
              trigger={['click']}
            >
              <Button size="small" style={{ fontSize: '12px' }} type="text">
                <span>{filterType === 'updatedAt' ? fm('File.updateTime') : fm('File.createdAt')}</span>
              </Button>
            </Dropdown>
            <Button
              icon={sortOrder === 'descend' ? <Downward /> : <Downward style={{ transform: 'rotate(180deg)' }} />}
              type="text"
              onClick={() => setSortOrder(sortOrder === 'ascend' ? 'descend' : 'ascend')}
            />
          </>
        );
      },
      dataIndex: filterType,
      width: 220,
      ellipsis: true,
      render: (value: number) => {
        return <>{formatTime(value * 1000)}</>;
      },
    },
    {
      title: fm('File.fileSize'),
      dataIndex: 'fileSize',
      render: (value: number) => formatFileSizeHuman(value),
      width: 120,
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: any, record: object) => {
        return (
          <MoreOutlined
            className="more"
            onClick={(event) => {
              event.stopPropagation();
              handleContextMenu(event, record);
            }}
          />
        );
      },
    },
  ];

  const sortedList = useMemo(() => {
    return [...data].sort((a, b) => {
      if (a.type === 'folder' && b.type !== 'folder') return -1;
      if (a.type !== 'folder' && b.type === 'folder') return 1;

      const valueA = a[filterType];
      const valueB = b[filterType];

      return sortOrder === 'descend' ? valueB - valueA : valueA - valueB;
    });
  }, [data, filterType, sortOrder]);
  const handleShareCollaboration = () => {
    setShareData({ guid: selectedRowKeys[0] });
    setShareVisible(true);
    setTypeSetting('');
  };

  useEffect(() => {
    if (guid) {
      reload();
      axiosFileMenu();
      fetchSpacePerm();
      getLastFileInfo();
    } else {
      reload();
    }
  }, [guid]);

  return (
    <Card
      className={styles['mainCardTable']}
      extra={
        <Space>
          {!selectedRowKeys.length || (
            <Space>
              <span className="checkedTotal">{checkedTotal}</span>
              {selectedRowKeys.length === data.length ? (
                <Button
                  icon={<MinusSquareOutlined />}
                  size="small"
                  type="text"
                  onClick={() => {
                    setSelectedRowKeys([]);
                  }}
                >
                  {fm('File.uncheck')}
                </Button>
              ) : (
                <Button
                  icon={<CheckSquareOutlined />}
                  size="small"
                  type="text"
                  onClick={() => setSelectedRowKeys(data.map(({ guid }) => guid))}
                >
                  {fm('File.allCheck')}
                </Button>
              )}
              {selectedRowKeys.length === 1 && (
                <Button icon={<ShareCollaboration />} size="small" type="text" onClick={handleShareCollaboration}>
                  {fm('ShareCollaboration.title')}
                </Button>
              )}

              <Button disabled icon={<DeliveredProcedureOutlined />} size="small" type="text">
                {fm('File.move')}
              </Button>
              <Button
                icon={<DeleteOutlined />}
                size="small"
                type="text"
                onClick={() => {
                  if (selectedRowKeys.length > 50) {
                    message.error(i18nText.deleteTips);
                    return;
                  }
                  deleteConfirm({
                    i18nText,
                    data: { fileGuids: selectedRowKeys },
                    api: deleteBulkFile,
                    callback: reload,
                  });
                }}
              >
                {fm('File.delete')}
              </Button>
              <Button disabled icon={<MoreOutlined />} size="small" type="text">
                {fm('File.more')}
              </Button>
            </Space>
          )}
        </Space>
      }
      title={guid ? <Breadcrumb items={computedTabItems} /> : fm('SiderMenu.siderMenuDesktopText')}
    >
      {!noData ? (
        <Table
          virtual
          className={tableClassName}
          columns={columns}
          dataSource={sortedList}
          loading={loading}
          pagination={false}
          rowKey={'guid'}
          rowSelection={{
            type: 'checkbox',
            columnWidth: 48,
            selectedRowKeys,
            onChange: (selectedRowKeys: Key[]) => {
              setSelectedRowKeys(selectedRowKeys);
            },
          }}
          scroll={{ y: tableMaxHeight, x: 760 }}
          onRow={(record: any) => {
            return {
              onClick: () => {
                if (selectedRowKeys.includes(record.guid)) {
                  // 存在
                  setSelectedRowKeys(selectedRowKeys.filter((guid) => guid !== record.guid));
                } else {
                  // 不存在
                  setSelectedRowKeys([...selectedRowKeys, record.guid]);
                }
              },
              onDoubleClick: (event) => {
                // @ts-ignore
                const isCheckbox = event.target?.closest('.ant-checkbox-wrapper');
                if (isCheckbox) return;
                openFile(record);
              },
              onContextMenu: (event) => {
                handleContextMenu(event, record);
              },
            };
          }}
        />
      ) : (
        <NoData description={i18nText.noDesktopDescription} img={<DesktopSvg />} title={i18nText.noDesktopTitle} />
      )}
      <RenameModal callback={renameCallback} params={params} visible={renameVisible} />
      <SettingsModal
        setShareVisible={setShareVisible}
        setTypeSetting={setTypeSetting}
        spaceData={spaceData}
        visible={opneSetting}
        onCancel={() => setOpneSetting(false)}
        onChangeInput={() => {
          axiosFileMenu();
        }}
        onSpaceUpdated={reload}
      />
      <CollaborationShare
        enterType={typeSetting}
        guid={shareData?.guid ?? ''}
        visible={shareVisible}
        onCancel={() => {
          setShareVisible(false);
        }}
      />
    </Card>
  );
};

export default Desktop;
