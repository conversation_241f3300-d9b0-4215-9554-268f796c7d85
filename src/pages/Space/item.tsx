import { type MenuProps } from 'antd';
import { history } from 'umi';

import { deleteSpace } from '@/api/space';
import { ReactComponent as Detele } from '@/assets/images/svg/detele.svg';
import { ReactComponent as SettingIcon } from '@/assets/images/svg/settingIcon.svg';
import { ReactComponent as ShareCollaboration } from '@/assets/images/svg/shareCollaboration.svg';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};
export const items = ({
  data,
  i18nText,
  reload,
  setOpneSetting,
  setShareVisible,
  typeDelete,
  setTypeSetting,
}: {
  data?: any;
  i18nText: { [key: string]: string };
  reload: () => void;
  setShareVisible: any;
  setOpneSetting?: any;
  typeDelete?: string;
  setTypeSetting?: (value: string) => void;
}): MenuItem[] => {
  return [
    {
      label: i18nText.rightClickShare + i18nText.rightClickCollaboration,
      key: 'collaboration',
      icon: <ShareCollaboration />,
      onClick: () => {
        setShareVisible(true);
        if (setTypeSetting) {
          setTypeSetting('');
        }
      },
    },

    {
      label: i18nText.rightClickSetting,
      key: 'setting',
      icon: <SettingIcon />,
      onClick: () => {
        setOpneSetting(true);
      },
      disabled: !data.permissionsAndReasons.canEdit.value,
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.rightClickDelete,
      key: 'delete',
      danger: true,
      icon: <Detele />,
      onClick: () =>
        deleteConfirm({
          i18nText,
          data: data['guid'],
          api: deleteSpace,
          callback: () => {
            if (typeDelete === 'desktop') {
              history.push({ pathname: '/space' });
            } else {
              reload();
            }
          },
        }),
      disabled: !data.permissionsAndReasons.canRemove.value,
    },
  ];
};
