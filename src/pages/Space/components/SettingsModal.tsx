import '../index.less';

import { Button, Form, Input, message, Modal, Tooltip } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';

import { editSpaceName } from '@/api/space';
import { ReactComponent as OwnerShip } from '@/assets/images/svg/ownerShip.svg';
import { ReactComponent as PlusSign } from '@/assets/images/svg/plusSign.svg';
import { useFormatMessage as $t } from '@/modules/Locale';
interface SettingsModalProps {
  visible: boolean;
  onCancel: () => void;
  spaceData: any;
  onSpaceUpdated: () => void;
  onChangeInput?: () => void;
  setShareVisible: (visible: boolean) => void;
  setTypeSetting?: any;
}
const SettingsModal: React.FC<SettingsModalProps> = ({
  visible,
  spaceData,
  onCancel,
  onSpaceUpdated,
  onChangeInput,
  setShareVisible,
  setTypeSetting,
}) => {
  const { name, permissionsAndReasons, team, onlyAdminsCanModifyCollaborators } = spaceData;
  const [inputValue, setInputValue] = useState('');
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  const i18nText: any = {
    infoWaring1: $t('Space.infoWaring1'),
    infowaring2: $t('Space.infoWaring2'),
    infoEmpty: $t('Space.infoEmpty'),
    infoEditSuccess: $t('Space.infoEditSuccess'),
    teamspaceSetting: $t('Space.teamspaceSetting'),
    teamSpaceSure: $t('Space.sure'),
    teamSpaceCancel: $t('Space.cancel'),
    teamSpaceName: $t('SiderMenu.siderMenuSpaceText'),
    enterSpaceName: $t('Space.enterSpaceName'),
    teamspaceOwnership: $t('Space.teamspaceOwnership'),
    teamspaceWhatOwnership: $t('Space.teamspaceWhatOwnership'),
    teamspaceBelongsto: $t('Space.teamspaceBelongsto'),
    teamspaceOwnershipExp: $t('Space.teamspaceOwnershipExp'),
  };
  const [form] = Form.useForm();
  const handleEdit = async () => {
    await form.validateFields();
    setLoadingSubmit(true);
    editSpaceName(spaceData.guid, {
      name: inputValue,
      onlyAdminsCanModifyCollaborators,
    }).then((res: any) => {
      if (res.status === 200) {
        message.success(i18nText.infoEditSuccess);
        onCancel();
        onSpaceUpdated();
        if (onChangeInput) {
          onChangeInput();
        }
      } else {
        message.error(res.message);
      }
      setLoadingSubmit(false);
    });
  };
  const inputHandleChange = (e: { target: { value: any } }) => {
    let value = e.target.value;
    value = _.trim(value);
    value = value.replace(/\s+/g, ' ');
    setInputValue(value);
  };
  useEffect(() => {
    setInputValue(name);
    form.setFieldsValue({ name });
  }, [name, form]);
  return (
    <Modal
      centered
      className="settingsModal"
      footer={[
        <Button key="back" color="default" variant="outlined" onClick={onCancel}>
          {i18nText.teamSpaceCancel}
        </Button>,
        <Button key="submit" color="default" loading={loadingSubmit} variant="solid" onClick={handleEdit}>
          {i18nText.teamSpaceSure}
        </Button>,
        <div
          key="enterCollaborator"
          className="enterCollaborator"
          onClick={() => {
            setShareVisible(true);
            setTypeSetting('setting');
          }}
        >
          <div className="svg">
            <PlusSign />
          </div>
          <div>添加协作者</div>
        </div>,
      ]}
      open={visible}
      title={i18nText.teamspaceSetting}
      width={400}
      onCancel={onCancel}
    >
      <div className="settingTitle">{i18nText.teamSpaceName}</div>
      <Form form={form} initialValues={{ name: inputValue }} layout="vertical">
        <Form.Item label="" name="name" rules={[{ required: true, message: i18nText.infoEmpty }]}>
          <Input
            disabled={!permissionsAndReasons?.canEdit?.value}
            maxLength={512}
            placeholder={i18nText.enterSpaceName}
            type="text"
            value={inputValue}
            onChange={inputHandleChange}
          />
        </Form.Item>
      </Form>
      <div className="line" />
      <div className="toolTipTitle">
        <span className="toolTipTitleLeft">{i18nText.teamspaceOwnership} </span>
        <Tooltip
          placement="top"
          title={<div className="toolTipText">{i18nText.teamspaceOwnershipExp}</div>}
          trigger="hover"
        >
          <OwnerShip />
        </Tooltip>
      </div>
      <div className="cardTitleItem">
        <span>{i18nText.teamspaceBelongsto} </span> {team?.name}
      </div>
    </Modal>
  );
};

export default SettingsModal;
