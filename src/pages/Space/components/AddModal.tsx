import { Button, Input, Modal } from 'antd';

import { handleChange } from '.';

interface SettingsModalProps {
  visible: boolean;
  i18nText: { [key: string]: string };
  setOpen: (value: boolean) => void;
  handleAdd: () => void;
  loadingSubmit: boolean;
  inputValue: string;
  setInputValue: (value: string) => void;
}
const AddModal: React.FC<SettingsModalProps> = ({
  visible,
  i18nText,
  setOpen,
  handleAdd,
  loadingSubmit,
  inputValue,
  setInputValue,
}) => {
  return (
    <Modal
      centered
      footer={[
        <Button key="submit" color="default" loading={loadingSubmit} variant="solid" onClick={handleAdd}>
          {i18nText.teamSpaceSure}
        </Button>,
        <Button
          key="back"
          color="default"
          variant="outlined"
          onClick={() => {
            setOpen(false);
          }}
        >
          {i18nText.teamSpaceCancel}
        </Button>,
      ]}
      open={visible}
      styles={{
        content: {
          padding: '30px 40px',
        },
        body: {
          padding: '8px 0 18px',
        },
      }}
      title={i18nText.createTeamSpace}
      width={546}
      onCancel={() => {
        setOpen(false);
      }}
      onOk={handleAdd}
    >
      <Input
        placeholder={i18nText.enterSpaceName}
        type="text"
        value={inputValue}
        onChange={(e) => handleChange(e, setInputValue, i18nText)}
      />
    </Modal>
  );
};
export default AddModal;
