export function createPropsComparer<T>(keys: (keyof T)[]) {
  return (prevProps: T, nextProps: T): boolean => {
    return keys.every((key) => {
      const prevValue = prevProps[key];
      const nextValue = nextProps[key];
      if (typeof prevValue === 'object' && prevValue !== null) {
        return JSON.stringify(prevValue) === JSON.stringify(nextValue);
      }
      return prevValue === nextValue;
    });
  };
}
