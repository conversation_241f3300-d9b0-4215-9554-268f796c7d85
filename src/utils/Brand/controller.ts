import { getBrandConfigReq } from './scripts';
import type { BrandContentType, BrandType } from './types';
import { BrandShowType } from './types';

const DEFAULT_CONTAINER_STYLE = {
  width: '200px',
  color: 'rgba(65, 70, 75, 0.6)',
};
export const getBrandConfig = async (): Promise<null | BrandType> => {
  if (!window.__RUNTIME_ENV__.ENABLE_BRAND) {
    // 未开启品牌功能，无需展示
    return null;
  }
  try {
    const { show, showType, custom, brandStr, image } = await getBrandConfigReq();
    if (!show) {
      return null;
    }
    const containerStyle = { ...DEFAULT_CONTAINER_STYLE, ...custom };
    const textContent: { type: BrandContentType; content: string } = {
      type: 'text',
      content: brandStr ?? '',
    };
    const imageContent: { type: BrandContentType; content: string } = {
      type: 'image',
      content: image ?? '',
    };
    switch (showType) {
      case BrandShowType.OnlyText:
        return {
          content: [textContent],
          containerStyle,
        };
      case BrandShowType.OnlyImage:
        return {
          content: [imageContent],
          containerStyle,
        };
      case BrandShowType.BeforeText:
        return {
          content: [textContent, imageContent],
          containerStyle,
        };
      case BrandShowType.BeforeImage:
        return {
          content: [imageContent, textContent],
          containerStyle,
        };
      default:
        return null;
    }
  } catch (e: unknown) {
    return null;
  }
};
