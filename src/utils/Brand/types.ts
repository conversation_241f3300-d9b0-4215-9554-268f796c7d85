export enum BrandShowType {
  BeforeImage = 1, // 先图后文
  BeforeText = 2, // 先文后图
  OnlyImage = 3, // 图片
  OnlyText = 4, // 文字
}

export type BrandContentType = 'image' | 'text';
export interface BrandConfigResponse {
  showType: BrandShowType;
  image?: string; // 图片地址
  brandStr?: string; // 文字
  show: boolean; // 开关
  custom?: Record<string, string> | null;
}

export interface BrandType {
  content: {
    type: BrandContentType;
    content: string; // 文本 或 图片地址
  }[]; // 显示内容，按数组顺序显示
  containerStyle: {
    width: string; // 外容器宽度
    color: string; // 文本颜色。
  };
}
