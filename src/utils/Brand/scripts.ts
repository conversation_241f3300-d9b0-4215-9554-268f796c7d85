import axios from 'axios';

import { adaptor } from '../ShimoSDK/APIAdaptor';
import type { BrandConfigResponse } from './types';

export const getBrandConfigReq = async (params?: { lang?: string }): Promise<BrandConfigResponse> => {
  const { url, ...configs } = adaptor({
    url: '/sdk/v2/api/internal/brand',
    method: 'GET',
    headers: {},
    query: params,
  });
  const { data }: { data: BrandConfigResponse } = await axios.get<BrandConfigResponse>(url, configs);
  return data;
};
