import 'core-js/features/global-this';
import 'core-js/features/object/from-entries';

import _ from 'lodash';
import type { Options } from 'shimo-broadcast-channel';
import { ShimoBroadcastChannel } from 'shimo-broadcast-channel';

import { initPreviewLoading } from '../loading/initLoading';
import { createSDK } from '../ShimoSDK';
import { initSocket } from '../ShimoSDK/socket';
// TODO:该功能尚未实现
// import { WorkerConfig } from '../../components/ServiceWorker/caches';

window._ = _;
// window.s18n = s18n;
window.__SM__ = {
  ...window.__SM__,
  createChannel(options: Options) {
    return new ShimoBroadcastChannel(options);
  },
};

window.createSDK = createSDK;

/**
 * 初始化编辑器，统一处理 root 元素、generic error 后的展示
 */
window.initSDK = (initializers) => {
  Promise.resolve()
    .then(() => {
      const root = document.querySelector('#root');
      if (!root) {
        throw new Error('container not found');
      }
    })
    .then(async () => {
      for (const initializer of initializers) {
        await initializer();
      }
    })
    .catch((err) => {
      const body = document.querySelector('body');
      if (body) {
        body.innerHTML = `${body.innerHTML}<div>${err instanceof Error ? err.message : String(err)}</div>`;
      } else {
        console.error(err);
      }
    });
};

window.initSocket = initSocket;

// 初始化云文件预览 loading 组件依赖注入代码，
// 用于通知 iframe 上层 lizard-one 环境编辑器何时完成加载
initPreviewLoading();

const swURL = window.__SM__?.SW_URL;
if ('serviceWorker' in navigator) {
  if (swURL) {
    try {
      const url = new URL(swURL, window.__RUNTIME_ENV__.SDK_V2_HOST);
      url.pathname = `${window.__RUNTIME_ENV__.SDK_V2_ROOT_PATH}${url.pathname}`;
      const searchParams = new URLSearchParams(location.search);

      window.addEventListener('load', () => {
        const config: any = {
          cdnHost: window.__SM_CDN_HOST__,
          debug: searchParams.get('debug') === 'true',
          endpoint: window.__RUNTIME_ENV__.SDK_V2_HOST,
          fileTypes: window.__SM__.FILE_TYPES,
          textDir: getComputedStyle(document.body).direction,
        };

        try {
          Object.assign(config, JSON.parse(localStorage.getItem('SM_WORKER_CONFIG') ?? '{}'));
        } catch (e: unknown) {}

        url.searchParams.append('workerConfig', btoa(JSON.stringify(config)));

        navigator.serviceWorker
          .register(url.toString())
          .then(() => {
            console.debug('Service worker registered');
          })
          .catch((err: unknown) => {
            console.error('Service worker registration failed:', err);
          });
      });
    } catch (e: unknown) {
      console.error('Service worker not loaded', {
        swURL,
        error: e,
      });
    }
  } else {
    navigator.serviceWorker
      .getRegistrations()
      .then(async (registrations) => {
        for (const registration of registrations) {
          if (registration.scope.includes(`${window.__RUNTIME_ENV__.SDK_V2_ROOT_PATH}/service-worker`)) {
            await registration.unregister();
            console.debug(`Service worker unregistered: ${registration.scope}`);
          }
        }
      })
      .catch((err: unknown) => {
        console.error('Failed to unregister service worker:', err);
      });
  }
}
