// 列表滚动
export function useMyScroll() {
  let lastScrollTop = 0;

  const scrollHandler = (options: {
    el: HTMLElement;
    cb: (direction: 'up' | 'down') => void;
    distance?: number; // 距离顶部或者底部的距离，达到时触发回调函数
  }) => {
    const { el, cb, distance = 200 } = options;
    // 如果是 document，使用 window 的相关属性
    const isDocument = el instanceof Document;
    let scrollTop = 0,
      scrollHeight = 0,
      clientHeight = 0;
    if (!isDocument) {
      scrollTop = el.scrollTop;
      scrollHeight = el.scrollHeight;
      clientHeight = el.clientHeight;
    } else {
      scrollTop = window.scrollY;
      scrollHeight = document.body.scrollHeight;
      clientHeight = window.innerHeight;
    }

    const isScrollingDown = scrollTop > lastScrollTop;
    if (scrollTop + clientHeight >= scrollHeight - distance) {
      if (cb) cb(isScrollingDown ? 'down' : 'up');
    }

    lastScrollTop = scrollTop <= 0 ? 0 : scrollTop; // 在移动设备上或在某些情况下(用户快速滑动列表)，滚动位置可能会变为负值
    return scrollTop;
  };
  return scrollHandler;
}
