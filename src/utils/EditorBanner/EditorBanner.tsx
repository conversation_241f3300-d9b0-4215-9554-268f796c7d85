// FIXME: 将这两个icon放到assets文件夹下
import { CloseOutline16Base64 } from './assets/close-outline-16';
import { Info20Base64 } from './assets/info-20';
import { useNoticeAlert } from './hooks/useNoticeAlert';
import styles from './index.less';

export const EditorBanner = (props: { socket: unknown }) => {
  const { socket } = props;
  const noticeAlertProps = useNoticeAlert(socket);
  const bannerProps = [noticeAlertProps];
  if (!bannerProps?.[0].visible || !bannerProps?.[0].text) {
    return null;
  }
  const banner = bannerProps[0];
  return (
    <div className={styles.alertText} id="alert-text">
      <img className={styles.infoIcon} src={Info20Base64} />
      <span className={styles.text}>{banner.text}</span>
      <span className={styles.close} onClick={banner.handleClose}>
        <img className={styles.closeIcon} src={CloseOutline16Base64} />
      </span>
    </div>
  );
};
