/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { useCallback, useEffect, useState } from 'react';

// ws通知的key
const WS_NOTICE_KEY = 'NOTICE';
// s转ms
const millisecondPerSecond = 1000;

// 系统通知的key
const SYSTEM_NOTICE_MSG = 'system-notice-msg';

// ws返回的页面类型
enum WsNotificationPageType {
  Desktop = 'desktop',
  Editor = 'editor',
}

interface BannerType {
  visible?: boolean;
  text?: string | null;
  handleClose?: () => void;
}

// 系统通知的位置
export enum NoticeAlertPositionType {
  Desktop = 'Desktop',
  Editor = 'Editor',
}

// ws返回的msg类型
type MsgType = {
  content: string;
  locale: 'en' | 'jp' | 'zh-CN';
}[];

interface BannerData {
  id: string;
  content: string;
  startTime: number;
  endTime: number;
}

// lang => en | ja | zh-CN | ar-SA
const getLang = (): string => {
  const lang = document.documentElement.lang;
  if (lang === 'ja') {
    return 'jp'; // 兼容
  }
  return lang;
};

// 当前时间是否属于时间范围内(时间戳单位/秒)
const isInTimeRange = (start: number, end: number): boolean => {
  const currentTime = Date.now();
  return currentTime >= start && currentTime <= end;
};

// 定义受支持的语言类型
const SUPPORTED_LOCALES = ['en', 'jp', 'zh-CN'] as const;
type SupportedLocale = (typeof SUPPORTED_LOCALES)[number];
// 是服务器推送的消息
const isServerPushMessage = (value: unknown): value is MsgType => {
  if (!Array.isArray(value)) {
    return false;
  }

  return !value.some((item) => {
    const isInvalidObject = typeof item !== 'object' || item === null;
    const isMissingFields = !('content' in item && 'locale' in item);
    const isInvalidLocale = !SUPPORTED_LOCALES.includes(item.locale as SupportedLocale);

    return isInvalidObject || isMissingFields || isInvalidLocale;
  });
};

const isSameUTCDay = (timestamp1: number): boolean => {
  const d1 = new Date(timestamp1);
  const d2 = new Date();
  return (
    d1.getUTCFullYear() === d2.getUTCFullYear() &&
    d1.getUTCMonth() === d2.getUTCMonth() &&
    d1.getUTCDate() === d2.getUTCDate()
  );
};

// 通知提醒hook
export const useNoticeAlert = (socket: unknown): BannerType => {
  const [visible, setVisible] = useState<boolean>(false);
  const [bannerData, setBannerData] = useState<BannerData | null>(null);
  const [preferenceData, setPreferenceData] = useState<Record<string, number>>(() =>
    JSON.parse(localStorage.getItem(SYSTEM_NOTICE_MSG) ?? '{}'),
  );

  // 更新localStorage
  const updatePreference = useCallback(
    (id: string) => {
      const newData = {
        ...preferenceData,
        [id]: Date.now(),
      };
      localStorage.setItem(SYSTEM_NOTICE_MSG, JSON.stringify(newData));
      setPreferenceData(newData);
    },
    [preferenceData],
  );

  // 关闭通知的统一处理
  const onClose = useCallback(() => {
    setVisible(false);
    if (bannerData?.id) {
      updatePreference(bannerData.id);
    }
  }, [bannerData, updatePreference]);

  // 处理通知数据的统一方法
  const handleNotificationData = useCallback(
    (data?: {
      type?: string;
      data?: {
        id?: string;
        msg?: string;
        page?: WsNotificationPageType[];
        start?: number;
        end?: number;
      };
    }) => {
      const { type, data: { msg = '', id = '', page = [], start = 0, end = 0 } = {} } = data ?? {};
      const parseMsg = msg && JSON.parse(msg);
      const startTime = start * millisecondPerSecond;
      const endTime = end * millisecondPerSecond;

      const isTargetPage = page.includes(WsNotificationPageType.Editor);
      if (
        type === WS_NOTICE_KEY &&
        isTargetPage &&
        isServerPushMessage(parseMsg) &&
        isInTimeRange(startTime, endTime)
      ) {
        const content = parseMsg.find((item) => item.locale === getLang())?.content;
        if (content && (!preferenceData[id] || !isSameUTCDay(preferenceData[id]))) {
          setBannerData({
            id,
            content,
            startTime,
            endTime,
          });
          setVisible(true);
        }
      }
    },
    [preferenceData],
  );

  useEffect(() => {
    // @ts-expect-error
    socket?.on?.('message', handleNotificationData);
    // 需要手动触发一次才会推送通知
    // @ts-expect-error
    socket?.emit?.('message', {
      type: WS_NOTICE_KEY,
    });
    return () => {
      // @ts-expect-error
      socket?.off?.('message', handleNotificationData);
    };
  }, [socket, handleNotificationData]);

  // 自动关闭逻辑
  useEffect(() => {
    let timer: ReturnType<typeof setInterval> | null = null;
    const checkTimeRange = () => {
      if (bannerData && !isInTimeRange(bannerData.startTime, bannerData.endTime)) {
        setVisible(false);
        if (timer) {
          clearInterval(timer);
        }
      }
    };
    if (bannerData && visible && isInTimeRange(bannerData.startTime, bannerData.endTime)) {
      timer = setInterval(checkTimeRange, millisecondPerSecond);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [bannerData, visible]);

  return {
    visible,
    text: bannerData?.content,
    handleClose: onClose,
  };
};
