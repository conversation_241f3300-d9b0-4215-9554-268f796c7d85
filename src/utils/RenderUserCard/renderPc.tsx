/* eslint-disable @typescript-eslint/no-unused-expressions */
import { throttle } from 'lodash';
import { createRoot } from 'react-dom/client';

import { calculateCardPosition, getCardRect, UserCardPositionType } from './position';
import { UserCardShell } from './UserCard';
import { createDeferred, createDeferredRef, createRef } from './utils';

export interface RenderUserCardProps {
  // 用户id
  uid: number | string;
  // 触发名片的元素在页面中的top，left位置与自身width, height (部分场景可通过getBoundingClientRect获取)
  rect: {
    left: number;
    top: number;
    width: number;
    height: number;
    hasReducedHeaderHeight?: boolean; // 已经减去了header的高度，默认为false
  };
  // 显示位置 默认为left_top
  position?: UserCardPositionType;
  // 数据预加载 同时处理路人样式
  me?: {
    name?: string;
    avatar?: string;
  };
}

// 判断当前卡片位置是否符合标准
const isCardPositionValid = (userCardRect: { top: number; left: number; width: number; height: number }) => {
  const cardRight = document.body.offsetWidth - (userCardRect.left + userCardRect.width);
  const cardBottom = document.body.offsetHeight - (userCardRect.top + userCardRect.height);
  return userCardRect.top > 0 && userCardRect.left > 0 && cardRight > 0 && cardBottom > 0;
};

export const renderPcUserCard: (props: RenderUserCardProps) => {
  close?: (force?: boolean) => void;
  opened?: Promise<void>;
  closed?: Promise<void>;
} = (props) => {
  const { uid, rect, position = UserCardPositionType.left_top } = props;

  // 重新定位卡片
  const getCardPosition = ({ width, height }: { width: number; height: number }): { top: number; left: number } => {
    const posInfo = {
      top: rect.top,
      height: rect.height,
      left: rect.left,
      width: rect.width,
    };
    const rectInfo = {
      width,
      height,
    };
    // 获取卡片位置
    let userCardRect = getCardRect(position, posInfo, rectInfo);
    // 兼容处理
    if (isCardPositionValid(userCardRect)) {
      return {
        top: userCardRect.top,
        left: userCardRect.left,
      };
    }
    // 如果当前的位置会被浏览器遮挡，那么就从方位枚举里挑一个不被遮挡的，如果找不到，那就么得法了┓(´∀`)┏
    const pos =
      Object.values(UserCardPositionType).find((pos: UserCardPositionType) =>
        isCardPositionValid(getCardRect(pos, posInfo, rectInfo)),
      ) ?? position;
    userCardRect = getCardRect(pos, posInfo, rectInfo);
    return {
      top: userCardRect.top > 0 ? userCardRect.top : 0,
      left: userCardRect.left > 0 ? userCardRect.left : 0,
    };
  };

  const body = document.querySelector('body');
  const isInCardRef = createRef(false);
  const isNeedClose = createRef(false);
  const opendDeferredRef = createDeferredRef(createDeferred());
  const closedDeferredRef = createDeferredRef(createDeferred());
  const container = document.createElement('div');

  container.style.zIndex = '1060';
  container.style.boxShadow = '0 20px 32px 0 rgba(0, 0, 0, 0.06)';
  container.style.borderRadius = '4px';
  container.style.border = '1px solid #41464B1A';
  container.className = 'lizard-user-card';
  container.id = `user-card-${uid}-${Math.floor(Math.random() * 1000000)}`;

  /**
   * 重新渲染位置
   */
  const handleReposition = (userCardRect: { top?: number; left?: number }) => {
    userCardRect.top !== undefined && (container.style.top = `${userCardRect.top}px`);
    userCardRect.left !== undefined && (container.style.left = `${userCardRect.left}px`);
  };

  /**
   * 重新计算卡片不被遮挡的位置
   * TODO: 初始化的时候没渲染dom，宽高是0，useEffect的时候是骨架屏的宽高，实际需要重绘后的宽高，
   * 这里选择了在组件内部渲染完成后再调用一次更新宽高数据 （ReactDOM.render的callback）
   */
  const handleResize = () => {
    const { width, height } = container.getBoundingClientRect();
    handleReposition(getCardPosition({ width: width || 288, height: height || 100 }));
  };

  // 除了左右布局的初步定位操作
  container.style.position = 'absolute';
  handleResize();

  /**
   * 不在卡片范围内就关闭
   */
  const handleClose = (event: any) => {
    if (!container.contains(event.target)) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      onClose();
    }
  };

  /**
   * 鼠标移动事件
   */
  const onMousemove = throttle((event: MouseEvent) => {
    const cursorX = event.clientX; // 获取鼠标当前的水平位置
    const cursorY = event.clientY; // 获取鼠标当前的垂直位置

    const { top, left, bottom, right } = calculateCardPosition(container, position);

    // 判断光标位置是否在卡片内
    const isCursorInUserCard = cursorX >= left && cursorX <= right && cursorY >= top && cursorY <= bottom;

    if (isCursorInUserCard) {
      isInCardRef.current = true;
    } else {
      isInCardRef.current = false;
      if (isNeedClose.current) {
        setTimeout(() => {
          handleClose(event);
        }, 300);
      }
    }
  }, 250);

  /**
   * 关闭
   */
  const onClose = () => {
    if (body?.contains(container)) {
      document?.removeEventListener('mousedown', handleClose, true);
      document.removeEventListener('mousemove', onMousemove);
      body?.removeChild(container);
      closedDeferredRef.current?.resolve();
    }
  };

  /**
   * 主动关闭user card，
   * @param force force 表示 UserCard 忽略内部状态强制关闭
   */
  const onCloseUserCard = (force?: boolean) => {
    if (force) {
      onClose();
    } else {
      // 延迟一点判定是因为鼠标移动事件加上了250ms的节流函数 isInCardRef的更新并没有那么及时
      setTimeout(() => {
        if (!isInCardRef.current) {
          onClose();
        } else {
          isNeedClose.current = true;
        }
      }, 300);
    }
  };

  const onResize = () => {
    handleResize();
    // 渲染结束后将opend的状态置为resolve
    opendDeferredRef.current.resolve();
  };

  const userId = typeof uid === 'string' ? parseInt(uid, 10) : uid;

  const root = createRoot(container);
  root.render(<UserCardShell handleResize={onResize} userId={userId} />);

  // 开启卡片
  container.style.display = 'block';
  body?.appendChild(container);
  // 监听鼠标点击事件
  document?.addEventListener('mousedown', handleClose, true);
  // 监听鼠标移动事件
  document.addEventListener('mousemove', onMousemove);

  return {
    close: onCloseUserCard,
    opened: opendDeferredRef.current?.promise,
    closed: closedDeferredRef.current?.promise,
  };
};
