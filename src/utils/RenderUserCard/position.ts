export enum UserCardPositionType {
  left = 'left',
  right = 'right',
  top = 'top',
  bottom = 'bottom',
  left_top = 'left_top',
  left_bottom = 'left_bottom',
  right_top = 'right_top',
  right_bottom = 'right_bottom',
  top_left = 'top_left',
  top_right = 'top_right',
  bottom_left = 'bottom_left',
  bottom_right = 'bottom_right',
}

// 默认的间距
const DEFAULT_DISTANCE = 10;

// 获取卡片位置
export const getCardRect = (
  position: UserCardPositionType,
  rect: {
    left: number;
    top: number;
    width: number;
    height: number;
    hasReducedHeaderHeight?: boolean;
  },
  cardRect: {
    width: number;
    height: number;
  },
) => {
  const cardWidth = cardRect.width;
  const cardHeight = cardRect.height;

  switch (position) {
    case UserCardPositionType.top:
      return {
        top: rect.top - cardHeight - DEFAULT_DISTANCE,
        left: rect.left + rect.width / 2 - cardWidth / 2,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.left:
      return {
        top: rect.top + rect.height / 2 - cardHeight / 2,
        left: rect.left - cardWidth - DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.right:
      return {
        top: rect.top + rect.height / 2 - cardHeight / 2,
        left: rect.left + rect.width + DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.bottom:
      return {
        top: rect.top + rect.height + DEFAULT_DISTANCE,
        left: rect.left + rect.width / 2 - cardWidth / 2,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.left_top:
      return {
        top: rect.top,
        left: rect.left - cardWidth - DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.left_bottom:
      return {
        top: rect.top + rect.height - cardHeight,
        left: rect.left - cardWidth - DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.right_top:
      return {
        top: rect.top,
        left: rect.left + rect.width + DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.right_bottom:
      return {
        top: rect.top + rect.height - cardHeight,
        left: rect.left + rect.width + DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
    case UserCardPositionType.top_left:
      return {
        top: rect.top - cardHeight - DEFAULT_DISTANCE,
        left: rect.left,
        width: cardWidth,
        height: cardHeight,
      };

    case UserCardPositionType.top_right:
      return {
        top: rect.top - cardHeight - DEFAULT_DISTANCE,
        left: rect.left + rect.width - cardWidth,
        width: cardWidth,
        height: cardHeight,
      };

    case UserCardPositionType.bottom_left:
      return {
        top: rect.top + rect.height + DEFAULT_DISTANCE,
        left: rect.left,
        width: cardWidth,
        height: cardHeight,
      };

    case UserCardPositionType.bottom_right:
      return {
        top: rect.top + rect.height + DEFAULT_DISTANCE,
        left: rect.left + rect.width - cardWidth,
        width: cardWidth,
        height: cardHeight,
      };
    default:
      return {
        top: rect.top,
        left: rect.left - cardWidth - DEFAULT_DISTANCE,
        width: cardWidth,
        height: cardHeight,
      };
  }
};

// 计算卡片位置
export const calculateCardPosition = (container: HTMLDivElement, position: UserCardPositionType) => {
  const cardRect = container.getBoundingClientRect(); // 获取目标元素的位置信息

  let { top, left, bottom, right } = cardRect;

  // 这里是为了将卡片和触发元素之间的距离也包括进来
  switch (position) {
    case UserCardPositionType.top:
    case UserCardPositionType.top_left:
    case UserCardPositionType.top_right:
      bottom += DEFAULT_DISTANCE;
      break;
    case UserCardPositionType.bottom:
    case UserCardPositionType.bottom_left:
    case UserCardPositionType.bottom_right:
      top -= DEFAULT_DISTANCE;
      break;
    case UserCardPositionType.left:
    case UserCardPositionType.left_top:
    case UserCardPositionType.left_bottom:
      right += DEFAULT_DISTANCE;
      break;
    case UserCardPositionType.right:
    case UserCardPositionType.right_top:
    case UserCardPositionType.right_bottom:
      left -= DEFAULT_DISTANCE;
      break;
    default:
  }

  return {
    top,
    left,
    right,
    bottom,
  };
};
