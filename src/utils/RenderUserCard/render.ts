import { renderMobileUserCard } from './renderMobile';
import type { RenderUserCardProps } from './renderPc';
import { renderPcUserCard } from './renderPc';
import { isMobile } from './utils';

export const renderUserCard: (props: RenderUserCardProps) => {
  close?: (force?: boolean) => void;
  opened?: Promise<void>;
  closed?: Promise<void>;
} = (props) => {
  if (isMobile()) {
    return renderMobileUserCard(props);
  }
  return renderPcUserCard(props);
};
