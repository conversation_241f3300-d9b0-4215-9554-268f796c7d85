import { useDataProvider, UserCard } from '@shimo/widgets';
import type { ApiAdaptor } from '@shimo/widgets/lib/UserCard/hooks/useDataProvider';

import { adaptor } from '../ShimoSDK/APIAdaptor';

export const UserCardShell = ({ userId, handleResize }: { userId: number; handleResize?: () => void }) => {
  const { data } = useDataProvider(userId, {
    apiAdaptor: adaptor as ApiAdaptor,
  });
  return (
    // FIXME:这个UserCard要在这个项目里重写一个
    <UserCard
      alias={data.alias}
      avatar={data.avatar}
      extendAttrs={data.extendAttrs}
      handleResize={handleResize}
      id={userId ?? data.id}
      infos={data.infos}
      labels={data.labels}
      name={data.name}
      paths={data.paths}
      requesting={data.requesting}
    />
  );
};
