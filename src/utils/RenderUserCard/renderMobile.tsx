import { createRoot } from 'react-dom/client';

import type { RenderUserCardProps } from './renderPc';
import { UserCardShell } from './UserCard';
import { createDeferred, createDeferredRef } from './utils';

export const renderMobileUserCard: (props: RenderUserCardProps) => {
  close?: (force?: boolean) => void;
  opened?: Promise<void>;
  closed?: Promise<void>;
} = (props) => {
  const { uid } = props;
  const body = document.body;
  const opendDeferredRef = createDeferredRef(createDeferred());
  const closedDeferredRef = createDeferredRef(createDeferred());
  // 创建遮罩层元素并设置样式
  const overlay = document.createElement('div');
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.zIndex = '1060';

  // 创建内容区域元素并设置样式
  const content = document.createElement('div');
  content.style.position = 'absolute';
  content.style.top = '50%';
  content.style.left = '50%';
  content.style.transform = 'translate(-50%, -50%)';
  content.style.backgroundColor = '#fff';
  content.style.zIndex = '1061';
  content.style.boxShadow = '0 20px 32px 0 rgba(0, 0, 0, 0.06)';
  content.style.borderRadius = '4px';
  content.style.border = '1px solid #41464B1A';

  // 添加遮罩层和内容区域到文档中
  body?.appendChild(overlay);
  body?.appendChild(content);

  /**
   * 关闭
   */
  const onClose = () => {
    if (body?.contains(overlay)) {
      body?.removeChild(overlay);
    }
    if (body?.contains(content)) {
      body?.removeChild(content);
    }
    closedDeferredRef.current?.resolve();
  };

  const onResize = () => {
    // 渲染结束后将opend的状态置为resolve
    opendDeferredRef.current.resolve();
  };

  // 点击遮罩层关闭
  overlay.addEventListener('click', () => {
    onClose();
  });

  overlay.className = 'lizard-user-card';
  overlay.id = `user-card-${uid}-${Math.floor(Math.random() * 1000000)}`;

  const userId = typeof uid === 'string' ? parseInt(uid, 10) : uid;
  const root = createRoot(content);
  root.render(<UserCardShell handleResize={onResize} userId={userId} />);

  return {
    close: onClose,
    opened: opendDeferredRef.current?.promise,
    closed: closedDeferredRef.current?.promise,
  };
};
