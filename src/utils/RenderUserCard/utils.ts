interface Deferred<T> {
  promise: Promise<T>;
  resolve: (value: T) => void;
  reject: (error?: unknown) => void;
}

export const createDeferredRef: (initialValue: any) => {
  current: Deferred<void>;
} = (initialValue) => {
  return {
    current: initialValue,
  };
};

export const createRef: (initialValue: any) => {
  current: boolean;
} = (initialValue) => {
  return {
    current: initialValue,
  };
};

export const createDeferred = <T>(): Deferred<T> => {
  const deferred: Partial<Deferred<T>> = {};
  deferred.promise = new Promise<T>((resolve, reject) => {
    deferred.resolve = resolve;
    deferred.reject = reject;
  });
  return deferred as Deferred<T>;
};

/**
 * 判断是否为移动端
 */
export function isMobile() {
  return /(iphone|ipod|ipad|android|windows phone|mobile)/i.test(navigator.userAgent);
}
