import io from 'socket.io-client';

import { getBoolean, getSocketHost, getSocketPath } from '@/utils/env';

import type { ShimoSDK } from '../ShimoSDK';
import type { RuntimeEnv } from '../types';

const defaultInitOpts: InitSocketOpts = {
  fileGuid: 'unknownFileGuid',
};

export interface InitSocketOpts {
  fileGuid: string;
}

interface InitSocketResponse {
  socket: SocketIOClient.Socket;

  /**
   * 更新 socket 配置的更新器，比如定时从 RUNTIME 中获取 Token 和 Signature 信息更新
   * https://github.com/socketio/socket.io/issues/3715
   */
  configUpdater: (envs: RuntimeEnv) => void;
}

export type InitSocketFunction = (sdk: ShimoSDK, opts: InitSocketOpts) => InitSocketResponse;

export const initSocket: InitSocketFunction = (
  sdk: <PERSON>moSDK,
  opts: InitSocketOpts = defaultInitOpts,
): InitSocketResponse => {
  const getURI = (search?: string) => {
    const url = new URL(getSocketHost(), location.origin);
    url.searchParams.append('signature', sdk.signature);
    url.searchParams.append('token', sdk.token);
    url.pathname = `${url.pathname}`;

    if (search) {
      const params = new URLSearchParams(search);
      params.forEach((value, key) => {
        url.searchParams.set(key, value);
      });
    }

    return {
      url,
      socketPath: getSocketPath() || '/ws',
    };
  };

  const { url, socketPath } = getURI();
  const socket = io(url.toString(), {
    path: socketPath,
    // 仅在 ws 连接失败且配置了 fallback_polling，才在重试时使用 polling
    // https://socket.io/docs/v2/client-api/#with-websocket-transport-only
    transports: ['websocket'],
    reconnection: true,
    reconnectionAttempts: 10,
    reconnectionDelay: 1000,
  });

  socket.on('connect', () => {
    socket?.emit('message', { type: 'CLIENT_READY', padId: opts.fileGuid });
  });

  if (getBoolean('FALLBACK_POLLING')) {
    socket.on('reconnect_attempt', () => {
      socket.io.opts.transports = ['polling', 'websocket'];
    });
  }

  return {
    socket,

    configUpdater: () => {
      // 防止意外情况持续执行
      if (!socket?.io?.opts?.query) {
        return;
      }

      try {
        // socket.io-client 2.4 实际上 opts query 是个 string，但是 types 包并没有 2.x
        // 版本为 1.x 的 types query 是个 object
        const query = (socket.io.opts.query as string) || '';
        const { url } = getURI(query);
        socket.io.opts.query = url.searchParams.toString();
        // uri.query has priority over opts.query
        // see https://github.com/socketio/engine.io-client/blob/3.5.x/engine.io.js#L114
        socket.io.uri = url.toString();
      } catch (e: unknown) {
        console.warn('update ws token and signature failed, err : ', e);
      }
    },
  };
};
