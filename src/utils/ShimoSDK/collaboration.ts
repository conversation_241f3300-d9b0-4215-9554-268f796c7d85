import { SaveStatus, SyncStatus } from '@shimo/core-collaboration';

import type { ShimoSDK } from '.';

export function createSaveStatus(sdk: ShimoSDK): SaveStatus {
  const delegation = new SaveStatus();

  // TODO: 将事件代理出去
  console.log(sdk);

  // delegation.addChangeStateListener((state, context) => {
  //   // TODO:
  // });

  // delegation.addErrorListener((error, context) => {
  //   // TODO:
  // });

  return delegation;
}

export function createSyncStatus(sdk: ShimoSDK): SyncStatus {
  const delegation = new SyncStatus();

  // TODO: 将事件代理出去
  console.log(sdk);

  // delegation.addChangeStateListener((state, context) => {
  //   // TODO:
  // });

  // delegation.addErrorListener((error, context) => {
  //   // TODO:
  // });

  return delegation;
}
