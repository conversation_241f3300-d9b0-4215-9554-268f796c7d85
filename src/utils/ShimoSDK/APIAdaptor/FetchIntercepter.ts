import type { RequestOptions } from './APIAdaptor';
import adaptor from './APIAdaptor';

let intercepted = false;

function getBody(input: RequestInfo, init?: RequestInit): BodyInit | undefined {
  if (init?.body !== null) {
    if (init?.body instanceof ReadableStream) {
      return init?.body.tee()[1];
    }
    return init?.body;
  }

  if (typeof input !== 'string' && input.body !== null) {
    if (input.body instanceof ReadableStream) {
      return input.body.tee()[1];
    }
    return input.body;
  }

  return undefined;
}

function getMethod(input: RequestInfo, init?: RequestInit): string {
  if (init?.method) {
    return init.method;
  }

  if (typeof input !== 'string' && input.method !== null) {
    return input.method;
  }

  return 'GET';
}

function getHeaders(input: RequestInfo, init?: RequestInit): Record<string, string> {
  let headers: Headers = new Headers();

  if (init?.headers) {
    headers = new Headers(init.headers);
  } else if (typeof input !== 'string' && input.headers !== null) {
    headers = new Headers(input.headers);
  }

  return Object.fromEntries(headers.entries());
}

/**
 * 将一些 Request 有关的值复制过来
 */
function copyRequestInit(input: RequestInfo, init?: RequestInit): RequestInit {
  const finalInit: Record<string, unknown> = {};

  const keys = [
    'cache',
    'credentials',
    'integrity',
    'keepalive',
    'mode',
    'redirect',
    'referrer',
    'referrerPolicy',
    'signal',
    'window',
  ];

  if (input instanceof Request) {
    for (const key of keys) {
      const value = (input as any)[key];
      // 除了 undefined，其他都会被转 string 处理
      if (typeof value !== 'undefined') {
        finalInit[key] = value;
      }
    }
  }

  if (init) {
    for (const key of keys) {
      const value = (init as any)[key];
      // 除了 undefined，其他都会被转 string 处理
      if (typeof value !== 'undefined') {
        finalInit[key] = value;
      }
    }
  }

  return finalInit;
}

export function interceptFetchRequest() {
  if (intercepted) {
    return;
  }

  const { fetch } = window;

  window.fetch = (async (input: RequestInfo, init?: RequestInit) => {
    const body = getBody(input, init);

    const fullURL = typeof input === 'string' ? input : input.url;
    const [url, search] = fullURL.split('?');

    let options: RequestOptions = {
      url,
      method: getMethod(input, init),
      query: Object.fromEntries(new URLSearchParams(search || '').entries()),
      headers: getHeaders(input, init),
    };

    options = adaptor(options);

    const qs = new URLSearchParams(options.query).toString();
    const finalURL = `${options.url}${qs ? `?${qs}` : ''}`;

    return await fetch(finalURL, {
      headers: options.headers,
      method: options.method,
      body,
      ...copyRequestInit(input, init),
    });
  }) as typeof fetch;

  intercepted = true;
}
