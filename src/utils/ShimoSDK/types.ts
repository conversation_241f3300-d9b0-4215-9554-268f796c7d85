/* eslint-disable @typescript-eslint/ban-types */
import type { SaveStatusDelegation, SyncStatusDelegation } from '@shimo/core-collaboration';
import type { DeviceMode, DisableMentionCards, PerformanceEntry, ShowToastOptions } from 'shimo-js-sdk-shared';
import type { StartParams } from 'shimo-startparams';

import type { AttachLimitType, CheckPermissionType } from '../../components/CheckPermission/scripts';
import type { BrandType } from '../Brand';
import type { RenderUserCardProps } from '../RenderUserCard';
import type { CreateSDKFunction, RuntimeUpdater } from '.';
import type { adaptor } from './APIAdaptor';
import type { InitSocketFunction } from './socket';

export interface SMGlobal {
  customAdaptorFuncString?: string;
  customAdaptor?: Function;
  customAdaptorContextString?: string;
  customAdaptorContext?: Record<string, unknown>;
}

declare global {
  interface Window {
    /**
     * 初始化编辑器，统一处理 root 元素、generic error 后的展示。
     */
    initSDK: (initializers: Function[]) => void;

    /**
     * 创建 ShimoSDK 的方法
     */
    createSDK: CreateSDKFunction;

    initSocket: InitSocketFunction;

    /**
     * 轻文档暴露 lodash
     */
    _: any;

    __env: unknown;

    ENV: { [key: string]: any };
    isOfflineClient: boolean;

    __RUNTIME_ENV__: RuntimeEnv;

    __SM_CDN_HOST__: string;

    dataLayer: Array<{ [key: string]: any }>;
    perf: {
      startTime: {
        launch: number;
        [key: string]: number;
      };
      trackNow: () => number;
      trackStart: (tag: string) => void;
      trackEnd: (tag: string) => number;
    };

    SDKDocx: {
      MODOC_VERSION: number;
      release?: string;

      // eslint-disable-next-line no-undef
      Editor: typeof DocxEditor;
      createSDK2: (options: CreateDocumentProOptions) => Promise<DocxEditor>;
    };

    Smsheet: {
      // https://git.shimo.im/shimo/sdk2-api-doc/-/blob/master/output/sdk.d.ts
      createSDK2: (options: CreateSheetOptions) => Promise<SheetEditor>;
    };

    // Sheet Mobile 入口
    // eslint-disable-next-line
    Smm_sheet: {
      // createSDK2 参考上面 Smsheet 定义
      createSDK2: (options: CreateSheetOptions) => Promise<SheetEditor>;
    };

    s18n: any;

    SDKDoc: {
      launch: (options: SDKDocLaunchOptions) => Promise<SDKDocLaunchResult>;
      createSDK2: (options: CreateDocumentOptions) => Promise<DocEditor>;
    };

    SmTable: {
      createSDK2: (options: CreateTableOptions) => Promise<TableEditor>;
    };

    smForm: {
      createSDK2: (options: CreateSDK2BaseOptions) => Promise<FormEditor>;
    };

    __LIZARD_FAILED__: boolean;

    /**
     * entrypoint 数据
     */

    file: File;
    user: User;

    // 编辑器是否渲染完成
    rendered: boolean;

    editor: DocxEditor;

    /**
     * 用于运行时的全局变量，和 __RUNTIME_ENV__ 的区别是，这类对象一般由代码主动设置，不通过配置输出
     */
    __SM__: {
      editor: DocEditor | TableEditor | PresentationEditor | SheetEditor | DocxEditor;

      /**
       * 性能记录片段
       */
      performanceEntries?: PerformanceEntry[];

      /**
       * SDK 自己的 service worker js 地址
       */
      SW_URL: string;

      /**
       * manifest.json 的字符串结果
       */
      MANIFEST_JSON: string;

      /**
       * 支持文件类型的字符串，"-6,-2" or "*"
       */
      FILE_TYPES: string;

      [key: string]: unknown;
    };

    presentationOptions?: PresentationEditorOptions;

    smPresentation: {
      createSDK2: (options: CreatePresentationOptions) => Promise<PresentationEditor>;
    };

    /**
     * 内容快照 ID
     */
    snapshotId?: string;
  }
}

export declare class BaseEditor {
  [key: string]: any;
  on: (...args: any[]) => void;
  off: (...args: any[]) => void;
  emit: (event: string, payload: any) => void;

  /**
   * 是否保持 iframe 当前 focus 状态，在 window blur 时不触发 mousedown
   */
  keepFocus: () => boolean;
}

export declare class SheetEditor extends BaseEditor {
  updateRuntimeEnv?: RuntimeUpdater;
}

export declare class PresentationEditor extends BaseEditor {
  constructor(options: PresentationEditorOptions);

  setContent(content: any): void;
  setContext(context: { guid?: string; user?: User; file?: File }): void;
  addSlot(slotName: string, slot: string | HTMLElement): void;
  getContent(): any;
  applyChange(change: any): void;
  canHandleChanges(): boolean;
  render(container: HTMLElement): void;
  destroy(): void;
}
interface PresentationEditorOptions {
  mode?: PresentationMode;
  modeParams?: PresentationModeParams;
  offline?: boolean;

  /** 覆盖某些操作实现 */
  override?: {
    uploadFile?: {
      /** File 或 url (可能跨域) */
      file: File | string;
      options?: {
        /** 上传进度通知，progress 为 0~100 的整数 (无小数部分) */
        onProgress?: (progress: number) => void;
        /** 向调用者提供终止上传的方法 */
        onProvideCancel?: (cancel: () => void) => void;
      };
    };
  };
}

export enum PresentationMode {
  /** 正常 */
  normal = 'normal',
  /** 只读 (无工具栏、菜单栏、历史面板，不可编辑内容，指定了某些特殊 CSS 样式) */
  readonly = 'readonly',
  /** 预览（可作为 iframe 受控视图展示，指定了某些特殊 CSS 样式，其他同只读） */
  preview = 'preview',
  /** 演示 (远程演示或演讲者视图客户端，指定了某些特殊 CSS 样式) */
  slideshow = 'slideshow',
}

export interface PresentationModeParams {
  /** 远程演示的文件 GUID */
  fileGuid?: string;
  /** 远程演示的演示 GUID */
  liveGuid?: string;
}

interface DocxEditorOptions {
  file: File;
  user: User;
  /**
   * 修改打开 URL 的行为
   */
  openHyperLinkCallback?: (url: string) => any;
  permissions: FilePermissions;
}

export declare class DocxEditor extends BaseEditor {
  collab: {
    clientId: string;
  };

  editor: any;

  docsApi: {
    attachEvent: (event: string, cb: (...args: any[]) => any) => void;
    [key: string]: (...args: any[]) => any;
  };

  attachEvent?: (event: string, cb: (...args: any[]) => any) => void;

  render: (elm: HTMLElement) => Promise<null>;

  revisions: {
    show: () => void;
  };

  constructor(options: DocxEditorOptions);
}

export interface SDKDocLaunchOptions {
  file: File;
  user?: User;
  readOnly?: boolean;
  container?: HTMLElement | string;
  scrollingContainer?: HTMLElement | string;
  uploadOrigin?: string;
  uploader?: {
    host: string;
    type: string;
    tokenPath: string;
  };
  isOffline?: boolean;
  locale?: string;
  socket: SocketIOClient.Socket;
  watermarkStatus?: boolean;
  canUseTrack?: boolean;
  plugins?: {
    canva?: boolean;
    featureGuide?: boolean;
    dateMention?: boolean;
    scriptsStore?: boolean;
  };
}

export interface SDKDocLaunchResult {
  editor: DocEditor;
}

export declare class DocEditor extends BaseEditor {
  insertHyperLink: (link: string, heightRatio: number) => void;
}

export declare class TableEditor extends BaseEditor {}

export declare class FormEditor extends BaseEditor {}

interface File {
  content?: string;
  contentUrl: string;
  backupContentUrls?: { url: string; tag: string }[];
  head?: number;
  guid: string;
  id: number;
  name: string;
  permissions: FilePermissions;
  Permissions: CollabPermissions[];
  type: string;
  updatedUser: User;
  user: User;
  watermark: boolean;
}

/**
 * 当前用户对文档的权限信息
 */
interface FilePermissions {
  readable?: boolean;
  editable?: boolean;
  commentable?: boolean;
  manageable?: boolean;
  exitable?: boolean;
  exportable?: boolean;
  collaboratorManageable?: boolean;
  adminManageable?: boolean;
  outsiderAddable?: boolean;
  childFileCreatable?: boolean;
  shareModeManageable?: boolean;
  teamShareModeManageable?: boolean;
  copyable?: boolean;
  lockable?: boolean;
  unlockable?: boolean;
  removable?: boolean;
  downloadable?: boolean;
  moveable?: boolean;
  sheetLockable?: boolean;
  passwordShareable?: boolean;
  fileAuthSetable?: boolean;
  isTocEnable?: boolean;

  /* docx */

  /**
   * 此属性为离线客户端打开超链接开特例
   */
  isOfflineOpenHyperLink?: boolean;
  /**
   * 是否有水印
   */
  watermarkStatus?: boolean;
  /**
   * 「插入」-\>「通过网址上传」是否可见
   */
  canViewAddInternetImage?: boolean;
  /**
   * 是否支持协作
   */
  isCollaborate?: boolean;
  /**
   * 「保存模版」按钮是否可见
   */
  isSaveTemplate?: boolean;
  /**
   * 「保存版本」是否可见
   */
  canViewVersion?: boolean;
  /**
   * 查看历史版本」是否可见
   */
  canViewHistory?: boolean;
  /**
   * 「导出」按钮是否可见
   */
  canViewExport?: boolean;
  /**
   * 是否可以使用在线快捷键
   */
  canUseOnlineShortcut?: boolean;
  /**
   * 是否可以使用 \@ 功能
   */
  canUseAtMention?: boolean;

  /**
   * 是否开启埋点
   */
  canUseTrack?: boolean;

  /**
   * 是否开启预览模式
   */
  isPreview?: boolean;
}

/**
 * 协作者权限信息
 */
interface CollabPermissions {
  id: number | null;
  owner: boolean;
  role: string;
}

interface User {
  avatar: string;
  email: string;
  id: number;
  name: string;
  teamId: number;
}

// eslint-disable-next-line no-undef
export { File, FilePermissions, User };

export enum ReadyState {
  LoadingEditor = 'loadingEditor',
  Failed = 'failed',
  Ready = 'ready',
}

export enum URLSharingType {
  NewDoc = 'newDoc',
  Docx = 'docx',
  Sheet = 'sheet',
  Form = 'form',
  FormPreview = 'formPreview' /* 表单预览页 */,
  FormFill = 'formFill' /* 表单填写页 */,
  PPT = 'ppt',
  Table = 'table',
}

/**
 * 生成 URL 时的额外信息。
 */
export interface URLInfo {
  /**
   * 生成 URL 时对应的分享文本
   */
  sharingText: string;

  /**
   * 生成 URL 时对应的类型，比如表单填写页
   */
  sharingType: URLSharingType;
}

/**
 * 对传入的 StartParams 进行序列化，并生成 url，url 主体由客户侧处理，比如 http://provider-domain/prodiver-file-id?startParams=startParams
 */
export type URLGenerator = (params: StartParams, info: URLInfo) => Promise<string | undefined>;

/**
 * 对传入的 URL 进行反序列化，返回一个 StartParams 给套件 SDK 使用。
 */
export type URLParser = (url: string) => Promise<StartParams | undefined>;

export type LinkOpener = (url: string, target?: string) => Promise<void>;

export type MentionClickHandlerForMobile = (payload: unknown) => void;

/**
 * 图片附件解密函数
 */
export type AttachDecrypter = (data: ArrayBuffer) => ArrayBuffer;

export interface RuntimeEnv {
  [key: string]: unknown;
  API_PATH: string;
  SDK_ENV: boolean;
  SDK_V2_HOST: string;
  SDK_V2_PATH_PREFIX: string;
  SDK_V2_SIGNATURE: string;
  SDK_V2_TOKEN: string;
  UPLOADER_HOST: string;
  /**
   * API 请求 prefix，比如 "x-sm-"
   */
  SDK_V2_HEADER_PREFIX: string;

  /**
   * 自定义 API Adaptor 的函数字符串，一般由客户提供
   */
  SDK_V2_API_ADAPTOR?: string;

  /**
   * 自定义 API Adaptor 的上下文数据 JSON 序列化后的字符串，一般由客户提供
   */
  SDK_V2_API_ADAPTOR_CONTEXT?: string;

  /**
   * 是否启用 service worker
   */
  SDK_v2_SW?: boolean;

  /**
   * 一般是 /sdk/v2
   */
  SDK_V2_ROOT_PATH: string;

  /**
   * 获取内容的 header 前缀
   */
  CONTENT_HEADER_META_PREFIX: string;
}

export interface ContainerRect {
  viewportWidth: number;
  viewportHeight: number;
  top: number;
  left: number;
  bottom: number;
  right: number;
  scrollTop: number;
}

export interface FileBaseItem {
  guid: string;
  providerId: string;
  type: 'docs' | 'docx' | 'sheets' | 'presentation' | 'table' | 'form';
}

interface SDKCollaborationOptions {
  // 初始化数据由外部传入
  content?: {
    data: string;
  };
  // 协作相关的信息
  collaboration?: {
    head?: number;
    // 是否开启了离线编辑
    offlineEditable: boolean;
    // 保存状态代理
    saveStatus: SaveStatusDelegation;
    // 离线同步状态代理
    syncStatus: SyncStatusDelegation;
  };
}

interface CreateSDK2BaseOptions {
  file: File;
  /** 是plain object，不是 ImmutableObject，没有 toJS() 方法 */
  user: User;

  /** 实际类型为 SocketIO.Client */
  socket: unknown;

  /** 目标容器，套件应直接渲染至容器，不需要后续调用 setContent/mount等方法 */
  container: HTMLElement;

  /** 表单类型 */
  formType?: string;

  encodeStartParams?: (params: StartParams) => string;

  /** 可能启用或禁用的插件，各套件不同 */
  plugins?: Record<string, boolean>;

  /**
   * 用于修改sdk发出的API请求，用于鉴权等目的，套件应使用此方法修改所有发出的请求
   *
   * 可以考虑用 axios 的 interceptors 实现：https://github.com/axios/axios#interceptors
   */
  apiAdaptor: typeof adaptor;

  startParams?: StartParams;

  generateUrl: URLGenerator;

  getInnerUrl: (params: StartParams) => Promise<string>;

  parseUrl: URLParser;

  overrideOpenLink?: LinkOpener;
  showToast?: (options: ShowToastOptions) => Promise<void>;

  disableMentionCards?: DisableMentionCards;

  /**
   * 处理移动端 \@ 点击事件
   */
  mentionClickHandlerForMobile?: MentionClickHandlerForMobile;

  /**
   * 是否禁用协作（禁用协作插件和协作者插件并禁止编辑），默认开启这两个插件（不检查任何文件权限/是否是协作者等）
   */
  disableCollaborate?: boolean;

  /**
   * 获取容器元素的宽高、viewport 信息
   */
  getContainerRect?: () => Promise<ContainerRect>;

  /**
   * 用于记录性能时间时的回调
   * @param mark - 标记名
   */
  onPerformanceTiming?: (mark: string) => void;

  forceDeviceMode?: DeviceMode;

  /**
   * 渲染UserCard
   */
  renderUserCard: (option: RenderUserCardProps) => void;

  /**
   * 渲染通知条
   */
  renderNotificationBar: (props: RenderNotificationBarProps) => void;

  /**
   * 检测该功能是否有权限的函数
   */
  checkPermission: CheckPermissionType;
  getBrandConfig: () => Promise<null | BrandType>;
}

/**
 * 表格初始化参数，移动端也一样
 */
interface CreateSheetOptions extends CreateSDK2BaseOptions, SDKCollaborationOptions {
  attachDecrypter?: AttachDecrypter;
  checkCrossTableReference: () => Promise<null | AttachLimitType>;
}

interface CreateTableOptions extends CreateSDK2BaseOptions {
  attachDecrypter?: AttachDecrypter;
}

/**
 * 轻文档初始化参数
 */
interface CreateDocumentOptions extends CreateSDK2BaseOptions, SDKCollaborationOptions {}

/**
 * 幻灯片初始化参数
 */
interface CreatePresentationOptions extends CreateSDK2BaseOptions, SDKCollaborationOptions {}

/**
 * 传统文档初始化参数
 */
interface CreateDocumentProOptions extends CreateSDK2BaseOptions, SDKCollaborationOptions {
  /**
   * 是否禁用默认的签名插件。在和中石油墨水屏合作时，由外部客户端实现签名组件，因此需要禁用默认的签名插件。
   */
  disableSignatureComponent?: boolean;
}

export interface RenderNotificationBarProps {
  dom: HTMLDivElement;
  user: User;
  file: File;
}

export const API_PATH = Object.freeze({
  BATCH_GET_USER_IDS: '/users/batch/getIds',
  BATCH_CONVERT_FILE_IDS: '/files/batch/convert-ids',
  BATCH_GET_FILE_BASES: '/files/batch/file-bases',
});

export const START_PARAMS_KEY = 'smParams';
