import type { ContentData } from '@shimo/core-collaboration';
import { Content } from '@shimo/core-collaboration';
import { Preference } from '@shimo/core-collaboration/preference';

import { getString } from '../env';
import type { File } from './types';

declare global {
  interface Window {
    __CONTENT_URL__?: {
      url: string;
      blocks: null; // 这里暂时没用，是用来处理表格工作表拆分的，但貌似还没完成
    };
    // ___DOC_CONTENT_PROMISE__  在专业文档、轻文档的 entry 文件中有实现
    ___DOC_CONTENT_PROMISE__?: Promise<ContentData>;
  }
}

function parseContent(response: { data: string; getHeader: (name: string) => string | null }): ContentData {
  const metaPrefix: string = getString('CONTENT_HEADER_META_PREFIX') || 'x-amz-meta-';

  const head = Number(response.getHeader(`${metaPrefix}head`));
  const length = Number(response.getHeader(`${metaPrefix}length`));

  const content: string = response.data;

  return {
    head,
    pool: content.slice(length),
    content: content.slice(0, length),
  };
}

function createContentFetcher(): Content {
  return new Content({
    preference: new Preference({
      name: 'content',
    }),
    logger: {
      // TODO: 接入日志
      delegation: console,
    },
    parser: {
      parse: parseContent,
    },
  });
}

export async function fetchContent(file: File): Promise<{
  head?: number;
  content: string;
  pool?: string;
}> {
  if (window.___DOC_CONTENT_PROMISE__ && window.___DOC_CONTENT_PROMISE__ instanceof Promise) {
    return await window.___DOC_CONTENT_PROMISE__;
  }

  const fetcher = createContentFetcher();

  if (typeof window.__CONTENT_URL__?.url === 'string') {
    return await fetcher.fetchUrl(window.__CONTENT_URL__.url);
  }

  const { contentUrl, backupContentUrls } = file;

  if (backupContentUrls && backupContentUrls.length > 0) {
    return await fetcher.fetchList([{ url: contentUrl, tag: 'default' }, ...backupContentUrls]);
  }

  return await fetcher.fetchUrl(contentUrl);
}
