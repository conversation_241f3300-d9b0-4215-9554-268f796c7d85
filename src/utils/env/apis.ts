// import { apis as ClientApi } from '@shimo/sdk2-api/output/api_definitions_pruned.json';
// FIXME: 这里本来是@shimo/sdk2-api/output/api_definitions_pruned.json的内容，但是这种导入方式会导致编译报错，先写在这把流程跑通
export const ClientApi = [
  {
    sdk: 'all',
    methods: [
      {
        method: 'createSDK2',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'file',
            required: 'Y',
            'param type': 'File',
          },
          {
            'param name': 'user',
            required: 'Y',
            'param type': 'User',
          },
          {
            'param name': 'socket',
            required: 'Y',
            'param type': 'unknown',
          },
          {
            'param name': 'plugins',
            'param type': 'Plugins',
          },
          {
            'param name': 'apiAdaptor',
            required: 'Y',
            'param type': 'ApiAdaptor',
          },
          {
            'param name': 'container',
            required: 'Y',
            'param type': 'HTMLElement',
          },
          {
            'param name': 'disableCollaborate',
            'param type': 'boolean',
            default: 'false',
          },
          {
            'param name': 'startParams',
            'param type': 'StartParams',
          },
          {
            'param name': 'generateUrl',
            'param type': 'GenerateUrl',
          },
          {
            'param name': 'parseUrl',
            'param type': 'ParseUrl',
          },
          {
            'param name': 'overrideOpenLink',
            'param type': 'OpenUrl',
          },
          {
            'param name': 'disableMentionCards',
            'param type': 'DisableMentionCards',
          },
          {
            'param name': 'attachDecrypter',
            'param type': 'AttachDecrypter',
          },
          {
            'param name': 'mentionClickHandlerForMobile',
            'param type': 'MouseMovePayload',
          },
          {
            'param name': 'encodeStartParams',
            'param type': 'EncodeStartParams',
          },
          {
            'param name': 'getContainerRect',
            'param type': 'GetContainerRect',
          },
          {
            'param name': 'getInnerUrl',
            'param type': 'GetInnerURL',
          },
          {
            'param name': 'onPerformanceTiming',
            'param type': 'OnPerformanceTiming',
          },
        ],
      },
    ],
  },
  {
    sdk: 'document',
    methods: [
      {
        method: 'showHistory',
        'support mobile': 'N',
      },
      {
        method: 'hideHistory',
        'support mobile': 'N',
      },
      {
        method: 'showRevision',
        'support mobile': 'N',
      },
      {
        method: 'hideRevision',
        'support mobile': 'N',
      },
      {
        method: 'showDiscussion',
        'support mobile': 'N',
      },
      {
        method: 'hideDiscussion',
        'support mobile': 'N',
      },
      {
        method: 'showComments',
        'support mobile': 'N',
      },
      {
        method: 'hideComments',
        'support mobile': 'N',
      },
      {
        method: 'showToc',
        'support mobile': 'N',
      },
      {
        method: 'hideToc',
        'support mobile': 'N',
      },
      {
        method: 'createRevision',
        'support mobile': 'N',
        returns: [],
      },
      {
        method: 'startDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'endDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'print',
        'support mobile': 'N',
      },
      {
        method: 'setTitle',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'value',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'getTitle',
        'support mobile': 'Y',
      },
      {
        method: 'insertExternalApp',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'url',
            required: 'Y',
            'param type': 'string',
          },
        ],
        returns: [
          {
            'param name': 'url',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
    ],
  },
  {
    sdk: 'documentPro',
    methods: [
      {
        method: 'getComments',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'includeChapterTitle',
            'param type': 'boolean',
            default: 'false',
          },
        ],
        returns: [
          {
            'param name': 'includeChapterTitle',
            'param type': 'boolean',
            default: 'false',
          },
        ],
      },
      {
        method: 'getComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'includeChapterTitle',
            'param type': 'boolean',
          },
        ],
        returns: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'includeChapterTitle',
            'param type': 'boolean',
          },
        ],
      },
      {
        method: 'getCommentBySelection',
        'support mobile': 'Y',
        returns: [],
      },
      {
        method: 'addComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'text',
            required: 'Y',
            'param type': 'string',
          },
        ],
        returns: [
          {
            'param name': 'text',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'replyComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'text',
            required: 'Y',
            'param type': 'string',
          },
        ],
        returns: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'text',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'removeComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'removeReply',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentDataId',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'updateComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentDataId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'text',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'goToComment',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentId',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'shouldScrollToComment',
            'param type': 'boolean',
            default: 'true',
          },
          {
            'param name': 'shouldSelectComment',
            'param type': 'boolean',
            default: 'false',
          },
        ],
      },
      {
        method: 'getSelectedText',
        'support mobile': 'Y',
        returns: [],
      },
      {
        method: 'goToPage',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'pageNum',
            required: 'Y',
            'param type': 'number',
          },
        ],
      },
      {
        method: 'goToTop',
        'support mobile': 'Y',
      },
      {
        method: 'addPageNum',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'position',
            required: 'Y',
            'param type': 'documentPro.DocumentPos',
          },
          {
            'param name': 'alignment',
            'param type': 'documentPro.HorizontalAlignment',
            default: '1',
          },
        ],
      },
      {
        method: 'removeAllPageNums',
        'support mobile': 'Y',
      },
      {
        method: 'showToc',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'shouldDisableCache',
            'param type': 'boolean',
            default: 'true',
          },
          {
            'param name': 'collapsedLevel',
            'param type': 'number',
          },
          {
            'param name': 'itemHeight',
            'param type': 'number',
          },
        ],
      },
      {
        method: 'hideToc',
        'support mobile': 'Y',
      },
      {
        method: 'zoom',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'percent',
            required: 'Y',
            'param type': 'number',
          },
        ],
      },
      {
        method: 'setBold',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'isBold',
            'param type': 'boolean',
            default: 'false',
          },
        ],
      },
      {
        method: 'setItalic',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'isItalic',
            'param type': 'boolean',
            default: 'false',
          },
        ],
      },
      {
        method: 'print',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'pageNums',
            required: 'Y',
            'param type': 'number[]',
          },
        ],
      },
      {
        method: 'printAll',
        'support mobile': 'Y',
      },
      {
        method: 'createRevision',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'name',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'showHistory',
        'support mobile': 'Y',
      },
      {
        method: 'hideHistory',
        'support mobile': 'Y',
      },
      {
        method: 'hideCommentHighlight',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'commentIds',
            'param type': 'undefined | string[]',
            default: 'undefined',
          },
        ],
      },
      {
        method: 'getHeadingsByPosition',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'y',
            'param type': 'number',
            default: '当前滚动条所在的文档实际位置',
          },
        ],
        returns: [
          {
            'param name': 'y',
            'param type': 'number',
            default: '当前滚动条所在的文档实际位置',
          },
        ],
      },
    ],
  },
  {
    sdk: 'sheet',
    methods: [
      {
        method: 'showComments',
        'support mobile': 'N',
      },
      {
        method: 'hideComments',
        'support mobile': 'N',
      },
      {
        method: 'showHistory',
        'support mobile': 'N',
      },
      {
        method: 'hideHistory',
        'support mobile': 'N',
      },
      {
        method: 'showLocks',
        'support mobile': 'N',
      },
      {
        method: 'hideLocks',
        'support mobile': 'N',
      },
      {
        method: 'createRevision',
        'support mobile': 'N',
      },
      {
        method: 'startDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'endDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'print',
        'support mobile': 'N',
      },
      {
        method: 'addRangeLock',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'userPermissions',
            required: 'Y',
            'param type': 'UserPermission',
          },
          {
            'param name': 'ranges',
            'param type': 'Range[]',
            default: '默认当前选中区域范围',
          },
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'description',
            'param type': 'string',
          },
          {
            'param name': 'departmentPermissions',
            'param type': 'DepartmentPermission',
          },
          {
            'param name': 'visitorPermission',
            'param type': 'PermissionLevel',
            default: 1,
          },
        ],
      },
      {
        method: 'addSheetLock',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'userPermissions',
            required: 'Y',
            'param type': 'UserPermission',
          },
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'description',
            'param type': 'string',
          },
          {
            'param name': 'departmentPermissions',
            'param type': 'DepartmentPermission',
          },
          {
            'param name': 'visitorPermission',
            'param type': 'PermissionLevel',
            default: 1,
          },
        ],
      },
      {
        method: 'removeRangeLocksInRanges',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'ranges',
            'param type': 'Range[]',
            default: '默认当前选中区域范围',
          },
        ],
      },
      {
        method: 'removeSheetLock',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
      },
      {
        method: 'getActiveSheetId',
        'support mobile': 'Y',
        returns: [],
      },
      {
        method: 'getSheetIds',
        'support mobile': 'Y',
        returns: [],
      },
      {
        method: 'getSheetIdByIndex',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'index',
            required: 'Y',
            'param type': 'number',
          },
        ],
        returns: [
          {
            'param name': 'index',
            required: 'Y',
            'param type': 'number',
          },
        ],
      },
      {
        method: 'getRangeValues',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'range',
            'param type': 'Range[]',
            default: '默认当前选中范围',
          },
        ],
        returns: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'range',
            'param type': 'Range[]',
            default: '默认当前选中范围',
          },
        ],
      },
      {
        method: 'getCellValue',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'row',
            required: 'Y',
            'param type': 'number',
          },
          {
            'param name': 'column',
            required: 'Y',
            'param type': 'number',
          },
        ],
        returns: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
          {
            'param name': 'row',
            required: 'Y',
            'param type': 'number',
          },
          {
            'param name': 'column',
            required: 'Y',
            'param type': 'number',
          },
        ],
      },
      {
        method: 'getRowCount',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
        returns: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
      },
      {
        method: 'getColumnCount',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
        returns: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
      },
      {
        method: 'isSheetVisible',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
        returns: [
          {
            'param name': 'sheetId',
            'param type': 'string',
            default: '默认当前工作表id',
          },
        ],
      },
      {
        method: 'setTitle',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'title',
            required: 'Y',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'updateRuntimeEnv',
        'support mobile': 'Y',
        params: [
          {
            'param name': 'env',
            required: 'Y',
            'param type': '{ [key: string]: any }',
          },
        ],
      },
      {
        method: 'setContent',
        'support mobile': 'N',
        params: [
          {
            'param name': 'content',
            required: 'Y',
            'param type': 'any',
          },
        ],
      },
      {
        method: 'setFocus',
        'support mobile': 'N',
        params: [
          {
            'param name': 'isFocus',
            'param type': 'boolean',
            default: 'true',
          },
        ],
      },
    ],
  },
  {
    sdk: 'presentation',
    methods: [
      {
        method: 'showHistory',
        'support mobile': 'N',
      },
      {
        method: 'hideHistory',
        'support mobile': 'N',
      },
      {
        method: 'startDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'endDemonstration',
        'support mobile': 'N',
      },
      {
        method: 'print',
        'support mobile': 'N',
      },
      {
        method: 'setContent',
        'support mobile': 'N',
        params: [
          {
            'param name': 'content',
            required: 'Y',
            'param type': 'any',
          },
        ],
      },
    ],
  },
  {
    sdk: 'table',
    methods: [
      {
        method: 'showRevision',
        'support mobile': 'N',
      },
      {
        method: 'hideRevision',
        'support mobile': 'N',
      },
      {
        method: 'createRevision',
        'support mobile': 'N',
      },
      {
        method: 'showRecordEditorDialog',
        'support mobile': 'N',
        params: [
          {
            'param name': 'fileGuid',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'tableGuid',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'recordGuid',
            required: 'Y',
            'param type': 'string',
          },
          {
            'param name': 'viewGuid',
            'param type': 'string',
          },
        ],
      },
      {
        method: 'showGlobalFieldDialog',
        'support mobile': 'N',
      },
    ],
  },
];
