import { assign } from 'lodash';

import { ClientApi } from './apis';

const clientApiMap = ClientApi?.reduce(
  (
    p: {
      [key: string]: { [key: string]: { 'support mobile': 'Y' | 'N' } };
    },
    e: any,
  ) => {
    p[e.sdk] = e.methods.reduce((p2: { [key: string]: object }, e2: any) => {
      p2[e2.method] = e2;
      return p2;
    }, {});
    return p;
  },
  {},
);
/* eslint-enable no-param-reassign */

/**
 * 用于获取环境变量
 */
export function getString(key: string): string {
  const value = window?.__RUNTIME_ENV__?.[key];
  if (value === null || value === undefined) {
    return '';
  }

  return String(value);
}

/**
 * 设置环境变量
 *
 * @param key - 目标环境变量
 * @param value - 值
 */
export function setEnv(key: string, value: unknown): void {
  window.__RUNTIME_ENV__ = assign({}, window.__RUNTIME_ENV__, { [key]: value });
}

export function getBoolean(key: string): boolean {
  return window.__RUNTIME_ENV__[key] === true;
}

export function getAPIPrefix(): string {
  return window.__RUNTIME_ENV__.SDK_V2_PATH_PREFIX;
}

/**
 * 获取环境变量：WEBSOCKET_HOST
 * 桌面客户端返回 process.env.APP_WS
 */
export function getSocketHost(): string {
  return getString('SDK_WEBSOCKET_HOST') || getString('WEBSOCKET_HOST');
}

/**
 * 支持 websocket 配置 path
 */
export function getSocketPath(): string {
  return getString('SDK_WEBSOCKET_PATH') || getString('WEBSOCKET_PATH');
}

/**
 *  获取环境变量: IS_MOBILE
 */
export function isMobile(): boolean {
  return getBoolean('IS_MOBILE');
}

/**
 *  判断 editor method 是否允许执行
 */
export function allowed(fileType: string, method: string): boolean {
  const m = clientApiMap[fileType]?.[method] || clientApiMap.all?.[method];
  if (isMobile() && !!m && m['support mobile'] !== 'Y') {
    return false;
  }
  return true;
}

/**
 * API 请求 prefix，比如 "x-sm-"
 */
export function getHeaderPrefix(): string {
  const PREFIX_KEY = 'SDK_V2_HEADER_PREFIX';
  let prefix = getString(PREFIX_KEY);

  if (!prefix) {
    prefix = atob('eC1zaGltby0=');
    window.__RUNTIME_ENV__ = {
      ...window.__RUNTIME_ENV__,
      [PREFIX_KEY]: prefix,
    };
  }

  return prefix;
}
