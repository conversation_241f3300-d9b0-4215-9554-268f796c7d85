import { renderTeamSeatDepletedTipModal } from './render';

export function teamSeatDepletedListener(socket?: unknown) {
  // @ts-expect-error
  socket?.on?.(
    'message',
    (params?: {
      type: string;
      data: {
        isDisable: boolean;
      };
    }) => {
      const { type, data } = params ?? {};
      if (type === 'LICENSE') {
        const { isDisable } = data ?? {};
        if (isDisable) {
          renderTeamSeatDepletedTipModal();
        }
      }
    },
  );
}
