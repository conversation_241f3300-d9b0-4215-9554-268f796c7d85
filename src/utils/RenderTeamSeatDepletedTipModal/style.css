.team-seat-depleted-tip-modal-mask {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(55, 55, 55, 60%);
  z-index: 999;
  overflow: hidden;
}

.team-seat-depleted-tip-modal {
  box-sizing: border-box;
  padding: 30px 40px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 10%) 0 2px 3px 1px;
  border-radius: 2px;
  width: 460px;
}

.team-seat-depleted-tip-modal-text {
  box-sizing: border-box;
  margin-bottom: 30px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.7;
  color: var(--sm-color-modal-body);
}

.team-seat-depleted-tip-modal-footer {
  box-sizing: border-box;
  text-align: right;
}

.team-seat-depleted-tip-modal-button {
  box-sizing: border-box;
  display: inline-flex;
  height: 32px;
  padding: 0 18px;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  border: 1px solid rgba(65, 70, 75, 10%);
  background: linear-gradient(0deg, #2c3033 -0.09%, #54585d 100%);
  color: #fff;
  cursor: pointer;
}

.team-seat-depleted-tip-modal-button:hover {
  background: rgba(65, 70, 75, 80%);
}

.team-seat-depleted-tip-modal-button:active {
  background: #2c3033;
}
