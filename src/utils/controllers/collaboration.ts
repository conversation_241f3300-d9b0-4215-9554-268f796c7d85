/* eslint-disable no-void */
import type { SaveStatus, SyncStatus } from '@shimo/core-collaboration';
import { SaveFailedErrorCode, SaveStatusState, SyncStatusState } from '@shimo/core-collaboration';
import { message, Modal } from 'antd';
import { InvokeMethod } from 'shimo-js-sdk-shared';

import { fm2 } from '@/modules/Locale';
import { editorStore } from '@/store/Editor';

import type { ShimoSDK } from '../ShimoSDK';

export const handleSaveStatus = (status: SaveStatus, sdk: ShimoSDK) => {
  status.addChangeStateListener((state: any) => {
    const store = editorStore.getState();
    store.update({ saveStatusType: state });
    switch (state) {
      case SaveStatusState.ONLINE_SAVING:
      case SaveStatusState.OFFLINE_SAVING: {
        void sdk.invokeChannelMethod(InvokeMethod.DispatchEditorEvent, 'saveStatusChanged', {
          status: 'saving',
        });
        break;
      }
      case SaveStatusState.SAVE_SUCCEED: {
        void sdk.invokeChannelMethod(InvokeMethod.DispatchEditorEvent, 'saveStatusChanged', {
          status: 'saved',
        });
        break;
      }
      case SaveStatusState.SAVE_FAILED: {
        void sdk.invokeChannelMethod(InvokeMethod.DispatchEditorEvent, 'saveStatusChanged', {
          status: 'error',
        });
        break;
      }
      default: {
        // never
      }
    }
  });
  status.addErrorListener((error: { code: unknown }) => {
    void sdk.invokeChannelMethod(InvokeMethod.DispatchEditorEvent, 'saveStatusChanged', {
      status: 'error',
    });
    if (error.code === SaveFailedErrorCode.STATUS_FORBIDDEN) {
      Modal.error({
        title: fm2('Editor.noEditing'),
        content: fm2('Editor.noEditingContent'),
      });
    } else if (error.code === SaveFailedErrorCode.STATUS_NOT_FOUND) {
      Modal.error({
        title: fm2('Editor.sorry'),
        content: fm2('Editor.fileDeleted'),
      });
    } else {
      Modal.error({
        title: fm2('Editor.saveFailed'),
        content: fm2('Editor.saveFailedContent'),
      });
    }
  });
};

export const handleSyncStatus = (status: SyncStatus) => {
  status.addChangeStateListener((state: any) => {
    if (state === SyncStatusState.SYNC_SAVING) {
      message.info(fm2('Editor.syncSaving'));
    } else if (state === SyncStatusState.SYNC_SUCCEED) {
      message.success(fm2('Editor.syncSucceed'));
    } else if (state === SyncStatusState.SYNC_FAILED) {
      message.error(fm2('Editor.syncFailed'));
    }
    const store = editorStore.getState();
    store.update({ saveStatusType: state });
  });
  status.addErrorListener((error: any) => {
    console.log('sync error', error);
  });
};
