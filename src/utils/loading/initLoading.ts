import type { EditorLoading } from '@shimo/editor-registry';
import { getRegistry } from '@shimo/editor-registry';
import type ReactType from 'react';

declare global {
  interface Window {
    __SM_PV__?: {
      setLoaded: () => void;
    };
  }
}

function setPageLoaded() {
  try {
    // 调用大预览顶层的窗口环境中（lizard-one 环境）的接口，通知大预览页面加载结束
    window.parent?.__SM_PV__?.setLoaded?.();
  } catch (err: unknown) {
    // nothing
  }
}

function getEditorLoading(React: typeof ReactType): EditorLoading {
  return ({ loading }): null => {
    const loadingRef = React.useRef(loading);
    loadingRef.current = loading;

    React.useEffect(() => {
      return () => {
        setPageLoaded();
      };
    }, []);

    React.useEffect(() => {
      if (loading === false) {
        setPageLoaded();
      }
    }, [loading]);

    return null;
  };
}

// 在没有外部注入的 EditorLoading 的环境中注入一个默认的 EditorLoading
// 该组件在 unmount 或 props.loading=false 时调用 window.top. 的
export function initPreviewLoading(): boolean {
  if (!getRegistry().isDefined('EditorLoading')) {
    getRegistry().inject('EditorLoading', (React: any) => {
      return getEditorLoading(React);
    });
    return true;
  }

  return false;
}
