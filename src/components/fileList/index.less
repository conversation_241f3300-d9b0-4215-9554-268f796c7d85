.mainCardTable {
  background: var(--theme-layout-color-bg-new-page);
  width: 100%;

  :global {
    .ant-card-body {
      padding: 1px 40px;
    }

    .breadcrumb-ellipsis {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 400px;
      min-width: 20px;
      white-space: nowrap;
      padding-right: 4px;
    }

    .ant-card-head {
      border-bottom: 1px solid var(--theme-box-shadow-color-level10);

      .desktopCursor {
        cursor: pointer;
      }

      .checkedTotal {
        color: var(--theme-text-color-secondary);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }

      .ant-card-head-title {
        line-height: 32px;
        font-weight: 500;
        padding: 0 12px;
      }
    }

    .ant-table-row {
      &:hover {
        .more {
          visibility: visible;
        }
      }

      .fileInfo {
        overflow: hidden;
        display: flex;
        height: 34px;
        line-height: 34px;
        align-items: center;

        .ant-space-item {
          display: flex;
          align-items: center;
        }
      }

      .fileName {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--theme-brand-color);
        font-weight: 400;
        max-width: 400px;
        min-width: 20px;
        white-space: nowrap;
        font-size: 14px;
        padding: 0 4px;
        font-family: 'PingFang SC';

        &:hover {
          cursor: pointer;
          text-decoration: underline;
        }
      }

      .more {
        visibility: hidden;
      }

      .share-user {
        line-height: 28px;
        font-size: 13px;
        color: var(--theme-text-color-secondary);
      }
    }

    .ant-table-thead {
      .ant-table-cell {
        font-size: 12px;
        font-weight: 400;
        padding: 15px 10px;
      }
    }

    .ant-table-body {
      .ant-table-cell {
        padding: 10px 12px;
      }
    }
  }

  .spaceDropdown {
    width: 100px;
  }
}

.noData {
  text-align: center;

  .title {
    color: var(--theme-basic-color-primary);
    font-size: 28px;
    font-style: normal;
    font-weight: 500;
    line-height: 48px;
  }

  .description {
    width: 372px;
    color: var(--theme-basic-color-primary);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}
