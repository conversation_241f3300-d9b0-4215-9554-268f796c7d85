import { Space, Tooltip } from 'antd';

import { getFileIcon, openFile } from '@/utils/file';

export const FileName = ({ record, name, disabled }: { record: any; name: string; disabled?: boolean }) => {
  return (
    <Space align="start" className="fileInfo">
      <img height={20} src={getFileIcon({ type: record.type, isSpace: record.isSpace })} width={20} />
      <Tooltip placement="top" title={name}>
        <span className="fileName" onClick={() => openFile({ ...record, disabled })}>
          {name}
        </span>
      </Tooltip>
      {record.starred && <img height={20} src={getFileIcon({ type: 'favorites' })} width={20} />}
    </Space>
  );
};
