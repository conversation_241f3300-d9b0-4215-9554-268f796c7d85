import { Form, Input, message, Modal } from 'antd';
import _ from 'lodash';
import React, { useState } from 'react';

import { rename } from '@/api/File';
import { fm } from '@/modules/Locale';
import { validateUnlawful } from '@/utils/validator';

interface FormModalProps {
  visible: boolean;
  params: { fileName?: string; guid?: string };
  callback: ({ visible, refresh }: { visible: boolean; refresh?: boolean }) => void;
}

const RenameModal: React.FC<FormModalProps> = ({ visible, callback, params }) => {
  const i18nText = {
    success: fm('RenameModal.editSuccess'),
    error: fm('RenameModal.editError'),
    message: `${fm('RenameModal.validatorMessage')}: .\\/:*?"<>`,
  };
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [suffix, setSuffix] = useState<string | undefined>('');

  const handleOk = () => {
    form
      .validateFields()
      .then(() => {
        setLoading(true);
        rename(params.guid, `${inputValue}${suffix}`)
          .then((res) => {
            if (res.status === 200) {
              message.success(i18nText.success);
              callback({ visible: false, refresh: true });
            }
          })
          .catch((err) => {
            message.error(`${i18nText.error} : ${err.data?.msg}`);
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {
        message.error(i18nText.message);
      });
  };

  const onCancel = () => {
    callback({ visible: false, refresh: false });
  };

  const afterOpenChange = (open: boolean) => {
    if (open) {
      setSuffix('');
      const { fileName } = params;
      let newName = fileName;
      if (fileName) {
        const index = fileName.lastIndexOf('.');
        if (index >= 0) {
          newName = fileName.slice(0, index);
          const after = fileName.substring(index);
          setSuffix(after);
        }
      }
      setInputValue(newName || '');

      form.setFieldValue('name', newName);
    }
  };

  const inputHandleChange = (e: { target: { value: any } }) => {
    let value = e.target.value;
    // 去除首尾空格
    value = _.trim(value);
    // 将连续的空格缩成一个空格
    value = value.replace(/\s+/g, ' ');

    setInputValue(value);
  };

  return (
    <Modal
      destroyOnClose
      afterOpenChange={afterOpenChange}
      confirmLoading={loading}
      okButtonProps={{
        disabled: inputValue?.length < 1,
      }}
      open={visible}
      title={fm('RenameModal.title')}
      onCancel={onCancel}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label=""
          name="name"
          rules={[
            { required: true, message: fm('RenameModal.InputPlaceholder') },
            {
              validator(rule, value) {
                if (validateUnlawful(value)) {
                  return Promise.reject(new Error(i18nText.message));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
        >
          <Input
            maxLength={512}
            placeholder={fm('RenameModal.InputPlaceholder')}
            value={inputValue}
            onChange={inputHandleChange}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RenameModal;
