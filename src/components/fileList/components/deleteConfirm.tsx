import { message, Modal } from 'antd';
import type React from 'react';

const deleteConfirm = ({
  i18nText,
  callback,
  api,
  data,
  icon,
  closable = true,
  className = 'delete-confirm-modal',
}: {
  i18nText: any;
  callback: () => void;
  api: (key: any) => Promise<any>;
  data: any;
  icon?: React.ReactNode;
  closable?: boolean;
  className?: string;
}) => {
  Modal.confirm({
    title: i18nText.title,
    content: i18nText.content,
    okText: i18nText.okText,
    cancelText: i18nText.cancelText,
    centered: true,
    icon: icon || null,
    closable,
    className,
    onOk: () => {
      return new Promise((resolve, reject) => {
        api(data)
          .then((res) => {
            message.success(i18nText.success);
            resolve(res);
            callback();
          })
          .catch(() => {
            message.error(i18nText.error);
            reject();
          });
      });
    },
  });
};

export default deleteConfirm;
