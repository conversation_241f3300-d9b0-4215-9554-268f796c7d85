import { FilterOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Tooltip } from 'antd';
import { useState } from 'react';

import { fm } from '@/modules/Locale';

export const ListFilter = ({
  items,
  init,
  callback,
  text,
}: {
  items: (value: string) => MenuProps['items'];
  init: string;
  text?: string;
  callback: (key: any) => void;
}) => {
  const [checked, setChecked] = useState<any>(init);
  const [changed, setChanged] = useState<boolean>(false);
  const levelMenuProps = {
    items: items(checked),
    selectable: true,
    selectedKeys: [],
    defaultSelectedKeys: [init],
    onClick: ({ key }: { key: string }) => {
      setChecked(key === 'clear' ? init : key);
      callback(key === 'clear' ? init : key);
      setChanged(key !== 'clear');
    },
  };

  return (
    <Tooltip title={fm('File.setFilter')}>
      <Dropdown menu={levelMenuProps} trigger={['click']}>
        <Button icon={<FilterOutlined />} type="text">
          {changed && text}
        </Button>
      </Dropdown>
    </Tooltip>
  );
};
