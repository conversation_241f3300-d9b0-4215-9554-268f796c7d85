import { Empty, Space } from 'antd';
import type { ReactNode } from 'react';

import styles from '../index.less';

export const NoData = ({ title, description, img }: { title?: string; description?: string; img: ReactNode }) => {
  return (
    <Empty
      description={
        <Space className={styles['noData']} direction="vertical">
          <div className={styles['image']}>{img}</div>
          <span className={styles['title']}>{title}</span>
          <span className={styles['description']}>{description}</span>
        </Space>
      }
      image={false}
    />
  );
};
