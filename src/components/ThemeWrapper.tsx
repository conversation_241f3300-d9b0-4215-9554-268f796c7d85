import { useEffect } from 'react';

import { useInitTheme } from '@/hooks/Theme';
import { useThemeStore } from '@/store/Theme';

export default function ThemeWrapper({ children }: { children: React.ReactNode }) {
  // 初始化主题设置
  useInitTheme();
  const { isDark } = useThemeStore((state) => state.theme);

  useEffect(() => {
    if (isDark) {
      document.body.classList.add('drive-dark');
    } else {
      document.body.classList.remove('drive-dark');
    }
  }, [isDark]);

  return <>{children}</>;
}
