// FormModal.tsx
import { Form, Input, message, Modal } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';

import { fm } from '@/modules/Locale';
interface FormModalProps {
  visible: boolean;
  onOk: (name: string) => void;
  onCancel: () => void;
  okText?: string;
  cancelText?: string;
  initialValues?: any;
  title?: string;
  type: string;
  confirmLoading?: boolean;
}

export function CreateWithNamePop({ visible, onOk, onCancel, initialValues, type, confirmLoading }: FormModalProps) {
  const folderTitle = fm('CreateWithNamePop.folderTitle');
  const saveVersionTitle = fm('CreateWithNamePop.saveVersionTitle');
  const spaceTitle = fm('CreateWithNamePop.spaceTitle');
  const folderPlaceholder = fm('CreateWithNamePop.folderPlaceholder');
  const spacePlaceholder = fm('CreateWithNamePop.spacePlaceholder');
  const saveVersionPlaceholder = fm('CreateWithNamePop.saveVersionPlaceholder');
  const ruleMessage = fm('CreateWithNamePop.ruleMessage');

  const [title, setTitle] = useState('');
  const [placeholder, setPlaceholder] = useState('');

  useEffect(() => {
    if (type === 'folder') {
      setTitle(folderTitle);
      setPlaceholder(folderPlaceholder);
    } else if (type === 'docx') {
      setTitle(saveVersionTitle);
      setPlaceholder(saveVersionPlaceholder);
    } else {
      setTitle(spaceTitle);
      setPlaceholder(spacePlaceholder);
    }
  }, [type]);

  const [form] = Form.useForm();
  const [inputValue, setInputValue] = useState('');

  const handleOk = async () => {
    try {
      await form.validateFields();
      onOk(inputValue);
    } catch (error) {
      message.error(ruleMessage);
    }
  };

  const inputHandleChange = (e: { target: { value: any } }) => {
    let value = e.target.value;
    // 去除首尾空格
    value = _.trim(value);
    // 将连续的空格缩成一个空格
    value = value.replace(/\s+/g, ' ');
    setInputValue(value);
  };

  return (
    <Modal
      centered
      destroyOnClose
      confirmLoading={confirmLoading}
      okButtonProps={{ disabled: !inputValue.length }}
      open={visible}
      title={title}
      width={400}
      onCancel={onCancel}
      onOk={handleOk}
    >
      <Form form={form} initialValues={initialValues} layout="vertical" preserve={false}>
        <Form.Item label="" name="name" rules={[{ required: true, message: placeholder }]}>
          <Input maxLength={512} placeholder={placeholder} value={inputValue} onChange={inputHandleChange} />
        </Form.Item>
      </Form>
    </Modal>
  );
}
