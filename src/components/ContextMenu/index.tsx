import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import { createRoot } from 'react-dom/client';

import styles from './index.less';

let menuContainer: HTMLDivElement | null = null;
let root: ReturnType<typeof createRoot> | null = null;
let clickOutsideHandler: ((e: MouseEvent) => void) | null = null;

const itemHeight = 32;

const createContainer = () => {
  if (!menuContainer) {
    menuContainer = document.createElement('div');
    document.body.appendChild(menuContainer);
  }
  return menuContainer;
};

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

export const createMenu = {
  show: (config: { items: MenuItem[]; position: { x: number; y: number }; theme?: 'light' | 'dark' }) => {
    const container = createContainer();

    // 过滤隐藏项
    const filtrItems: MenuItem[] = [];
    config.items.forEach((item) => {
      if (item.hidden) return;
      if (item.children) {
        const visibleChildren = item.children.filter((child) => !child.hidden);
        if (visibleChildren.length > 0) {
          filtrItems.push({ ...item, children: visibleChildren });
        }
      } else {
        filtrItems.push(item);
      }
    });

    // 边界调整
    const adjustPosition = (x: number, y: number) => {
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const menuWidth = 200;
      const menuHeight = filtrItems.length * itemHeight;

      return {
        x: Math.max(0, Math.min(x, viewportWidth - menuWidth)),
        y: Math.max(0, Math.min(y, viewportHeight - menuHeight)),
      };
    };

    const { x, y } = adjustPosition(config.position.x, config.position.y);

    // 清理旧根和监听器
    if (root) root.unmount();
    if (clickOutsideHandler) {
      document.removeEventListener('click', clickOutsideHandler, { capture: true });
    }

    // 渲染菜单
    const MenuContent = () => (
      <div className={styles.menuList} style={{ left: x, top: y }}>
        <Menu items={filtrItems} mode="vertical" theme={config.theme || 'light'} onClick={createMenu.destroy} />
      </div>
    );

    root = createRoot(container);
    root.render(<MenuContent />);

    // 点击外部关闭
    clickOutsideHandler = (e: MouseEvent) => {
      if (!menuContainer?.contains(e.target as Node)) {
        setTimeout(() => {
          createMenu.destroy();
        }, 200); // 子菜单事件未触发就销毁
      }
    };
    document.addEventListener('click', clickOutsideHandler, { capture: true, once: true });
  },

  destroy: () => {
    if (clickOutsideHandler) {
      document.removeEventListener('click', clickOutsideHandler, { capture: true });
      clickOutsideHandler = null;
    }
    if (root) {
      root.unmount();
      root = null;
    }
    if (menuContainer) {
      document.body.removeChild(menuContainer);
      menuContainer = null;
    }
  },
};
