export interface Notification {
  id: string;
  createdAt: string;
  isRead: boolean;
  notification: {
    msg: string;
    fileName: string;
    msgType: number;
    user: {
      avatar: string;
      name: string;
      team: {
        id: number;
        name: string;
      };
    };
    file: {
      url: string;
    };
    createdAt: string;
    [key: string]: unknown;
  };
}
export type GroupedNotification = {
  createdAt: string;
  children: Notification[];
};
