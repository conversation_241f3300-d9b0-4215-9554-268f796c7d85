import { Avatar, Button, List, message, Space, Typography } from 'antd';

import { readAlon } from '@/api/Message';
import { fm } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import styles from '../index.less';
import type { Notification } from '../type';

export const MessageCard = ({ item, callback }: { item: Notification; callback: () => void }) => {
  const actionMap: { [key: number]: string } = {
    0: fm('MessageCenter.commented'), //'评论了',
    1: fm('MessageCenter.mentioned'), // '提到了你',
    2: fm('MessageCenter.addCollaborator'), // '添加你为协作者',
    3: fm('MessageCenter.setAdministrator'), // '将你设置为企业管理员',
    4: fm('MessageCenter.inviteJoinBusiness'), // '邀请你加入企业',
    5: fm('MessageCenter.newMembersJoin'), // '新成员加入',
    6: fm('MessageCenter.deleteDocument'), // '删除文档',
    7: fm('MessageCenter.remindsReviewTasks'), // '提醒你查看任务',
    8: fm('MessageCenter.liked'), // '点赞了',
    9: fm('MessageCenter.NotificationToDoChanges'), // '待办事项修改通知'：删除了你在
    10: fm('MessageCenter.mentionYou'), // '提到你',
    11: fm('MessageCenter.moveYouOfBusiness'), // '将你移出企业',
    12: fm('MessageCenter.handingOverBusinessToYou'), // '将企业移交给你',
    13: fm('MessageCenter.companyNameChanged', { name: null }), // '修改了企业名称“${name}”',
    14: fm('MessageCenter.openedBusinessLink'), // '打开企业邀请链接',
    15: fm('MessageCenter.closedBusinessLink'), // '关闭了企业邀请链接',
    16: fm('MessageCenter.taskReminders'), // '任务提醒',
    17: fm('MessageCenter.tableSelectionReminders'), // '表格选区提醒',
    18: fm('MessageCenter.changeUserConfig'), // '修改企业成员账号（邮箱，昵称，密码）',
    20: fm('MessageCenter.systemNotifications'), // '系统通知',
    21: fm('MessageCenter.application'), // '申请', //  申请权限
    // 22: '文件被恢复通知 ${昵称}恢复了${文件名称}',
    // 23: '用户1、用户2、用户3 等7人更新了文档「文档名称」',
    // 24: '表单通知',
    // 25: '配额报警通知',
    // 26: '会员提醒', // 例如会员到期续费提醒、文件协作达到上限升级账号提醒
    // 27: '文件的新增、修改、删除通知',
  };

  const i18nText = {
    markRead: fm('MessageCenter.markRead'),
    join: fm('MessageCenter.join'),
    discussion: fm('MessageCenter.discussion'),
    permissions: fm('MessageCenter.permissions'),
    dateArrived: fm('MessageCenter.dateArrived'),
  };
  const { formatTime } = useFormatTime();

  /** 谁 */
  const UserName = () => (
    <div className={styles.messageName}>
      <Typography.Text ellipsis={{ tooltip: item.notification.user.name }} style={{ maxWidth: 50 }}>
        {item.notification?.user.name}
      </Typography.Text>
    </div>
  );

  const readMessage = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    readAlon(id)
      .then(() => {
        callback();
      })
      .catch((err) => {
        message.error(err.data?.msg);
      });
  };

  return (
    <>
      {/* 评论了 */}
      {[0].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Paragraph ellipsis={{ rows: 2, tooltip: item.notification.msg }} style={{ width: '100%' }}>
                  {`“ ${item.notification.msg} ”`}
                </Typography.Paragraph>
              </div>
              <Space>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 50 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ maxWidth: 120 }}>
                  {`「${item.notification.fileName}」`}
                </Typography.Text>
              </div>
            </Space>
          }
        />
      ) : null}
      {/* 提到了 */}
      {[1].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.msg }} style={{ width: 120 }}>
                  {`「${item.notification.msg}」`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 100 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 添加你为协作者 */}
      {[2].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ width: 120 }}>
                  {`「${item.notification.fileName}」`}
                </Typography.Text>
              </div>
              <Space>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 100 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 企业邀请 */}
      {[4].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.user.team.name }} style={{ width: 120 }}>
                  {`「${item.notification.user.team.name}」`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                <Button size="small" type="text">
                  {i18nText.join}
                </Button>
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 100 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 提醒你查看任务 */}
      {[7].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ width: 160 }}>
                  {`「${item.notification.fileName}」`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 100 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 点赞 */}
      {[8].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.msg }} style={{ width: 120 }}>
                  {`”${item.notification.msg}”`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <>
              <Space>
                <UserName />
                <span className={styles.messageType}>
                  <Typography.Text
                    ellipsis={{ tooltip: actionMap[item.notification.msgType] }}
                    style={{ maxWidth: 50 }}
                  >
                    {actionMap[item.notification.msgType]}
                  </Typography.Text>
                </span>
              </Space>
              <Space>
                <div className={styles.messageTarget}>
                  <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ maxWidth: 100 }}>
                    {`「${item.notification.fileName}」`}
                  </Typography.Text>
                </div>
                <span className={styles.messageType}>
                  <Typography.Text style={{ width: 20 }}>{i18nText.discussion}</Typography.Text>
                </span>
              </Space>
            </>
          }
        />
      ) : null}
      {/* 删除了你在 */}
      {[9].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
                {item.isRead ? null : (
                  <Button size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
                    {i18nText.markRead}
                  </Button>
                )}
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: actionMap[item.notification.msgType] }} style={{ maxWidth: 65 }}>
                  {actionMap[item.notification.msgType]}
                </Typography.Text>
              </span>
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ maxWidth: 60 }}>
                  {`「${item.notification.fileName}」`}
                </Typography.Text>
              </div>
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: i18nText.dateArrived }} style={{ maxWidth: 40 }}>
                  {i18nText.dateArrived}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 申请权限 */}
      {[21].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space className={styles.messageHover}>
                <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <span className={styles.messageType}>
                <Typography.Text
                  ellipsis={{
                    tooltip: `${actionMap[item.notification.msgType]}「${item.notification.fileName}」${
                      i18nText.permissions
                    }`,
                  }}
                  style={{ maxWidth: 160 }}
                >
                  {`${actionMap[item.notification.msgType]}「${item.notification.fileName}」${i18nText.permissions}`}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
    </>
  );
};
