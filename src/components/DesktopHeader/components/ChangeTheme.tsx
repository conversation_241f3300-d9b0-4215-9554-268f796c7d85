import { BgColorsOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown } from 'antd';
import { useMemo } from 'react';

import { useRecordTheme } from '@/hooks/Theme';
import { useFormatMessage } from '@/modules/Locale';

export const ChangeTheme = () => {
  const { recordTheme } = useRecordTheme();

  const autoThemeSetting = useFormatMessage('Login.autoThemeSetting');
  const lightThemeSetting = useFormatMessage('Login.lightThemeSetting');
  const darkThemeSetting = useFormatMessage('Login.darkThemeSetting');

  const themeItems: MenuProps['items'] = useMemo(
    () => [
      {
        key: 'auto',
        label: autoThemeSetting,
        onClick: () => {
          recordTheme('System');
        },
      },
      {
        key: 'light',
        label: `☀️ ${lightThemeSetting}`,
        onClick: () => {
          recordTheme('Light');
        },
      },
      {
        key: 'dark',
        label: `🌙 ${darkThemeSetting}`,
        onClick: () => {
          recordTheme('Dark');
        },
      },
    ],
    [autoThemeSetting, lightThemeSetting, darkThemeSetting, recordTheme],
  );
  return (
    <Dropdown arrow menu={{ items: themeItems }} placement="bottomRight">
      <Button type="text">
        <BgColorsOutlined />
      </Button>
    </Dropdown>
  );
};
