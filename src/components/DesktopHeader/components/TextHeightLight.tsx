import { escapeRegExp } from 'lodash';
import React, { useMemo } from 'react';

type HighlightMode = 'backend' | 'frontend' | 'both';

interface HybridHighlighterProps {
  text: string;
  searchTerm?: string;
  mode?: HighlightMode;
  highlightClass?: string;
}

// 类型守卫：判断是否为 React 元素
const isReactElement = (item: any): item is JSX.Element => {
  return React.isValidElement(item);
};

export const HybridHighlighter = ({
  text,
  searchTerm = '',
  mode = 'both',
  highlightClass = 'highlight',
}: HybridHighlighterProps) => {
  // 处理后端高亮
  const backendHighlighted = useMemo(() => {
    if (!text) return [];
    const parts = text.split(/(<em>|<\/em>)/g).filter(Boolean);
    let depth = 0;
    const elements: (string | JSX.Element)[] = [];

    parts.forEach((part) => {
      if (part === '<em>') {
        depth++;
      } else if (part === '</em>') {
        depth = Math.max(0, depth - 1);
      } else {
        elements.push(
          depth > 0 ? (
            <span key={`be-${elements.length}`} className={highlightClass}>
              {part}
            </span>
          ) : (
            part
          ),
        );
      }
    });

    return elements;
  }, [text, highlightClass]);

  // 处理前端高亮（支持嵌套结构）
  const frontendHighlighted = useMemo(() => {
    if (!searchTerm.trim()) return backendHighlighted;

    const processNode = (node: string | JSX.Element): (string | JSX.Element)[] => {
      if (isReactElement(node)) {
        // 递归处理子元素
        const children = React.Children.map(node.props.children, (child) =>
          typeof child === 'string' ? processNode(child) : child,
        );
        return [React.cloneElement(node, {}, ...children)];
      }

      const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
      return String(node)
        .split(regex)
        .map((part, i) =>
          regex.test(part) ? (
            <span key={`fe-${i}`} className={highlightClass}>
              {part}
            </span>
          ) : (
            part
          ),
        );
    };

    return backendHighlighted.flatMap(processNode);
  }, [backendHighlighted, searchTerm, highlightClass]);

  // 最终渲染内容
  const renderContent = useMemo(() => {
    switch (mode) {
      case 'backend':
        return backendHighlighted;
      case 'frontend':
        return frontendHighlighted;
      case 'both':
      default:
        return frontendHighlighted;
    }
  }, [mode, backendHighlighted, frontendHighlighted]);

  return (
    <span>{renderContent.map((item, index) => (isReactElement(item) ? item : <span key={index}>{item}</span>))}</span>
  );
};
