.desktop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 16px;
  width: 100%;
  height: 100%;
}

.left-section {
  display: flex;
  position: relative;
  align-items: center;

  button {
    height: 28px !important;
  }

  .arrow-right-btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    width: 42px;

    &:hover {
      z-index: 1;
    }
  }

  .arrow-down-btn {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .plus-btn {
    margin-left: 10px;
  }

  .ipt {
    cursor: pointer;
    margin-left: 20px;
    box-shadow: none;
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    background-color: transparent;
    padding-right: 0;
    padding-left: 0;
    font-weight: 700;
    font-size: 14px;

    &:hover {
      box-shadow: none;
      border: none;
      border-bottom: 2px solid lightgray;
    }

    &:focus {
      box-shadow: none;
      border: none;
      border-bottom: 2px solid lightblue !important;
    }
  }

  // 隐藏测量元素样式
  .hidden-measure {
    position: absolute;
    top: 100%;
    left: 12px;
    visibility: hidden;
    font-weight: 700;
    font-size: 14px;
  }

  .title {
    margin-right: 12px;
    margin-left: 20px;
    font-weight: 700;
    font-size: 14px;
  }

  .collect-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 12px;
    width: 16px;
    height: 30px;

    svg {
      position: relative;
      top: 1px;
    }
  }

  .save-status {
    margin-left: 20px;
    color: rgba(0, 0, 0, 45%);
    font-size: 12px;
  }
}

.right-section {
  display: flex;
  align-items: center;

  button {
    height: 28px !important;
  }

  .team-btn {
    margin-left: 10px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    &:hover {
      z-index: 1;
    }
  }

  .share-btn {
    margin-right: 10px;
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
