import { Space, Switch } from 'antd';
import { Header } from 'antd/es/layout/layout';
import { history } from 'umi';

import { useRecordTheme } from '@/hooks/Theme';
import { useThemeStore } from '@/store/Theme';

import { MessageCenter } from './components/MessageCenter';
import { SearchCenter } from './components/SearchCenter';
import UserCenter from './components/UserCenter';
import styles from './index.less';

const DesktopHeader = () => {
  const headerName = '石墨文档';
  const theme = useThemeStore((state) => state.theme);
  const { recordTheme } = useRecordTheme();

  const goHome = () => {
    history.push('/recent');
  };

  return (
    <Header className={styles.headerContent}>
      <a className={styles.headerName} onClick={goHome}>
        {headerName}
      </a>
      <SearchCenter />
      <Space>
        <Switch onChange={() => recordTheme(theme.isDark ? 'Light' : 'Dark')} />
        <MessageCenter />
        <UserCenter />
      </Space>
    </Header>
  );
};

export default DesktopHeader;
