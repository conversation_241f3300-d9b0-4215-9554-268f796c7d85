import './index.less';

import { Breadcrumb } from 'antd';
import { memo } from 'react';

import type { BreadcrumbModel } from '@/model/Menus';

type Props = {
  items?: BreadcrumbModel[];
};
const BreadcrumbComponent = ({ items }: Props) => {
  return (
    <div className="breadcrumb">
      <Breadcrumb items={items} />
    </div>
  );
};
export const ManagementBreadcrumbComponent = memo(BreadcrumbComponent);
