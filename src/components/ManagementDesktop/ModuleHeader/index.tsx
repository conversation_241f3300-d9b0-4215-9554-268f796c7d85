import './index.less';

import type { ReactNode } from 'react';
import { memo } from 'react';

import useMenus from '@/hooks/useMenus';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { createPropsComparer } from '@/utils/createPropsComparer';

import { ManagementBreadcrumbComponent } from '../Breadcrumb';

interface ModuleHeaderProps {
  children?: ReactNode;
  isShowBreadcrumb?: boolean;
}

const compareProps = createPropsComparer<ModuleHeaderProps>(['isShowBreadcrumb']);

const ModuleHeader = memo(({ isShowBreadcrumb = false, children }: ModuleHeaderProps) => {
  const me = useMeStore((state) => state.me);
  const { selectedMenuItem, breadcrumbList } = useMenus();
  const title = selectedMenuItem.title;
  return (
    <div className={`management-header-container ${isShowBreadcrumb ? 'show-breadcrumb' : ''}`}>
      {isShowBreadcrumb && <ManagementBreadcrumbComponent items={breadcrumbList} />}
      <div className="header-text-container">
        <div>
          <span className="header-title">{title}</span>
          <span className="header-team-info">
            {me?.team?.name} / {$t('Management.businessID')} {me?.teamId}
          </span>
        </div>
        {children}
      </div>
    </div>
  );
}, compareProps);

ModuleHeader.displayName = 'ModuleHeader';

export { ModuleHeader };
