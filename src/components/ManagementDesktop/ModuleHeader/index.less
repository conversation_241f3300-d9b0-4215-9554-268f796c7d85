.management-header-container {
  padding: 16px 24px;
  box-sizing: border-box;
  width: 100%;
  background: var(--theme-layout-color-bg-new-page);
  height: 72px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  .header-text-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;

    .header-title {
      color: var(--theme-text-color-default);
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
    }

    .header-team-info {
      margin-left: 12px;
      color: var(--theme-text-color-secondary);
      font-size: 14px;
      line-height: 22px;
    }
  }
}

.show-breadcrumb {
  height: 102px;

  .header-text-container {
    height: auto;
  }
}
