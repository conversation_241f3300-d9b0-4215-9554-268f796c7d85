import { Space } from 'antd';
import { Header as LeftHeader } from 'antd/es/layout/layout';
import { history } from 'umi';

import styles from './index.less';
type Props = {
  title?: string;
};
export const Header = ({ title }: Props) => {
  const goDesktop = () => {
    history.push('/');
  };
  return (
    <LeftHeader className={styles.managementHeaderContent}>
      <Space align="center" className={styles.headerContent} size={20}>
        <div className={styles.headerName} onClick={goDesktop} />
        <div className={styles.headerSubName}>{title}</div>
      </Space>
    </LeftHeader>
  );
};
