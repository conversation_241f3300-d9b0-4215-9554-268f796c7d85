import { ReactComponent as DesktopIcon } from '@/assets/images/svg/desktop.svg';
import { ReactComponent as WorkbenchIcon } from '@/assets/images/svg/workbench.svg';
import { useFileIcon } from '@/hooks/FileIcon';
import { fm } from '@/modules/Locale';
import type { FileDetail } from '@/types/api';

import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; fileType?: string; fileGuid?: string }) => void;
  recentFiles?: FileDetail[];
}

export function BackToPopover({ onItemClick, recentFiles = [] }: PopoverProps) {
  const { getFileIcon } = useFileIcon();

  function handleItemClick(item: { key: string; fileType?: string; fileGuid?: string }) {
    onItemClick?.(item);
  }

  return (
    <div className={css.container}>
      {/* 暂时注释搜索框，后面要用到 */}
      {/* <div className={css.section}>
        <Input placeholder={fm('BackToPopover.searchFiles')} allowClear prefix={<SearchOutlined />} />
      </div>
      <div className={css.divider}></div> */}

      <div className={css.section}>
        <div className={css.sectionTitle}>{fm('BackToPopover.backTo')}</div>
        <div className={css.item} onClick={() => handleItemClick({ key: 'myDesktop' })}>
          <DesktopIcon className={css.icon} />
          <span>{fm('BackToPopover.myDesktop')}</span>
        </div>
        <div className={css.item} onClick={() => handleItemClick({ key: 'workbench' })}>
          <WorkbenchIcon className={css.icon} />
          <span>{fm('BackToPopover.workbench')}</span>
        </div>
      </div>
      <div className={css.section}>
        <div className={css.sectionTitle}>{fm('BackToPopover.recentlyUsed')}</div>
        {recentFiles.map((file) => (
          <div
            key={file.guid}
            className={css.item}
            onClick={() => handleItemClick({ key: 'recent', fileType: file.type, fileGuid: file.guid })}
          >
            <img className={css.icon} src={getFileIcon(file.subType || file.type)} />
            <span>{file.name}</span>
          </div>
        ))}
      </div>

      {/* 暂时注释快速访问，后面要用到 */}
      {/* <div className={css.section}>
        <div className={css.sectionTitle}>{fm('BackToPopover.quickAccess')}</div>
      </div> */}
    </div>
  );
}
