import { ExportOutlined } from '@ant-design/icons';
import { Popover } from 'antd';
import React from 'react';

import arrowRight from '@/assets/images/common/<EMAIL>';
import folderSrc from '@/assets/images/fileIcon/<EMAIL>';
import formSrc from '@/assets/images/fileIcon/<EMAIL>';
import mosheetSrc from '@/assets/images/fileIcon/<EMAIL>';
import newdocSrc from '@/assets/images/fileIcon/<EMAIL>';
import pptSrc from '@/assets/images/fileIcon/<EMAIL>';
import quizFormSrc from '@/assets/images/fileIcon/<EMAIL>';
import tableSrc from '@/assets/images/fileIcon/<EMAIL>';
import tableViewFormSrc from '@/assets/images/fileIcon/<EMAIL>';
import templateSrc from '@/assets/images/fileIcon/<EMAIL>';
import uploadFileSrc from '@/assets/images/fileIcon/<EMAIL>';
import uploadFolderSrc from '@/assets/images/fileIcon/<EMAIL>';
import modocSrc from '@/assets/images/fileIcon/<EMAIL>';
import { fm } from '@/modules/Locale';

import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; noSupport?: boolean }) => void;
  onOpenInNewTab?: (key: string) => void;
  subMenuItems?: MenuItem[];
}

interface MenuItem {
  key: string;
  icon: React.ReactNode;
  title: string;
  className?: string;
  children?: MenuItem[];
  noSupport?: boolean;
  showDivider?: boolean;
}

export function AddNewPopover({ onItemClick, onOpenInNewTab }: PopoverProps) {
  function handleItemClick(key: string, noSupport?: boolean) {
    onItemClick?.({ key, noSupport });
  }

  function handleOpenInNewTab(e: React.MouseEvent<Element>, key: string) {
    e.stopPropagation();
    onOpenInNewTab?.(key);
  }

  const mainItems: MenuItem[] = [
    {
      key: 'newdoc',
      icon: <img className={css.icon} src={newdocSrc} />,
      title: fm('File.newdoc'),
    },
    {
      key: 'modoc',
      icon: <img className={css.icon} src={modocSrc} />,
      title: fm('File.modoc'),
    },
    {
      key: 'mosheet',
      icon: <img className={css.icon} src={mosheetSrc} />,
      title: fm('File.mosheet'),
    },
    {
      key: 'presentation',
      icon: <img className={css.icon} src={pptSrc} />,
      title: fm('File.presentation'),
    },
    {
      key: 'table',
      icon: <img className={css.icon} src={tableSrc} />,
      title: fm('File.table'),
    },
    {
      key: 'form',
      icon: <img className={css.icon} src={formSrc} />,
      title: fm('File.form'),
      children: [
        {
          key: 'normalForm',
          icon: <img className={css.icon} src={formSrc} />,
          title: fm('File.normalForm'),
        },
        {
          key: 'tableViewForm',
          icon: <img className={css.icon} src={tableViewFormSrc} />,
          title: fm('File.tableViewForm'),
        },
        {
          key: 'quizForm',
          icon: <img className={css.icon} src={quizFormSrc} />,
          title: fm('File.quizForm'),
        },
      ],
    },
    {
      key: 'folder',
      icon: <img className={css.icon} src={folderSrc} />,
      title: fm('File.folder'),
      showDivider: true,
    },
    {
      key: 'templateLibrary',
      icon: <img className={css.icon} src={templateSrc} />,
      title: fm('AddNewPopover.templateLibrary'),
      noSupport: true,
    },
    {
      key: 'upload',
      icon: <img className={css.icon} src={uploadFileSrc} />,
      title: fm('AddNewPopover.upload'),
    },
    {
      key: 'uploadFolder',
      icon: <img className={css.icon} src={uploadFolderSrc} />,
      title: fm('AddNewPopover.uploadFolder'),
      noSupport: true,
    },
  ];

  // 通用的菜单项渲染函数
  function renderMenuItem(item: MenuItem, options: { showNewTab?: boolean } = {}) {
    const { showNewTab = true } = options;

    return (
      <React.Fragment key={item.key}>
        <div
          className={`${css.item} ${item.children ? css.children : ''}`}
          onClick={() => !item.children && handleItemClick(item.key, item.noSupport)}
        >
          <div className={css.itemContent}>
            {item.icon}
            <span>{item.title}</span>
          </div>
          {showNewTab && !item.children && (
            <div
              className={css.newTabIcon}
              title={fm('Editor.openInNewTab')}
              onClick={(e) => handleOpenInNewTab(e, item.key)}
            >
              <ExportOutlined />
            </div>
          )}
          {item.children && <img src={arrowRight} width={5.5} />}
        </div>
        {item.showDivider && <div className={css.divider} />}
      </React.Fragment>
    );
  }

  function renderMainMenuItem(item: MenuItem) {
    // 需要显示新标签页图标的项目key
    const showNewTabIcon = !['form', 'folder'].includes(item.key);

    if (item.children) {
      return (
        <Popover
          key={item.key}
          arrow={false}
          content={
            <div className={css.formSubmenuContent}>
              {item.children?.map((subItem) => renderMenuItem(subItem, { showNewTab: true }))}
            </div>
          }
          placement="rightTop"
          trigger="hover"
        >
          {renderMenuItem(item, { showNewTab: false })}
        </Popover>
      );
    }

    return renderMenuItem(item, { showNewTab: showNewTabIcon });
  }

  return (
    <div className={css.container}>
      <div className={css.section}>
        {mainItems.map((item) => (
          <React.Fragment key={item.key}>{renderMainMenuItem(item)}</React.Fragment>
        ))}
      </div>
    </div>
  );
}
