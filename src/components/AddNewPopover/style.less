// 提取共同的 item 样式
.commonItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
  cursor: pointer;
  padding: 0 16px;
  height: 36px;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);

    .newTabIcon {
      // visibility: visible;  // 先隐藏，后面在放开
      opacity: 1;
    }
  }

  .itemContent {
    display: flex;
    align-items: center;

    .icon {
      width: 20px;
      margin-right: 12px;
    }

    span {
      color: var(--theme-text-color-default);
      font-size: 13px;
      font-weight: 400;
      line-height: 1;
    }
  }

  .newTabIcon {
    visibility: hidden;
    opacity: 0;
    transition: all 0.2s;
    color: var(--theme-text-color-secondary);
    font-size: 10px;

    :global(.anticon) {
      margin-right: 0;
      color: inherit;
      font-size: 12px;
    }

    &:hover {
      color: var(--theme-text-color-default);
    }
  }
}

.container {
  position: relative;
  padding: 8px 0;
  width: 220px;
  background-color: var(--theme-menu-color-bg-default);

  .section {
    padding: 4px 0;

    .item {
      .commonItem();

      &.children {
        position: relative;

        .newTabIcon {
          display: none;
        }
      }
    }
  }

  .divider {
    margin: 4px 16px;
    border-top: 1px solid var(--theme-separator-color-lighter);
    background: transparent;
    height: 1px;
  }
}

// 子菜单 Popover 样式
.formSubmenuContent {
  padding: 8px 0;
  width: 220px;
  background-color: var(--theme-menu-color-bg-default);

  .item {
    .commonItem();
  }
}
