.show {
  display: block;
}

.hidden {
  display: none;
}

.header {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  width: 100%;
  height: 50px;
  background: var(--theme-basic-color-bg-default);
  box-shadow: 0 -1px 0 0 var(--theme-basic-color-lighter) inset;

  * {
    box-sizing: border-box;
  }

  .leftSection {
    display: flex;
    position: relative;
    align-items: center;
    min-width: 350px;

    .ipt {
      cursor: pointer;
      margin-left: 12px;
      box-shadow: none;
      border: none;
      border-top: 2px solid transparent;
      border-bottom: 2px solid transparent;
      border-radius: 0;
      background-color: transparent;
      padding-right: 0;
      padding-left: 0;
      font-weight: 500;
      font-size: 14px;
      line-height: 1;
      height: 28px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: var(--theme-text-color-default);

      &::placeholder {
        color: var(--theme-basic-color-black);
      }

      &:hover {
        box-shadow: none;
        border-bottom: 2px solid var(--theme-basic-color-guidance);
      }
    }

    // 隐藏测量元素样式
    .hiddenMeasure {
      position: absolute;
      top: 100%;
      left: 12px;
      visibility: hidden;
      font-weight: 700;
      font-size: 14px;
    }

    .title {
      margin-right: 12px;
      margin-left: 20px;
      font-weight: 700;
      font-size: 14px;
    }

    .favoriteBtn {
      position: relative;
      top: -1px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      margin-left: 7px;
      width: 16px;
      height: 28px;

      svg {
        position: relative;
        top: 1px;
      }
    }

    .cancelStarIcon {
      color: var(--theme-text-color-default);
    }
  }

  .rightSection {
    display: flex;
    align-items: center;
  }
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 28px;
  color: var(--theme-text-color-medium);
  border: 1px solid var(--theme-basic-color-lighter);
  border-radius: 2px;
  background: linear-gradient(180deg, var(--theme-basic-color-bg-default) 0%, var(var(--gray5)) 100%);
  cursor: pointer;

  &:hover {
    background: var(--theme-basic-color-bg-default);
    border: 1px solid var(--theme-basic-color-light);
  }
}

.arrowRightBtn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  width: 41px;

  &:hover {
    z-index: 1;
  }
}

.arrowDownBtn {
  width: 25px;
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.plusBtn {
  width: 36px;
  margin-left: 8px;
}

.textBtn {
  padding: 0 10px;
  font-size: 12px;
  font-weight: 400;
}

.historyBtn {
  margin-left: 12px;
}

.demoBtn {
  margin-left: 8px;
}

.teamBtn {
  margin-left: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;

  &:hover {
    z-index: 1;
  }
}

.shareBtn {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.moreBtn {
  width: 36px;
  margin-left: 8px;
  color: var(--theme-text-color-medium);
}
