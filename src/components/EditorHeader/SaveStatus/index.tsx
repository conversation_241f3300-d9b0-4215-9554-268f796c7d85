import { useEffect, useState } from 'react';
import { useIntl } from 'umi';

import checkCircleGrayIcon from '@/assets/images/editor/<EMAIL>';
import checkCircleGreenIcon from '@/assets/images/editor/<EMAIL>';
import circleGrayIcon from '@/assets/images/editor/<EMAIL>';
import noWifiIcon from '@/assets/images/editor/<EMAIL>';
import warningIcon from '@/assets/images/editor/<EMAIL>';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import type { FileStatusType } from '@/model/Editor';
import { SaveStatusType, SyncStatusType } from '@/model/Editor';
import { fm } from '@/modules/Locale';
import { useEditorStore } from '@/store/Editor';

import css from './style.less';

interface Props {
  fileType?: string;
}

// 离线相关状态类型
const offlineRelatedStatus = [
  SaveStatusType.OFFLINE,
  SaveStatusType.OFFLINE_SAVING,
  SaveStatusType.OFFLINE_PERSIST_SUCCEED,
  SaveStatusType.OFFLINE_PERSIST_FAILED,
  SyncStatusType.SYNC_SAVING,
  SyncStatusType.SYNC_SUCCEED,
  SyncStatusType.SYNC_FAILED,
];

export function SaveStatus({ fileType }: Props) {
  // 保存状态文案
  const [statusText, setStatusText] = useState('');
  const [finalStatusType, setFinalStatusType] = useState<FileStatusType>(SaveStatusType.ONLINE);
  const isOnline = useNetworkStatus();
  const intl = useIntl();

  const statusMap = {
    [SaveStatusType.OFFLINE]: fm('Editor.saveStatus.offline'),
    [SaveStatusType.OFFLINE_SAVING]: fm('Editor.saveStatus.offlineSaving'),
    [SaveStatusType.OFFLINE_PERSIST_SUCCEED]: fm('Editor.saveStatus.offlinePersistSucceed'),
    [SaveStatusType.OFFLINE_PERSIST_FAILED]: fm('Editor.saveStatus.offlinePersistFailed'),
    [SaveStatusType.ONLINE]: fm('Editor.saveStatus.online'),
    [SaveStatusType.ONLINE_SAVING]: fm('Editor.saveStatus.onlineSaving'),
    [SaveStatusType.SAVE_FAILED]: fm('Editor.saveStatus.saveFailed'),
    [SaveStatusType.SAVE_ACCEPTED]: fm('Editor.saveStatus.saveAccepted'),
    [SaveStatusType.SAVE_SUCCEED]: fm('Editor.saveStatus.saveSucceed'),
    [SaveStatusType.APPLY_SUCCEED]: fm('Editor.saveStatus.applySucceed'),
    [SaveStatusType.APPLY_FAILED]: fm('Editor.saveStatus.applyFailed'),
    [SaveStatusType.SAVE_TIMEOUT]: fm('Editor.saveStatus.saveTimeout'),
    [SaveStatusType.ACCEPT_TIMEOUT]: fm('Editor.saveStatus.acceptTimeout'),
    [SyncStatusType.SYNC_SAVING]: fm('Editor.syncStatus.syncSaving'),
    [SyncStatusType.SYNC_SUCCEED]: fm('Editor.syncStatus.syncSucceed'),
    [SyncStatusType.SYNC_FAILED]: fm('Editor.syncStatus.syncFailed'),
  };

  const statusType = useEditorStore((state) => state.saveStatusType);

  useEffect(() => {
    if (!fileType || !statusType) return;

    let finalStatus: FileStatusType = statusType;
    let statusKey = statusType;

    // 网络离线且状态不是离线相关时，强制显示离线状态
    if (!isOnline && !offlineRelatedStatus.includes(statusType)) {
      finalStatus = SaveStatusType.OFFLINE;
      statusKey = SaveStatusType.OFFLINE;
    }

    setStatusText(statusMap[statusKey as keyof typeof statusMap] || '');
    setFinalStatusType(finalStatus);
  }, [statusType, fileType, isOnline, intl.locale]);

  if (!fileType || !statusType) return null;

  // 根据不同的保存状态选择对应的图标
  function renderIcon() {
    let src = '';
    switch (finalStatusType) {
      case SaveStatusType.ONLINE:
        src = checkCircleGreenIcon;
        break;
      case SaveStatusType.OFFLINE:
      case SaveStatusType.OFFLINE_PERSIST_SUCCEED:
        src = noWifiIcon;
        break;
      case SaveStatusType.SAVE_SUCCEED:
      case SyncStatusType.SYNC_SUCCEED:
        src = checkCircleGrayIcon;
        break;
      case SaveStatusType.OFFLINE_PERSIST_FAILED:
      case SaveStatusType.SAVE_FAILED:
      case SaveStatusType.APPLY_FAILED:
      case SaveStatusType.SAVE_TIMEOUT:
      case SaveStatusType.ACCEPT_TIMEOUT:
      case SyncStatusType.SYNC_FAILED:
        src = warningIcon;
        break;
      case SaveStatusType.ONLINE_SAVING:
      case SaveStatusType.SAVE_ACCEPTED:
      case SaveStatusType.OFFLINE_SAVING:
      case SyncStatusType.SYNC_SAVING:
        src = circleGrayIcon;
        break;
    }

    return <img className={css.saveStatusIcon} src={src} />;
  }

  return (
    <>
      {renderIcon()}
      <div className={css.saveStatusText}>{statusText}</div>
    </>
  );
}
