.formMenu {
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;
  box-shadow: 0 20px 32px 0 var(--theme-box-shadow-color-level6);
  color: var(--theme-text-color-default);

  .itemContainer {
    height: 56px;
    min-width: 158px;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    cursor: pointer;
    position: relative;
    border-radius: 2px;
    padding: 4px 10px;
    box-sizing: border-box;
    border: 1px solid transparent;

    &:hover {
      background-color: var(--theme-text-button-color-hover);
      border-color: var(--theme-separator-color-lighter);
    }

    .iconContainer {
      position: relative;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        display: block;
        width: 40px;
        height: 40px;
      }
    }

    .title {
      font-size: 12px;
      color: var(--sm-optimize-color-gray100);
      text-align: left;
      margin-left: 8px;
      line-height: 20px;
      max-width: 224px;
      display: box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
