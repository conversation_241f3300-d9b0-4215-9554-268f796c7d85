import type { fileItem } from '../CreateFileMenu';
import styles from './index.less';

type FormMenuProps = {
  formMenuList: fileItem[];
  onClose: (item: fileItem) => void;
};
const FormMenu: React.FC<FormMenuProps> = ({ formMenuList, onClose }) => {
  const itemClick = (item: fileItem) => {
    onClose(item);
  };

  return (
    <div className={styles.formMenu}>
      {formMenuList.map((item: fileItem) => (
        <div key={item.value}>
          <div className={`${styles.itemContainer}`} onClick={() => itemClick(item)}>
            <div className={styles.iconContainer}>
              <img src={item.src} />
            </div>
            <span className={styles.title}>{item.title}</span>
          </div>
        </div>
      ))}
    </div>
  );
};
export default FormMenu;
