.siderStyle {
  padding: 16px 0;
  box-sizing: border-box;
  flex-shrink: 0;
  height: 100%;
  background: var(--theme-card-default);
  box-shadow: inset -1px 0 0 var(--theme-box-shadow-color-level6);

  .rightSilder {
    position: absolute;
    top: 0;
    right: -15px;
    width: 30px;
    bottom: 0;
    z-index: 20;
  }

  .rightSilder:hover::after {
    width: 30px;
    height: 30px;
  }

  .creatDiv {
    display: flex;
    flex-direction: column;
    padding: 0 16px 24px;

    .createBtn {
      width: 100%;
      height: 32px;
      margin-left: 0;
      padding: 0 18px;
      border-radius: 2px;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      text-align: center;
      color: var(--theme-text-color-white);
      border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
      background: var(--theme-efficiency-button-color-bg);

      &:hover {
        background: var(--theme-efficiency-button-color-bg);
        border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
        box-shadow: none;
        color: var(--theme-text-color-white);
      }
    }
  }

  .menus {
    display: flex;
    flex-direction: column;
    flex: 1 0 0;
    min-height: 0;
    padding: 0 16px 14px;

    :global {
      .ant-menu-vertical > .ant-menu-item {
        height: 36px;
        line-height: 36px;
      }

      .ant-menu-inline {
        .ant-menu-item {
          color: var(--theme-text-color-default);
          padding: 12px 16px !important;
          border-radius: 2px;
          line-height: 36px;
          height: 36px;
          margin: 0;

          .ant-menu-title-content {
            margin-inline-start: 12px;
          }
        }

        .ant-menu-item-divider {
          margin: 12px 0;
          border-color: var(--theme-separator-color-lighter);
        }

        .ant-divider {
          margin: 12px 0;
          border-color: var(--theme-separator-color-lighter);
        }
      }
    }

    ul {
      border-inline-end: 0 !important;
      background-color: inherit;
    }
  }

  .footer {
    padding: 16px 16px 0;

    :global {
      .ant-divider-horizontal {
        margin: 12px 0;
      }
    }

    .businessOnlyIcon {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background-color: var(--theme-menu-color-bg-hover);
      }

      &:active {
        background-color: var(--theme-menu-color-bg-active);
      }
    }

    .footerDiv {
      display: flex;
      flex-direction: column;

      .business {
        overflow: hidden;
        text-overflow: ellipsis;
        height: 36px;
        line-height: 36px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        cursor: pointer;
        border-radius: 2px;

        &:hover {
          background-color: var(--theme-menu-color-bg-hover);
        }

        &:active {
          background-color: var(--theme-menu-color-bg-active);
        }

        .title {
          margin-left: 12px;
          white-space: nowrap;
          flex: auto;
          color: var(--theme-text-color-default);
        }
      }
    }
  }
}

.silderButton {
  position: absolute;
  /* stylelint-disable-next-line custom-property-pattern */
  top: var(--silder--y, 0);
  border: 0.5px solid rgba(224, 224, 224);
  left: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(103, 102, 102, 20%);
  cursor: pointer;
  transform: rotate(180deg) translateY(50%);
}

.rightSvg {
  transform: rotate(0deg) translateY(-50%);
}

.plusIcon {
  font-size: 20px;
}
