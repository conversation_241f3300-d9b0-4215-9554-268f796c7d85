import { Popover, Tooltip } from 'antd';
import { useState } from 'react';
import { useParams } from 'umi';

import { ReactComponent as ArrowSvg } from '@/assets/images/fileMenu/rightArrow.svg';
import { ReactComponent as TemplateSvg } from '@/assets/images/fileMenu/template.svg';
// import type { UploadRequestOption } from 'antd/es/upload/interface';
import useFileUpload from '@/hooks/useFileUpload';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm } from '@/modules/Locale';

import FormMenu from '../FormMenu';
import styles from './index.less';

export type FileType =
  | 'newdoc'
  | 'modoc'
  | 'mosheet'
  | 'table'
  | 'presentation'
  | 'form'
  | 'table-form'
  | 'test-form'
  | 'folder'
  | 'space'
  | 'upload-file';
export interface fileItem {
  src: string;
  title: string;
  value: FileType;
  disabled?: boolean;
  children?: any[];
}

type CreateFileMenuProps = {
  fileMenuList: fileItem[][];
  onClose: (item: fileItem) => void;
  showTemplateModel: () => void;
};
const CreateFileMenu: React.FC<CreateFileMenuProps> = ({ fileMenuList, onClose, showTemplateModel }) => {
  const isOnline = useNetworkStatus();
  const [formOpen, setFormOpen] = useState(false);

  // 获取当前的Guid
  const params = useParams<{ guid: string }>();
  const pGuid = params?.guid || 'Desktop';
  // 使用文件上传 hook
  const { triggerUpload } = useFileUpload({
    parentGuid: pGuid,
  });

  const itemClick = (item: fileItem) => {
    if (!isOnline) return;
    onClose(item);
    if (item.value === 'upload-file') {
      return triggerUpload();
    }
  };

  const onCloseHandle = (item: fileItem) => {
    onClose(item);
    setFormOpen(false);
  };

  const siderMenuCreateTemplateText = fm('SiderMenu.siderMenuCreateTemplateText');
  const networkStatusTipText = fm('CreateFileMenu.networkStatusTipText');

  const getFormContent = (formMenu: any[]) => {
    return <FormMenu formMenuList={formMenu} onClose={onCloseHandle} />;
  };

  const InnerItem = (item: fileItem, fileIndex: number, itemIndex: number) => {
    return (
      <Tooltip title={!isOnline && fileIndex === 1 && itemIndex === 2 ? networkStatusTipText : ''}>
        {item.children?.length ? (
          <Popover
            arrow={false}
            content={getFormContent(item.children)}
            open={formOpen}
            placement="rightTop"
            onOpenChange={setFormOpen}
          >
            <div className={`${styles.itemContainer}`} onClick={() => itemClick(item)}>
              <div className={styles.iconContainer}>
                <img src={item.src} />
              </div>
              <span className={styles.title}>{item.title}</span>
            </div>
          </Popover>
        ) : (
          <div
            className={`${styles.itemContainer}  ${
              !isOnline && fileIndex === 1 && itemIndex === 2 ? styles.itemDisabled : ''
            }`}
            onClick={() => itemClick(item)}
          >
            <div className={styles.iconContainer}>
              <img src={item.src} />
            </div>
            <span className={styles.title}>{item.title}</span>
          </div>
        )}
      </Tooltip>
    );
  };

  return (
    <div className={styles.fileMenu}>
      <section>
        {fileMenuList.map((fileItems, fileIndex) => (
          <div key={fileIndex} className={styles.groupContainer}>
            {fileItems.map((item: fileItem, itemIndex) => (
              <div key={item.value}>{InnerItem(item, fileIndex, itemIndex)}</div>
            ))}
          </div>
        ))}
      </section>
      <div className={styles.createForm} onClick={showTemplateModel}>
        <div className={styles.wrapper}>
          <TemplateSvg />
          <span className={styles.formTitle}>{siderMenuCreateTemplateText}</span>
        </div>
        <ArrowSvg />
      </div>
    </div>
  );
};
export default CreateFileMenu;
