.fileMenu {
  width: 348px;
  border-radius: 4px;
  padding: 16px;
  box-sizing: border-box;
  box-shadow: 0 20px 32px 0 var(--theme-box-shadow-color-level6);
  color: var(--theme-text-color-default);

  .groupContainer {
    border-bottom: 0.5px solid var(--theme-separator-color-lighter);
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 4px;
    padding-top: 8px;

    &:first-child {
      padding-top: 0;
    }

    .itemContainer {
      height: 56px;
      min-width: 158px;
      display: inline-flex;
      -webkit-box-align: center;
      align-items: center;
      cursor: pointer;
      position: relative;
      border-radius: 2px;
      padding: 4px 10px;
      box-sizing: border-box;
      margin-bottom: 4px;
      border: 1px solid transparent;

      &:hover {
        background-color: var(--theme-card-info-bg-hover);
        border-color: var(--theme-card-info-bg-hover-border);
      }

      &:active {
        background-color: var(--theme-card-info-bg-active);
        border-color: var(--theme-card-info-bg-active-border);
      }

      .iconContainer {
        position: relative;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          display: block;
          width: 40px;
          height: 40px;
        }
      }

      .title {
        font-size: 12px;
        color: var(--sm-optimize-color-gray100);
        text-align: left;
        margin-left: 8px;
        line-height: 20px;
        max-width: 224px;
        display: box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .itemDisabled {
      cursor: not-allowed;
    }
  }

  .createForm {
    margin-top: 8px;
    height: 32px;
    border: 1px solid transparent;
    border-radius: 2px;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    cursor: pointer;
    padding: 12px 18px;

    .wrapper {
      display: flex;
      -webkit-box-align: center;
      align-items: center;

      .formTitle {
        margin-left: 9px;
      }
    }

    &:hover {
      background-color: var(--theme-card-info-bg-hover);
      border-color: var(--theme-card-info-bg-hover-border);
    }

    &:active {
      background-color: var(--theme-card-info-bg-active);
      border-color: var(--theme-card-info-bg-active-border);
    }
  }
}
