import { But<PERSON>, Divider, <PERSON>dal, Tabs } from 'antd';
import React, { useState } from 'react';

import { ReactComponent as CloseSvg } from '@/assets/images/upload/close.svg';
import { fm } from '@/modules/Locale';

import type { FileType } from '../Desktop/CreateFileMenu';
import { TabContent } from '../TabContent';
interface TemplateModalProps {
  visible: boolean;
  closeTemplate: () => void;
}

interface TabsPropItems {
  key: FileType;
  label: string;
  children: JSX.Element;
}

export const TemplateModal = React.memo(function TemplateModal({ visible, closeTemplate }: TemplateModalProps) {
  const siderMenuCreateDocText = fm('SiderMenu.siderMenuCreateDocText');
  const siderMenuCreateMoDocText = fm('SiderMenu.siderMenuCreateMoDocText');
  const siderMenuCreateTableText = fm('SiderMenu.siderMenuCreateTableText');
  const siderMenuCreateMoTableText = fm('SiderMenu.siderMenuCreateMoTableText');
  const siderMenuCreatePptText = fm('SiderMenu.siderMenuCreatePptText');
  const siderMenuCreateFormText = fm('SiderMenu.siderMenuCreateFormText');
  const operations = (
    <>
      <Divider type="vertical" /> <Button icon={<CloseSvg />} type="text" onClick={closeTemplate} />
    </>
  );
  const [activeKey, setActiveKey] = useState<FileType>('newdoc');
  const items: TabsPropItems[] = [
    {
      key: 'newdoc',
      label: siderMenuCreateDocText,
      children: <TabContent type="newdoc" visible={activeKey === 'newdoc'} onClose={closeTemplate} />,
    },
    {
      key: 'modoc',
      label: siderMenuCreateMoDocText,
      children: <TabContent type="modoc" visible={activeKey === 'modoc'} onClose={closeTemplate} />,
    },
    {
      key: 'mosheet',
      label: siderMenuCreateTableText,
      children: <TabContent type="mosheet" visible={activeKey === 'mosheet'} onClose={closeTemplate} />,
    },
    {
      key: 'table',
      label: siderMenuCreateMoTableText,
      children: <TabContent type="table" visible={activeKey === 'table'} onClose={closeTemplate} />,
    },
    {
      key: 'presentation',
      label: siderMenuCreatePptText,
      children: <TabContent type="presentation" visible={activeKey === 'presentation'} onClose={closeTemplate} />,
    },
    {
      key: 'form',
      label: siderMenuCreateFormText,
      children: <TabContent type="form" visible={activeKey === 'form'} onClose={closeTemplate} />,
    },
  ];

  const changeTab = (activeKey: string) => {
    setActiveKey(activeKey as FileType);
  };

  return (
    <Modal
      centered
      classNames={{ content: 'templateModel' }}
      closeIcon={false}
      footer={null}
      open={visible}
      width={1008}
    >
      <Tabs
        activeKey={activeKey}
        defaultActiveKey="newdoc"
        items={items}
        tabBarExtraContent={operations}
        onChange={changeTab}
      />
    </Modal>
  );
});
