import { But<PERSON>, Divider, Tabs } from 'antd';
import { useState } from 'react';

import { ReactComponent as CloseSvg } from '@/assets/images/upload/close.svg';
import { fm } from '@/modules/Locale';

import type { FileType } from '../Desktop/CreateFileMenu';
import { TabContent } from '../TabContent';

interface TabsPropItems {
  key: FileType;
  label: string;
  children: JSX.Element;
}

interface ModalContentProp {
  handleCancel: () => void;
}

export function ModalContent({ handleCancel }: ModalContentProp) {
  const siderMenuCreateDocText = fm('SiderMenu.siderMenuCreateDocText');
  const siderMenuCreateMoDocText = fm('SiderMenu.siderMenuCreateMoDocText');
  const siderMenuCreateTableText = fm('SiderMenu.siderMenuCreateTableText');
  const siderMenuCreateMoTableText = fm('SiderMenu.siderMenuCreateMoTableText');
  const siderMenuCreatePptText = fm('SiderMenu.siderMenuCreatePptText');
  const siderMenuCreateFormText = fm('SiderMenu.siderMenuCreateFormText');
  const operations = (
    <>
      <Divider type="vertical" /> <Button icon={<CloseSvg />} type="text" onClick={handleCancel} />
    </>
  );
  const [activeKey, setActiveKey] = useState<FileType>('newdoc');
  const items: TabsPropItems[] = [
    {
      key: 'newdoc',
      label: siderMenuCreateDocText,
      children: <TabContent type="newdoc" visible={activeKey === 'newdoc'} onClose={handleCancel} />,
    },
    {
      key: 'modoc',
      label: siderMenuCreateMoDocText,
      children: <TabContent type="modoc" visible={activeKey === 'modoc'} onClose={handleCancel} />,
    },
    {
      key: 'mosheet',
      label: siderMenuCreateTableText,
      children: <TabContent type="mosheet" visible={activeKey === 'mosheet'} onClose={handleCancel} />,
    },
    {
      key: 'table',
      label: siderMenuCreateMoTableText,
      children: <TabContent type="table" visible={activeKey === 'table'} onClose={handleCancel} />,
    },
    {
      key: 'presentation',
      label: siderMenuCreatePptText,
      children: <TabContent type="presentation" visible={activeKey === 'presentation'} onClose={handleCancel} />,
    },
    {
      key: 'form',
      label: siderMenuCreateFormText,
      children: <TabContent type="form" visible={activeKey === 'form'} onClose={handleCancel} />,
    },
  ];

  const changeTab = (activeKey: string) => {
    setActiveKey(activeKey as FileType);
  };
  return (
    <Tabs
      activeKey={activeKey}
      defaultActiveKey="newdoc"
      items={items}
      tabBarExtraContent={operations}
      onChange={changeTab}
    />
  );
}
