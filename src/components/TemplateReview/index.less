.back {
  padding: 0 10px;
  gap: 4px;
  height: 24px;

  .backIcon {
    transform: rotate(90deg);
  }
}

.header {
  display: flex;
  margin-top: 24px;

  .headerImage {
    width: 144px;
    height: 90px;
    border-radius: 4px;
    margin-right: 32px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .headerRight {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }

    button {
      height: 32px;
      padding: 0 18px;
    }
  }
}

iframe {
  width: 100%;
  padding: 0;
  margin: 0;
  height: auto;
  border: 0;
}

.drawerView {
  padding: 4px 12px;

  :global {
    .ant-drawer-header {
      padding: 0;
      padding-bottom: 24px;
    }

    .ant-drawer-body {
      padding: 0;
      margin-top: 24px;
    }
  }
}
