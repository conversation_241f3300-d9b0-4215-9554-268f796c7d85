.siderContainer {
  width: 312px;
  height: 100%;
  min-height: 300px;
  background: var(--theme-layout-color-bg-editor);
  box-shadow: -1px 0 0 0 var(--theme-box-shadow-color-level8) inset;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  overflow: auto;
  padding: 48px 16px 0;
}

.userInfoBox {
  height: 188px;
  width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 4px;
  padding: 20px 34px;
  border: 1px solid var(--theme-box-shadow-color-level10);
  box-shadow: 0 8px 18px 0 var(--theme-box-shadow-color-level6);
  background: var(--theme-layout-color-bg-white);
  background-image: url('@/assets/images/common/userBoxbg.png');
  background-size: cover;

  .ant-avatar {
    display: block;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    top: -28px;
  }
}

.enterpriseIcon {
  margin-top: 48px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .dividerLine {
    flex: 1;
    height: 1px;
    background-color: var(--theme-separator-color-light);
    margin-bottom: 12px;
  }
}

.userNameBox {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userEmailBox {
  font-size: 12px;
  font-weight: 400;
  margin-top: 8px;
  color: var(--theme-text-color-secondary);
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.styledMenu {
  width: 100%;
  margin-top: 16px;
  background: var(--theme-layout-color-bg-new-page);
  border: none;

  .ant-menu-item {
    margin: 0;
    height: 48px;
    line-height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    width: 100%;

    .ant-menu-title-content {
      margin-left: 8px;
      font-weight: 500;
    }
  }

  .ant-menu-item-selected {
    background: var(--theme-menu-color-bg-active);
  }
}

.enterpriseBox {
  height: 52px;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 22px;
  border-top: 1px solid var(--theme-separator-color-lighter);

  .marginIcon8 {
    margin-left: 8px;
  }
}
