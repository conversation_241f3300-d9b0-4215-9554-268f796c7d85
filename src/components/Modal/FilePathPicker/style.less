.filePathPicker {
  * {
    box-sizing: border-box;
  }

  :global {
    .ant-modal-content {
      padding: 0;

      .ant-modal-body {
        .ant-modal-confirm-paragraph {
          max-width: 100%;
        }

        .ant-modal-confirm-body-wrapper {
          .ant-modal-confirm-body {
            .ant-modal-confirm-content {
              margin-top: 0;
            }
          }
        }
      }
    }
  }

  // 侧边栏
  .sideMenu {
    width: 170px;
    border-right: 1px solid var(--theme-basic-color-lighter);
    padding: 12px;
    overflow-y: auto;
    background-color: var(--theme-layout-color-bg-new-page);
    color: var(--theme-text-color-default);

    .header {
      margin-left: 12px;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }

    .subTitle {
      height: 16px;
      font-size: 10px;
      margin-left: 12px;
      color: var(--theme-basic-color-black);
      font-weight: 400;
    }

    .desktopSpace {
      margin-top: 24px;
    }

    .menuItem {
      height: 36px;
      padding: 0 12px;
      line-height: 1;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 2px;

      &:hover {
        background-color: var(--theme-basic-color-lighter);
      }

      &.active {
        background-color: var(--theme-basic-color-light);
      }

      .anticon {
        font-size: 16px;
        color: inherit;
      }
    }
  }

  // 内容区域
  .modalContent {
    display: flex;
    height: 528px;
    background-color: var(--theme-basic-color-bg-default);
    color: var(--theme-text-color-default);

    .mainContent {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .header {
        display: flex;
        align-items: center;
        padding: 7px 20px;

        .locationBreadcrumb {
          align-self: end;
          display: flex;
          align-items: center;
          gap: 24px;
          height: 32px;

          :global(.ant-btn-icon) {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .backBtn,
          .forwardBtn {
            width: 24px;
            height: 24px;
            font-size: 12px;
            color: var(--theme-text-color-default);

            :global(.ant-btn-icon) {
              font-size: 12px;
            }

            &:hover:not(:disabled) {
              background-color: var(--theme-text-button-color-hover);
            }

            &:disabled {
              color: var(--theme-text-color-disabled);
            }
          }
        }

        .fileInfo {
          width: 359px;
          margin-left: 76px;

          :global(.ant-input) {
            color: var(--theme-text-color-default);
            background-color: var(--theme-basic-color-bg-default);
          }

          .fileName {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 14px;

            :global(.ant-input) {
              flex: 1;
              height: 32px;
            }

            .fileNameText {
              width: max-content;
              color: var(--theme-text-color-default);
            }
          }

          .fileNav {
            display: flex;
            align-items: center;
            gap: 15px;

            :global {
              .ant-select {
                flex: 1;
                height: 32px;

                &.ant-select-single.ant-select-open {
                  .ant-select-selection-item {
                    color: var(--theme-text-color-default);
                  }
                }

                .ant-select-selector {
                  color: var(--theme-text-color-default);
                  background-color: var(--theme-basic-color-bg-default);
                  border-color: var(--theme-basic-color-black);
                  box-shadow: none;

                  &:hover {
                    border-color: var(--theme-basic-color-black);
                  }

                  .ant-select-prefix {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 8px;
                  }
                }

                .ant-select-arrow {
                  color: var(--theme-text-color-secondary);
                }

                .ant-select-selection-placeholder {
                  color: var(--theme-basic-color-black);
                }
              }
            }

            .fileNavText {
              width: max-content;
            }
          }
        }

        .viewControls {
          display: flex;
          align-items: center;
          margin-left: auto;
          align-self: end;
          height: 32px;

          .searchIcon {
            font-size: 18px;
            cursor: pointer;
          }
        }
      }

      .fileList {
        flex: 1;
        overflow-y: auto;
        padding: 10px 2px 10px 21px;
        border-top: 1px solid var(--theme-box-shadow-color-level8);
        border-bottom: 1px solid var(--theme-box-shadow-color-level8);

        .fileTree {
          width: 100%;
          height: 100%;
          overflow: hidden;
        }

        :global {
          .ant-spin-nested-loading {
            height: 100%;
            margin-right: 19px;

            .ant-spin-container {
              width: 100%;
              height: 100%;
            }

            .ant-spin-dot-holder {
              color: var(--theme-text-color-default);
            }
          }
        }
      }

      .folderItem {
        display: flex;
        align-items: center;
        cursor: pointer;
        height: 42px;
        padding-left: 10px;
        border-radius: 4px;
        border: 1px solid transparent;

        &:hover {
          background-color: var(--theme-menu-color-bg-hover);
          border-color: var(--theme-basic-color-lighter);
        }

        .expandIcon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          font-size: 12px;
          flex-shrink: 0;
          border-radius: 1px;

          &:hover {
            background-color: var(--theme-text-button-color-hover);
          }
        }
      }

      .emptyFolder {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 30px;
        border-radius: 4px;

        .emptyFolderText {
          padding-left: 24px;
        }
      }

      .emptyList {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icon {
          width: 200px;
          color: var(--theme-basic-color-primary);
        }

        .emptyTitle {
          margin-top: 8px;
          color: var(--theme-basic-color-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: 24px;
        }

        .emptySubTitle {
          color: var(--theme-text-color-secondary);
          margin-top: 8px;
          font-size: 14px;
          font-weight: 400;
          line-height: 24px;
        }
      }

      .folderIcon {
        width: 18px;
        margin-left: 10px;
      }

      .spaceIcon {
        color: var(--theme-text-color-default);
        margin-left: 10px;
      }

      .fileIcon {
        width: 20px;
        height: 20px;
      }

      .fileItem {
        height: 42px;
        padding-left: 42px;
        margin: 0;
        display: flex;
        align-items: center;

        &:hover {
          background-color: var(--theme-menu-color-bg-hover);
          border-color: var(--theme-basic-color-lighter);
        }

        .itemIcon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .itemName {
        margin-left: 8px;
        font-size: 14px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        position: relative;
        top: -1px;
      }
    }

    // 底部按钮
    .modalActions {
      display: flex;
      padding: 7px 20px;
      gap: 8px;

      .createFolderBtn:not([disabled]) {
        color: var(--theme-text-color-default);
      }

      .createFolderBtn:disabled {
        color: var(--theme-text-color-disabled);
      }

      .confirmBtn:disabled {
        color: var(--theme-text-color-disabled);
      }

      button {
        padding: 0 10px;
        font-size: 13px;
        gap: 10px;
      }

      button:nth-child(2) {
        margin-left: auto;
      }
    }

    // 搜索区域
    .searchContainer {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .searchIcon {
        width: 18px;
        height: 18px;
        background-color: var(--theme-basic-color-lighter);
        border-radius: 2px;
      }

      .searchWrapper {
        width: 168px;
        position: relative;

        :global(.ant-input-outlined) {
          color: var(--theme-text-color-default);
          border: 1px solid var(--theme-basic-color-black);
          background-color: var(--theme-basic-color-bg-default);
          box-shadow: 0 2px 8px 0 var(--input-border-shadow);

          &:focus {
            border: 1px solid var(--theme-basic-color-primary);
            box-shadow: 0 2px 8px 0 var(--input-border-shadow);
          }
        }

        .inputSearchIcon {
          width: 18px;
          height: 18px;
        }

        .inputCloseIcon {
          width: 24px;
          height: 24px;
          color: var(--theme-text-color-secondary);
          cursor: pointer;
        }
      }

      .resultContainer {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        background-color: var(--theme-basic-color-bg-default);
        border: 1px solid var(--theme-box-shadow-color-level8);
        border-radius: 2px;
        box-shadow: 0 8px 18px 0 var(--theme-box-shadow-color-level6);
        z-index: 1000;
      }

      .searchResults {
        padding: 4px 0;
      }

      li {
        display: flex;
        align-items: center;
        height: 36px;
        line-height: 1;
        padding: 0 16px;
        gap: 8px;
        cursor: pointer;
        color: var(--theme-text-color-default);

        &:hover {
          background-color: var(--theme-text-button-color-hover);
        }

        .folderIcon {
          width: 18px;
        }

        .spaceIcon {
          width: 18px;
          color: var(--theme-text-color-default);
          margin-left: 10px;
          flex-shrink: 0;
        }

        .fileName {
          font-size: 13px;
          color: var(--theme-basic-color-primary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .noResults {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 27px;
        color: var(--theme-basic-color-black);
        font-size: 12px;
        font-weight: 400;
      }

      .loadingContainer {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        font-size: 16px;
      }
    }
  }

  // 网格视图
  .gridView {
    width: 100%;
    height: 100%;
  }

  .gridItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    width: 100%;
    min-width: 0;

    &:hover {
      background-color: var(--theme-menu-color-bg-hover);
    }

    .folderIcon {
      margin: 0;
      margin-bottom: 8px;
      width: 48px;
      flex-shrink: 0;
    }

    .fileIcon {
      width: 48px;
      flex-shrink: 0;
    }

    .itemName {
      text-align: center;
      margin: 0;
      margin-top: 8px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
      line-height: 16px;
    }
  }

  .clearButton {
    margin-left: 4px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #666;
    }
  }
}
