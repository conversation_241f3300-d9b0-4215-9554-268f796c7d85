import { Modal } from 'antd';
import { useState } from 'react';

import { Content } from './Content';
import { SideMenu } from './SideMenu';
import css from './style.less';

export interface Options {
  filename?: string;
  locationGuid?: string;
  title?: string;
  onOk?: (locationGuid: string, folderName: string) => void;
  onCancel?: () => void;
  type?: 'create' | 'move';
  isAdmin?: boolean;
  role?: string;
}

export const FilePathPicker = (options: Options = {}) => {
  const { filename, locationGuid = 'desktop', onOk, type = 'create', isAdmin, role } = options;

  let modalInstance: any;

  function ModalContent() {
    const [curLocationGuid, setCurLocationGuid] = useState(locationGuid);
    const [menuId, setMenuId] = useState<string>('desktop');

    return (
      <div className={css.modalContent}>
        <SideMenu curMenuId={menuId} type={type} onSideMenuChange={setMenuId} />
        <Content
          filename={filename}
          isAdmin={isAdmin}
          locationGuid={locationGuid}
          role={role}
          sideMenuId={menuId}
          type={type}
          onCancel={() => {
            modalInstance.destroy();
          }}
          onLocationChange={(locationGuid) => {
            setCurLocationGuid(locationGuid);
          }}
          onOk={(folderName) => {
            onOk?.(curLocationGuid, folderName);
            modalInstance.destroy();
          }}
          onSideMenuChange={setMenuId}
        />
      </div>
    );
  }

  modalInstance = Modal.info({
    width: 893,
    icon: null,
    footer: null,
    centered: true,
    closable: false,
    className: css.filePathPicker,
    content: <ModalContent />,
  });
};
