import classNames from 'classnames';

import { ReactComponent as DesktopSvg } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as FavoritesSvg } from '@/assets/images/sidepanel/favorites.svg';
import { ReactComponent as RecentSvg } from '@/assets/images/sidepanel/recent.svg';
import { ReactComponent as ShareSvg } from '@/assets/images/sidepanel/share.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { fm2 } from '@/modules/Locale';

import css from './style.less';

export interface SideMenuProps {
  curMenuId: string;
  onSideMenuChange: (menuId: string) => void;
  type?: 'create' | 'move';
}

export function SideMenu({ curMenuId, onSideMenuChange, type = 'create' }: SideMenuProps) {
  // 侧边栏菜单项
  const menuItems = [
    { label: fm2('FilePathPicker.quickEntry'), className: 'quickEntry' },
    { id: 'used', label: fm2('FilePathPicker.recent'), icon: <RecentSvg /> },
    { id: 'shared', label: fm2('FilePathPicker.shared'), icon: <ShareSvg /> },
    { id: 'favorites', label: fm2('FilePathPicker.favorites'), icon: <FavoritesSvg /> },
    { label: fm2('FilePathPicker.desktopSpace'), className: 'desktopSpace' },
    { id: 'desktop', label: fm2('FilePathPicker.desktop'), icon: <DesktopSvg /> },
    { id: 'space', label: fm2('FilePathPicker.space'), icon: <SpaceSvg /> },
  ];
  const title = type === 'create' ? fm2('FilePathPicker.createTitle') : fm2('FilePathPicker.moveTitle');

  return (
    <div className={css.sideMenu}>
      <div className={css.header}>{title}</div>
      {menuItems.map((item) =>
        item.id ? (
          <div
            key={item.id}
            className={classNames(css.menuItem, curMenuId === item.id && css.active)}
            onClick={() => onSideMenuChange(item.id)}
          >
            {item.icon}
            {item.label}
          </div>
        ) : (
          <div key={item.label} className={classNames(css.subTitle, item.className && css[item.className])}>
            {item.label}
          </div>
        ),
      )}
    </div>
  );
}
