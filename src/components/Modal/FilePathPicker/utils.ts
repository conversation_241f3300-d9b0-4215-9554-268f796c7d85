import type { FileDetail } from '@/types/api';

import type { FileItem, FolderItem } from './FolderManager';
// 辅助函数，过滤文件
function mapFileItem(item: FileDetail): FileItem {
  return {
    id: item.guid,
    name: item.name,
    type: item.type,
  };
}

// 辅助函数，过滤文件夹
function mapFolderItem(item: any): FolderItem {
  return {
    id: item.guid,
    name: item.name,
    type: 'folder',
    parentName: '',
    role: item.role,
    sourceMenuId: item.isSpace ? 'space' : item.starred ? 'favorites' : 'desktop',
  };
}

// 将文件列表分离为文件和文件夹
export function processFileList(list: FileDetail[]) {
  const fileItems = list
    .filter((item: FileDetail) => !item.isFolder && !item.isSpace)
    .sort((a, b) => b.updatedAt - a.updatedAt)
    .map(mapFileItem);

  const folderItems = list.filter((item: FileDetail) => item.isFolder || item.isSpace).map(mapFolderItem);

  return { files: fileItems, folders: folderItems };
}
