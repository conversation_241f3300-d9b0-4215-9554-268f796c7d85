// FormModal.tsx
import { Button, Form, Input, message, Modal } from 'antd';
import trim from 'lodash/trim';
import { useEffect, useState } from 'react';

import { fm2 } from '@/modules/Locale';

import css from './style.less';

interface FormModalProps {
  onOk: (name: string) => void;
  okText?: string;
  cancelText?: string;
  initialValues?: any;
  title?: string;
  type?: 'space' | 'folder' | 'saveVersion';
  onClose?: () => void;
  placeholder?: string;
}
// 主组件，不再导出空组件
function ModalContent(props: FormModalProps) {
  const { onOk, type, initialValues } = props;
  const folderPlaceholder = fm2('CreateWithNamePop.folderPlaceholder');
  const spacePlaceholder = fm2('CreateWithNamePop.spacePlaceholder');
  const saveVersionPlaceholder = fm2('CreateWithNamePop.saveVersionPlaceholder');
  const ruleMessage = fm2('CreateWithNamePop.ruleMessage');

  const [placeholder, setPlaceholder] = useState('');

  useEffect(() => {
    if (props.placeholder) {
      setPlaceholder(props.placeholder);
    } else if (type === 'space') {
      setPlaceholder(spacePlaceholder);
    } else if (type === 'saveVersion') {
      setPlaceholder(saveVersionPlaceholder);
    } else {
      setPlaceholder(folderPlaceholder);
    }
  }, [type]);

  const [form] = Form.useForm();
  const [inputValue, setInputValue] = useState('');

  async function handleOk() {
    try {
      await form.validateFields();
      onOk(inputValue);
      props.onClose?.();
    } catch (error) {
      message.error(ruleMessage);
    }
  }

  function inputHandleChange(e: React.ChangeEvent<HTMLInputElement>) {
    let value = e.target.value;
    // 去除首尾空格
    value = trim(value);
    // 将连续的空格缩成一个空格
    value = value.replace(/\s+/g, ' ');
    setInputValue(value);
  }

  return (
    <div>
      <Form form={form} initialValues={initialValues} layout="vertical" preserve={false}>
        <Form.Item label="" name="name" rules={[{ required: true, message: placeholder }]}>
          <Input maxLength={512} placeholder={placeholder} value={inputValue} onChange={inputHandleChange} />
        </Form.Item>
      </Form>
      <div className={css.footer}>
        <Button color="default" disabled={!inputValue.length} variant="solid" onClick={handleOk}>
          {fm2('FilePathPicker.confirm')}
        </Button>
        <Button onClick={props.onClose}>{fm2('FilePathPicker.cancel')}</Button>
      </div>
    </div>
  );
}

export function CreateFolder(props: FormModalProps) {
  // 创建Modal实例
  let modalInstance: any = null;

  // 关闭弹窗的函数
  function closeModal() {
    if (modalInstance) {
      modalInstance.destroy();
    }
  }

  // 修改onOk回调，确保关闭Modal
  const enhancedProps = {
    ...props,
    onClose: closeModal,
    onOk: (name: string) => {
      props.onOk(name);
      closeModal();
    },
  };

  const titleMap = {
    folder: fm2('CreateWithNamePop.folderTitle'),
    saveVersion: fm2('CreateWithNamePop.saveVersionTitle'),
    space: fm2('CreateWithNamePop.spaceTitle'),
  };
  const title = titleMap[props.type as keyof typeof titleMap] || titleMap.folder;

  // 使用Modal.info创建弹窗
  modalInstance = Modal.info({
    title,
    width: 400,
    content: <ModalContent {...enhancedProps} />,
    className: 'create-folder-modal',
    icon: null,
    footer: null,
    centered: true,
    closable: true,
    maskClosable: true,
    onCancel: closeModal,
  });
}
