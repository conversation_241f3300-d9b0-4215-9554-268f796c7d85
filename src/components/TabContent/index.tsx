import 'overlayscrollbars/overlayscrollbars.css';

import { <PERSON><PERSON>, message, Spin } from 'antd';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'umi';

import { loadTemplateTypeData } from '@/api/Template1';
import docSrc from '@/assets/images/template/doc.png';
import docSmSrc from '@/assets/images/template/doc-small.png';
import docxSrc from '@/assets/images/template/docx.png';
import docxSmSrc from '@/assets/images/template/docx-small.png';
import formSrc from '@/assets/images/template/form.png';
import formSmSrc from '@/assets/images/template/form-small.png';
import pptSrc from '@/assets/images/template/ppt.png';
import pptSmSrc from '@/assets/images/template/ppt-small.png';
import sheetSrc from '@/assets/images/template/sheet.png';
import sheetSmSrc from '@/assets/images/template/sheet-small.png';
import tableSrc from '@/assets/images/template/table.png';
import tableFormSmSrc from '@/assets/images/template/table-form-small.png';
import tableSmSrc from '@/assets/images/template/table-small.png';
import tableFormSrc from '@/assets/images/template/tableForm.png';
import testFormSmSrc from '@/assets/images/template/test-form-small.png';
import testFormSrc from '@/assets/images/template/testForm.png';
import { fm } from '@/modules/Locale';
import { getFileIcon } from '@/utils/file';

import type { FileType } from '../Desktop/CreateFileMenu';
import { TemplateReview } from '../TemplateReview';
import styles from './index.less';

// 提取要排除的类型为单独的类型
type ExcludedFileTypes = 'table-form' | 'test-form' | 'folder' | 'space' | 'upload-file';
interface TabContentProp {
  type: Exclude<FileType, ExcludedFileTypes>;
  visible?: boolean;
  onClose: () => void;
}

export interface TemplateDataItem {
  id?: string | number;
  guid: string;
  img: string;
  icon: string;
  name: string;
  desc_url?: string;
  [key: string]: any;
}

const initialPage = 1;
const pageSize = 20;

export const TabContent = React.memo(function TabContent({ type, visible, onClose }: TabContentProp) {
  const siderMenuCreateDocText = fm('SiderMenu.siderMenuCreateDocText');
  const siderMenuCreateMoDocText = fm('SiderMenu.siderMenuCreateMoDocText');
  const siderMenuCreateTableText = fm('SiderMenu.siderMenuCreateTableText');
  const siderMenuCreateMoTableText = fm('SiderMenu.siderMenuCreateMoTableText');
  const siderMenuCreatePptText = fm('SiderMenu.siderMenuCreatePptText');
  const siderMenuCreateOrdinaryFormText = fm('SiderMenu.siderMenuCreateOrdinaryFormText');
  const siderMenuCreateTableFormText = fm('SiderMenu.siderMenuCreateTableFormText');
  const siderMenuCreateTestFormText = fm('SiderMenu.siderMenuCreateTestFormText');

  const i18nText = {
    preview: fm('TabContent.preview'),
    use: fm('TabContent.use'),
    testFormSubTitle: fm('TabContent.testFormSubTitle'),
    tableFormSubTitle: fm('TabContent.tableFormSubTitle'),
    formSubTitle: fm('TabContent.formSubTitle'),
    pptSubTitle: fm('TabContent.pptSubTitle'),
    tableSubTitle: fm('TabContent.tableSubTitle'),
    sheetSubTitle: fm('TabContent.sheetSubTitle'),
    docxSubTitle: fm('TabContent.docxSubTitle'),
    docSubTitle: fm('TabContent.docSubTitle'),
    empty: fm('TabContent.empty'),
    noSupport: fm('Editor.noSupport'),
  };

  // 基础标签项配置
  interface TabItemBase {
    icon: string;
    smallIcon: string;
    title: string;
    subTitle: string;
    typeValue: number;
  }

  // Form 类型的子项配置
  interface FormTabItem extends Omit<TabItemBase, 'typeValue'> {
    typeValue?: number;
    formType?: 'table' | 'quiz';
  }

  // Form 标签配置
  interface FormTabConfig {
    typeValue: number;
    items: FormTabItem[];
  }

  // 联合所有可能的标签配置类型
  type TabConfig = TabItemBase | FormTabConfig;

  type TabTypeMap = Record<Exclude<FileType, ExcludedFileTypes>, TabConfig>;

  // 获取当前的Guid
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || 'Desktop';
  const tabTypeMap: TabTypeMap = {
    newdoc: {
      icon: docSrc,
      smallIcon: docSmSrc,
      title: `${i18nText.empty}${siderMenuCreateDocText}`,
      subTitle: i18nText.docSubTitle,
      typeValue: -2,
    },
    modoc: {
      icon: docxSrc,
      smallIcon: docxSmSrc,
      title: `${i18nText.empty}${siderMenuCreateMoDocText}`,
      subTitle: i18nText.docxSubTitle,
      typeValue: -6,
    },
    mosheet: {
      icon: sheetSrc,
      smallIcon: sheetSmSrc,
      title: `${i18nText.empty}${siderMenuCreateTableText}`,
      subTitle: i18nText.sheetSubTitle,
      typeValue: -4,
    },
    table: {
      icon: tableSrc,
      smallIcon: tableSmSrc,
      title: `${i18nText.empty}${siderMenuCreateMoTableText}`,
      subTitle: i18nText.tableSubTitle,
      typeValue: -11,
    },
    presentation: {
      icon: pptSrc,
      smallIcon: pptSmSrc,
      title: `${i18nText.empty}${siderMenuCreatePptText}`,
      subTitle: i18nText.pptSubTitle,
      typeValue: -10,
    },
    form: {
      typeValue: -8,
      items: [
        {
          icon: formSrc,
          smallIcon: formSmSrc,
          title: `${i18nText.empty}${siderMenuCreateOrdinaryFormText}`,
          subTitle: i18nText.formSubTitle,
        },
        {
          icon: tableFormSrc,
          smallIcon: tableFormSmSrc,
          title: `${i18nText.empty}${siderMenuCreateTableFormText}`,
          subTitle: i18nText.tableFormSubTitle,
          formType: 'table',
        },
        {
          icon: testFormSrc,
          smallIcon: testFormSmSrc,
          title: `${i18nText.empty}${siderMenuCreateTestFormText}`,
          subTitle: i18nText.tableFormSubTitle,
          formType: 'quiz',
        },
      ],
    },
  };
  const [emptyItem] = useState<TabConfig>(tabTypeMap[type]);

  const createFileFunc = (item: any) => {
    onClose();
    const parentGuid = guid;
    if (item.formType) {
      window.open(`/api/v1/files/create/${type}?parentGuid=${parentGuid}&formType=${item.formType}`, '_blank');
      return;
    }
    window.open(`/api/v1/files/create/${type}?parentGuid=${parentGuid}`, '_blank');
  };

  const emptyItemTemp = (item: any, isSmall?: boolean) => {
    return (
      <div key={item.title} className={styles.emptyItem} onClick={() => createFileFunc(item)}>
        <div className={styles.icon}>
          <img src={isSmall ? item.smallIcon : item.icon} />
        </div>
        <div className={styles.title}>{item.title}</div>
        <div className={styles.subTitle}>{item.subTitle}</div>
      </div>
    );
  };

  const [dataList, setDataList] = useState<TemplateDataItem[]>([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [itemData, setItemData] = useState<TemplateDataItem>();

  const emptyFile = (isSmall?: boolean) => {
    return type === 'form' ? (
      <div className={styles.formEmptyContent}>
        {(emptyItem as FormTabConfig).items &&
          (emptyItem as FormTabConfig).items.map((item: any) => emptyItemTemp(item, isSmall))}
      </div>
    ) : (
      emptyItemTemp(emptyItem, isSmall)
    );
  };

  const [isLoading, setIsLoading] = useState(true);

  // 加载数据的函数
  const fetchData = async () => {
    try {
      setIsLoading(true);
      const {
        data: { publicTemplates },
      } = await loadTemplateTypeData({ type: emptyItem.typeValue, page: initialPage, pageSize });
      setDataList(publicTemplates || []);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type) {
      fetchData();
    }
  }, [type]);

  const contentRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (visible === false) {
      setOpenDrawer(false); //关闭 drawer
    }
    contentRef.current?.scrollIntoView({
      block: 'start', // 对齐顶部
    });
  }, [visible]);

  const handleReview = (item: TemplateDataItem) => {
    setItemData(item);
    message.warning(i18nText.noSupport);
    // setOpenDrawer(true);
  };

  const toUseTemp = (item: TemplateDataItem) => {
    const newFiles: string[] = ['newdoc', 'modoc', 'mosheet', 'table', 'presentation', 'form'];
    if (newFiles.includes(type)) {
      const parentGuid = guid;
      window.open(`/api/v1/files/create/${type}?parentGuid=${parentGuid}&templateGuid=${item?.guid}`, '_blank');
    }
    onClose();
  };

  const dataFileList = (
    <OverlayScrollbarsComponent
      className={styles.listContent}
      options={{
        scrollbars: {
          autoHide: 'scroll', // 滚动时隐藏滚动条
          clickScroll: true, // 点击轨道滚动
        },
      }}
    >
      <div ref={contentRef} className={styles.scrollContent}>
        {emptyFile(true)}
        {dataList.map((item) => (
          <div key={item.guid} className={styles.dataItem}>
            <img src={item.img} />
            <div className={styles.footerContent}>
              <div className={styles.title}>
                <img src={getFileIcon({ type })} />
                <span>{item.name}</span>
              </div>
              <div className={styles.buttons}>
                <Button type="default" onClick={() => handleReview(item)}>
                  {i18nText.preview}
                </Button>
                <Button type="primary" onClick={() => toUseTemp(item)}>
                  {i18nText.use}
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </OverlayScrollbarsComponent>
  );
  return (
    <Spin spinning={isLoading}>
      <div className={styles.content}>
        {!isLoading && (dataList.length ? dataFileList : emptyFile())}
        <TemplateReview itemData={itemData} open={openDrawer} onClose={() => setOpenDrawer(false)} />
      </div>
    </Spin>
  );
});
