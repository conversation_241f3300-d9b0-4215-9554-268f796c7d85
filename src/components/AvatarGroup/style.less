// 头像状态点
.statusDot {
  position: absolute;
  right: 3px;
  bottom: 2px;
  box-sizing: border-box;
  border-radius: 50%;
  width: 4px;
  height: 4px;
}

.avatarWrapper {
  position: relative;
  line-height: 1;
  cursor: pointer;
}

.avatar {
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-basic-color-black);
  color: var(--theme-text-color-white);
  border: 1px solid var(--theme-text-color-white);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatarText {
    font-weight: 500;
  }
}

// 剩余人数气泡样式
.restCount {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 4px;
  min-width: 27px;
  height: 24px;
  border-radius: 14px;
  color: var(--theme-text-color-white);
  font-weight: 500;
  font-size: 12px;
  background-color: var(--theme-text-color-medium);
  cursor: pointer;
}

.avatarGroup {
  display: flex;
  align-items: center;

  .avatarWrapper {
    margin-left: -3px;
  }
}

// 头像聚合弹框样式
.avatarPopoverContent {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 12px 16px;
  max-width: 142px;
  max-height: 320px;
  overflow-y: auto;
  background-color: var(--theme-menu-color-bg-default);
}
