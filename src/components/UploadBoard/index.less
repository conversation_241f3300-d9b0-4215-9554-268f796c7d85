.visible {
  display: block;
}

.disvisible {
  display: none;
}

.cancelText {
  color: var(--theme-text-color-disabled);
}

.styledPanelGroup {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 999;
  background: var(--theme-layout-color-bg-white);
  color: var(--theme-text-color-default);

  .panels {
    width: 458px;
    margin-top: 12px;

    .panelWrapper {
      box-shadow: 0 20px 32px 0 var(--theme-box-shadow-color-level6);
      border-radius: 2px;
      overflow: hidden;

      .titleExpIcon {
        width: 32px;
        height: 32px;
      }

      .titleStatusIcon {
        transform: scale(1.2);
        padding: 3px;
      }

      .header {
        display: flex;
        padding: 8px 16px;
        -webkit-box-pack: justify;
        justify-content: space-between;
        -webkit-box-align: center;
        align-items: center;

        .listTitle {
          margin-left: 8px;
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
        }

        .checkBtn {
          width: 104px;
        }

        .lightText {
          font-size: 14px;
          line-height: 24px;
          vertical-align: middle;
          color: var(--theme-text-color-secondary);
        }

        .right {
          button {
            margin-left: 8px;
            width: 32px;
          }
        }

        .left,
        .right {
          display: flex;
          -webkit-box-align: center;
          align-items: center;
        }
      }

      .showContent {
        display: block;
      }

      .noShowContent {
        display: none;
      }

      .content {
        width: 100%;
        font-size: 13px;

        .scrollContent {
          max-height: 240px;

          .failPop {
            position: relative;
            background: var(--theme-layout-color-bg-editor);
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
            padding: 8px 12px;
            margin: 0 16px;

            .failCloseBtn {
              position: absolute;
              right: 20px;
              top: 12px;
            }

            .failTitle {
              font-size: 14px;
              line-height: 24px;
            }

            .failList {
              // max-height: 160px;

              .failItem {
                font-size: 12px;
                line-height: 20px;
                overflow-y: auto;
                color: var(--theme-text-color-secondary);
              }
            }

            .footer {
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              flex-direction: row-reverse;

              button {
                font-size: 12px;
              }
            }
          }

          .list {
            margin-bottom: 16px;
            padding: 0 16px;

            .item {
              height: 32px;
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              -webkit-box-pack: justify;
              justify-content: space-between;
              padding: 8px 4px 8px 8px;
              cursor: pointer;

              &:hover {
                background-color: var(--theme-menu-color-bg-hover);
              }

              &:hover .right .hoverShow {
                display: flex;
              }

              &:hover .right .hoverHidden {
                display: none;
              }

              &:hover .left .haveUnderLine {
                text-decoration: underline;
                cursor: pointer;
              }

              &:hover .left .dontUnderLine {
                text-decoration: none;
                cursor: default;
              }

              .left {
                display: flex;
                -webkit-box-align: center;
                align-items: center;
                overflow: hidden;

                img {
                  width: 15px;
                  height: 15px;
                  margin-right: 12px;
                }

                .fileName {
                  max-width: 290px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .right {
                flex-shrink: 0;
                display: flex;
                align-items: center;

                .right1 {
                  .cancelText {
                    color: var(--theme-text-color-disabled);
                  }
                }

                .right2 {
                  display: flex;
                  align-items: center;

                  .myProgress {
                    padding: 8px;
                  }
                }

                .hoverShow {
                  display: none;
                  margin-left: 8px;
                }

                .hoverHidden {
                  display: flex;
                  align-items: center;
                }

                .percentIcon {
                  height: 16px;
                  overflow: hidden;
                }
              }
            }
          }
        }

        .itemStatusIcon {
          width: 14px;
          height: 14px;
          padding: 9px;
          margin-left: 8px;
        }

        .itemBtnIcon {
          width: 28px;
          height: 28px;
        }

        .itemFolderIcon {
          width: 14px;
          height: 14px;
          padding: 7px;
        }
      }
    }
  }
}
