import './index.less';

import type { MenuProps } from 'antd';
import { Button, Dropdown, message, Modal, QRCode, Tooltip } from 'antd';
import { useEffect, useState } from 'react';

import { getCollaborationDetail, getCollaborationList } from '@/api/Collaboration';
import { ReactComponent as ArrowLeft22 } from '@/assets/images/svg/arrowLeft22.svg';
import { ReactComponent as Link24 } from '@/assets/images/svg/link24.svg';
import { ReactComponent as Qrcode } from '@/assets/images/svg/qrcode.svg';
import { ReactComponent as SettingIcon } from '@/assets/images/svg/settingIcon.svg';
import { fm2 } from '@/modules/Locale';

import { Collaboration } from './Collaboration';
import { CollaborationAdd } from './CollaborationAdd';
import { CollaborationList } from './CollaborationList';
import { Share } from './Share';
//操作者的身份
// data.role
// inherited 继承的
// none 无  ///   没有协作权限 （有分享，登录的人都是企业内部人员，都有分享you know）
// reader 只读用户
// commentator 可评论者
// editor 可编辑者
// owner 所有者
// let role = data?.role;

// 文件协作权限
// const collaborationPermission = data?.permissionsAndReasons?.canManageCollaborator?.value||false;

// 分享链接开启状态（只有文件才有shareMode，文件夹和团队空间没有）
// private: 不分享-------------------没有分享过
// readonly: 只读
// commentable: 可以评论
// editable: 可以编辑
// enterprise_readonly: 企业成员只读
// enterprise_commentable: 企业成员可以评论
// enterprise_editable: 企业成员可以编辑
// 密码初始状态

// 是否是管理员
// const isAdmin = data?.isAdmin || data?.isFileAdmin;

interface CollaborationShareProps {
  visible: boolean;
  guid: string;
  onCancel: () => void;
  enterType?: string;
}
interface Role {
  id: number;
  avatar?: string;
}
interface CollaborationData {
  role?: string;
  url?: string;
  guid?: string;
  parentId?: number;
  parentRole?: string;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  isFolder?: boolean;
  shareMode?: string;
  name?: string;
}
const CollaborationShare: React.FC<CollaborationShareProps> = ({ visible, guid, onCancel, enterType }) => {
  const [data, setData] = useState<CollaborationData | null>(null);
  const [shareStatus, setShareStatus] = useState('');
  const [shareDisabled, setShareDisabled] = useState(false);
  const [linkCopyBtn, setLinkCopyBtn] = useState(true);
  const [fullUrl, setFullUrl] = useState('');
  const getDataInfo = () => {
    getCollaborationDetail(guid).then((res) => {
      const data = res.data;
      const copyUrl = `${new URL(data.url, window.location.href).href}/《${data.name}》`;
      setFullUrl(copyUrl);
      setData(data);
      // 分享回显和设置
      if (data?.shareMode) {
        setShareStatus(data.shareMode);
      } else {
        setShareStatus('');
      }
      if (data?.isSpace || data?.isFolder) {
        setShareDisabled(true);
      } else {
        if ((data?.role === 'editor' && data?.role === 'owner') || data?.isFileAdmin) {
          setShareDisabled(false);
        } else {
          setShareDisabled(true);
        }
      }
    });
  };
  const [CollaborationAdmins, setCollaborationAdmins] = useState<Role[]>([]);
  const [CollaborationRoles, setCollaborationRoles] = useState<Role[]>([]);
  const getUserList = () => {
    getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
      setCollaborationAdmins(res.data.admins);
      setCollaborationRoles(res.data.roles);
    });
  };
  const [listOpen, setListOpen] = useState(false);
  const [addOpen, setAddOpen] = useState(false);
  const [addAdminsOrRoles, setAddAdminsOrRoles] = useState('');
  const items: MenuProps['items'] = [
    {
      key: 'qrCode',
      label: <QRCode type="svg" value={fullUrl} />,
    },
  ];
  const handleCopyLinkShare = () => {
    navigator.clipboard.writeText(fullUrl);
    message.success(fm2('ShareCollaboration.copySuccess'));
  };
  useEffect(() => {
    if (visible) {
      getDataInfo();
      getUserList();
    }
    setListOpen(false);
    setAddOpen(false);
    if (enterType === 'setting') {
      setListOpen(true);
      setAddOpen(true);
      setAddAdminsOrRoles('roles');
    }
  }, [visible]);
  return (
    <Modal
      centered
      className="collaborationModal"
      footer={[
        data?.role === 'none' || (!listOpen && !addOpen) ? (
          <div className="shareFooter">
            <div key="qrCodeBox" className="qrCodeBox">
              <div className="qrLeft">
                <div className="qrTitle">{fm2('ShareCollaboration.shareMethod')}</div>
                <Tooltip title={fm2('ShareCollaboration.qrCodeShare')}>
                  <Dropdown menu={{ items }} placement="top" trigger={['click']}>
                    <Button className="qrCode" icon={<Qrcode />} />
                  </Dropdown>
                </Tooltip>
                {data?.role !== 'none' && linkCopyBtn && (
                  <Tooltip title={fm2('ShareCollaboration.copyLink')}>
                    <Button icon={<Link24 />} onClick={handleCopyLinkShare} />
                  </Tooltip>
                )}
              </div>
              {data?.role !== 'none' && (
                <div className="qrRight">
                  <Button icon={<SettingIcon />} type="text">
                    {fm2('ShareCollaboration.setPermission')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : null,
      ]}
      open={visible}
      title={
        data?.role !== 'none' ? (
          <div
            onClick={() => {
              if (addOpen) {
                setListOpen(true);
                setAddOpen(false);
              } else {
                setListOpen(false);
              }
            }}
          >
            {!listOpen ? (
              <span>{fm2('ShareCollaboration.title')}</span>
            ) : (
              <div className="modlaArrow">
                {enterType === 'setting' ? '' : <ArrowLeft22 />}
                {!addOpen ? (
                  <span>{fm2('ShareCollaboration.back')}</span>
                ) : (
                  <span>
                    {fm2('ShareCollaboration.add')}
                    {addAdminsOrRoles === 'roles'
                      ? fm2('ShareCollaboration.coauthor')
                      : fm2('ShareCollaboration.admin')}
                  </span>
                )}
              </div>
            )}
          </div>
        ) : (
          <span>{fm2('ShareCollaboration.linkShare')}</span>
        )
      }
      width={480}
      zIndex={1050}
      onCancel={onCancel}
    >
      {data?.role !== 'none' ? (
        !listOpen ? (
          <Collaboration
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            data={data}
            guid={guid}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
            setFullUrl={setFullUrl}
            setLinkCopyBtn={setLinkCopyBtn}
            setListOpen={setListOpen}
            shareDisabled={shareDisabled}
            shareStatus={shareStatus}
          />
        ) : !addOpen ? (
          <CollaborationList
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            data={data}
            getUserList={getUserList}
            guid={guid}
            parentId={data?.parentId === 0 ? undefined : data?.parentId}
            parentRole={data?.parentRole || ''}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
          />
        ) : (
          <CollaborationAdd addAdminsOrRoles={addAdminsOrRoles} data={data} guid={guid} />
        )
      ) : (
        <Share url={data?.url || ''} />
      )}
    </Modal>
  );
};

export default CollaborationShare;
