import { Button, Input, message } from 'antd';

import { ReactComponent as Link16 } from '@/assets/images/svg/link16.svg';
import { ReactComponent as SolidDownwardArrow } from '@/assets/images/svg/solidDownwardArrow.svg';
import { fm2 } from '@/modules/Locale';
interface ShareProps {
  url: string;
}
export const Share: React.FC<ShareProps> = ({ url }) => {
  const fullUrl = new URL(url, window.location.href).href;
  const handleCopy = () => {
    navigator.clipboard
      .writeText(fullUrl)
      .then(() => {
        message.success(fm2('ShareCollaboration.copySuccess'));
      })
      .catch(() => {
        message.error(fm2('ShareCollaboration.copyFail'));
      });
  };
  return (
    <div className="shareLinkCopy">
      <Input
        disabled
        className="inputDisabled"
        defaultValue={fm2('ShareCollaboration.linkReadOnly')}
        suffix={<SolidDownwardArrow />}
      />
      <Button icon={<Link16 />} onClick={handleCopy}>
        {fm2('ShareCollaboration.copyLink')}
      </Button>
    </div>
  );
};
