import { UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Input, message, Select, Switch, Tooltip } from 'antd';
import { useEffect, useState } from 'react';

import { SharePasswordStatus, updateExpireTime, updateShareStatus } from '@/api/Collaboration';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as Link16 } from '@/assets/images/svg/link16.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { ReactComponent as QuestionMark } from '@/assets/images/svg/questionMark.svg';
import { ReactComponent as Refresh } from '@/assets/images/svg/refresh.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { fm2 } from '@/modules/Locale';
interface CollaborationProps {
  data: any;
  guid: string;
  shareStatus: string;
  shareDisabled: boolean;
  CollaborationAdmins: any[];
  CollaborationRoles: any[];
  setFullUrl: (url: string) => void;
  setListOpen: (open: boolean) => void;
  setAddOpen: (open: boolean) => void;
  setLinkCopyBtn: (btn: boolean) => void;
  setAddAdminsOrRoles: (type: 'admins' | 'roles') => void;
}
export const Collaboration: React.FC<CollaborationProps> = ({
  data,
  guid,
  setFullUrl,
  shareStatus,
  shareDisabled,
  CollaborationAdmins,
  CollaborationRoles,
  setListOpen,
  setAddOpen,
  setLinkCopyBtn,
  setAddAdminsOrRoles,
}) => {
  const [switchCheckedLink, setSwitchCheckedLink] = useState<boolean>(false);
  const [switchCheckedPassword, setSwitchCheckedPassword] = useState<boolean>(false);
  const [passwordText, setPasswordText] = useState<string>('');

  const [switchCheckedValid, setSwitchCheckedValid] = useState<boolean>(false);
  const [fullUrlPassword, setFullUrlPassword] = useState<string>('');

  const [shareStatusValue, setShareStatusValue] = useState<string>('');
  const [options, setOptions] = useState<OptionType[]>([]);

  const [defaultValueDays, setDefaultValueDays] = useState<number | null>(null);
  const [remainingDays, setremainingDays] = useState<number>(0);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(fullUrlPassword);
    message.success(fm2('ShareCollaboration.copySuccess'));
  };
  type OptionType = {
    value: string;
    label: React.ReactNode;
  };
  const linkInCompanyText = fm2('ShareCollaboration.linkInCompany');
  const linkInInternetText = fm2('ShareCollaboration.linkInInternet');
  const optionsClose: OptionType[] = [
    {
      value: 'enterprise_readonly',
      label: (
        <>
          {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
        </>
      ),
    },
    {
      value: 'enterprise_commentable',
      label: (
        <>
          {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
        </>
      ),
    },
    {
      value: 'enterprise_editable',
      label: (
        <>
          {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
        </>
      ),
    },
    {
      value: 'readonly',
      label: (
        <>
          {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
        </>
      ),
    },
    {
      value: 'commentable',
      label: (
        <>
          {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
        </>
      ),
    },
    {
      value: 'editable',
      label: (
        <>
          {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
        </>
      ),
    },
  ];
  const linkInCompanyWithPassword = fm2('ShareCollaboration.linkInCompanyWithPassword');
  const linkInternetWithPassword = fm2('ShareCollaboration.linkInternetWithPassword');
  const optionsOpen: OptionType[] = [
    {
      value: 'enterprise_readonly',
      label: (
        <>
          {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
        </>
      ),
    },
    {
      value: 'enterprise_commentable',
      label: (
        <>
          {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
        </>
      ),
    },
    {
      value: 'enterprise_editable',
      label: (
        <>
          {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
        </>
      ),
    },
    {
      value: 'readonly',
      label: (
        <>
          {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
        </>
      ),
    },
    {
      value: 'commentable',
      label: (
        <>
          {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
        </>
      ),
    },
    {
      value: 'editable',
      label: (
        <>
          {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
        </>
      ),
    },
  ];
  const optionsDays = [
    { value: 1, label: `1${fm2('ShareCollaboration.day')}` },
    { value: 7, label: `7${fm2('ShareCollaboration.day')}` },
    { value: 30, label: `30${fm2('ShareCollaboration.day')}` },
  ];

  const passwordButton = (checked: boolean) => {
    SharePasswordStatus(guid, { passwordProtected: checked, reset: false }).then((res: any) => {
      const baseUrl = `${new URL(data?.url, window.location.href).href}/《${data?.name}》`;
      setPasswordText(res.data?.password);
      if (checked) {
        setOptions(optionsOpen);
        setFullUrlPassword(`${baseUrl} ${fm2('ShareCollaboration.accessPassword')} ${res.data?.password}`);
        setFullUrl(`${baseUrl} ${fm2('ShareCollaboration.accessPassword')} ${res.data?.password}`);
      } else {
        setOptions(optionsClose);
        setFullUrlPassword(baseUrl);
        setFullUrl(baseUrl);
      }

      setShareStatusValue('enterprise_readonly');
      setSwitchCheckedPassword(checked);
    });
  };
  const handleChangePassword = () => {
    SharePasswordStatus(guid, { passwordProtected: true, reset: true }).then((res) => {
      setPasswordText(res.data?.password);
    });
  };
  const headleChangeDays = (value: number) => {
    updateExpireTime(guid, { shareModeExpireDuration: value * 86400 }).then((res) => {
      const shareModeExpiredAt = res.data?.shareModeExpiredAt;
      const now = Math.floor(Date.now() / 1000);
      if (shareModeExpiredAt) {
        const remainingSeconds = shareModeExpiredAt - now;
        const remainingDays = remainingSeconds > 0 ? Math.floor(remainingSeconds / 86400) : 0;
        setremainingDays(remainingDays);
      } else {
        setremainingDays(0);
      }
    });
  };

  const getDays = () => {
    if (data?.shareModeExpireDuration && data?.shareModeExpireDuration !== 0) {
      setSwitchCheckedValid(true);
      const shareModeExpireDuration = data?.shareModeExpireDuration;
      const expireDays = shareModeExpireDuration ? Math.floor(shareModeExpireDuration / 86400) : undefined;
      setDefaultValueDays(expireDays ?? null);

      const shareModeExpiredAt = data?.shareModeExpiredAt;
      const now = Math.floor(Date.now() / 1000);
      if (shareModeExpiredAt) {
        const remainingSeconds = shareModeExpiredAt - now;
        const remainingDays = remainingSeconds > 0 ? Math.ceil(remainingSeconds / 86400) : 0;
        setremainingDays(remainingDays);
      } else {
        setremainingDays(0);
      }
    } else {
      setSwitchCheckedValid(false);
    }
  };
  const changeValid = (checked: boolean): void => {
    setSwitchCheckedValid(checked);
    const days = checked ? 30 : 0;
    setDefaultValueDays(days);
    headleChangeDays(days);
  };
  useEffect(() => {
    //分享是否开启
    if (shareStatus !== 'private' && shareStatus !== '' && !shareDisabled) {
      setSwitchCheckedLink(true);
      setLinkCopyBtn(false);
    } else {
      setSwitchCheckedLink(false);
      setLinkCopyBtn(true);
    }
    //分享密码是否开启
    const baseUrl = `${new URL(data?.url, window.location.href).href}/《${data?.name}》`;
    if (data?.passwordProtected) {
      setSwitchCheckedPassword(true);
      setPasswordText(data?.password);
      setOptions(optionsOpen);
      if (data?.password) {
        setFullUrlPassword(`${baseUrl} ${fm2('ShareCollaboration.accessPassword')} ${data?.password}`);
        setFullUrl(`${baseUrl} ${fm2('ShareCollaboration.accessPassword')} ${data?.password}`);
      } else {
        setFullUrlPassword(baseUrl);
        setFullUrl(baseUrl);
      }
    } else {
      setSwitchCheckedPassword(false);
      setPasswordText('');
      setOptions(optionsClose);
      setFullUrlPassword(baseUrl);
      setFullUrl(baseUrl);
    }

    if (shareStatus && shareStatus !== '' && shareStatus !== 'private') {
      setShareStatusValue(shareStatus);
    } else {
      setShareStatusValue('enterprise_readonly');
    }
    //分享日期 是否开启 并回显
    getDays();
  }, [guid, data, shareStatus]);
  return (
    <div>
      <div className="modalBodyInput">
        <Input
          placeholder={fm2('ShareCollaboration.searchAddCollab')}
          prefix={<Search />}
          onFocus={() => {
            if (data?.isAdmin || data?.isFileAdmin) {
              setListOpen(true);
              setAddOpen(true);
              setAddAdminsOrRoles('roles');
            } else {
              message.error(fm2('ShareCollaboration.onlyManagerCanAddCoauthor'));
            }
          }}
        />
      </div>
      <div className="userList">
        <div className="userItem">
          <div className="collaborator">{fm2('ShareCollaboration.coauthor')}</div>
          {CollaborationRoles.length > 0 ? (
            CollaborationRoles.slice(0, 5).map((item) => {
              return <Avatar key={item.id} icon={<UserOutlined />} size={24} src={item.avatar} />;
            })
          ) : (
            <div className="noDataCollaborator">
              <NoDataIcon />
              <span>暂无</span>
            </div>
          )}
          <div className="manager">{fm2('ShareCollaboration.admin')}</div>
          {CollaborationAdmins.slice(0, 3).map((item) => {
            return <Avatar key={item.id} icon={<UserOutlined />} size={24} src={item.avatar} />;
          })}
        </div>
        <div
          className="userRightIcon"
          onClick={() => {
            setListOpen(true);
          }}
        >
          <ArrowRight />
        </div>
      </div>
      <div className="linkShareTitle">{fm2('ShareCollaboration.linkShare')}</div>
      <div className="switchBox">
        {/* 分享开关 */}
        <Switch
          checked={switchCheckedLink}
          className="switchMr"
          disabled={shareDisabled}
          size="small"
          onChange={(checked) => {
            setSwitchCheckedLink(checked);
            setLinkCopyBtn(!checked);
            if (!checked) {
              updateShareStatus(guid, { shareMode: 'private' }).then(() => {
                // console.log(res.data)
              });
            } else {
              updateShareStatus(guid, { shareMode: shareStatusValue }).then(() => {
                // console.log(res.data)
              });
            }
            setSwitchCheckedValid(false);
          }}
        />
        <span> {switchCheckedLink ? fm2('ShareCollaboration.open') : fm2('ShareCollaboration.close')}</span>
      </div>
      {switchCheckedLink && (
        <div className="switchOpenShare">
          <div className="linkCopy">
            <div className="linkText">
              <Select
                options={options}
                style={{ width: '100%' }}
                value={shareStatusValue}
                onChange={(value) => {
                  setShareStatusValue(value);
                  updateShareStatus(guid, { shareMode: value }).then(() => {
                    setSwitchCheckedValid(false);
                  });
                }}
              />
            </div>
            <Button icon={<Link16 />} onClick={handleCopyLink}>
              {fm2('ShareCollaboration.copyLink')}
              {switchCheckedPassword ? fm2('ShareCollaboration.linkWithPassword') : ''}
            </Button>
          </div>
          <div className="linkSetting">
            <div className="linkPassword">
              {/* 密码开关 */}
              <Switch
                checked={switchCheckedPassword}
                className="switchMr"
                size="small"
                onChange={(checked) => {
                  passwordButton(checked);
                }}
              />
              {switchCheckedPassword ? (
                <>
                  <div>{fm2('ShareCollaboration.linkPassword')}</div>
                  <div className="linkPasswordInput">{passwordText}</div>
                  <Refresh />
                  <div className="changePassword" onClick={handleChangePassword}>
                    {fm2('ShareCollaboration.changePassword')}
                  </div>
                </>
              ) : (
                <div>{fm2('ShareCollaboration.needPassword')}</div>
              )}
            </div>
            <div className="linkValid">
              <div className="linkValidLeft">
                <Switch
                  checked={switchCheckedValid}
                  className="switchMr"
                  size="small"
                  onChange={(checked) => {
                    changeValid(checked);
                  }}
                />
                <span>{fm2('ShareCollaboration.linkExpiration')}</span>
                {switchCheckedValid ? (
                  <>
                    <Select
                      className="validSelect"
                      defaultValue={defaultValueDays}
                      options={optionsDays}
                      size="small"
                      onChange={(value) => {
                        headleChangeDays(value);
                      }}
                    />
                    <Tooltip
                      placement="top"
                      title={
                        <div className="tooltipShare">
                          <div>{fm2('ShareCollaboration.switchOff')}</div>
                          <div>{fm2('ShareCollaboration.switchOn')}</div>
                        </div>
                      }
                    >
                      <QuestionMark />
                    </Tooltip>
                  </>
                ) : (
                  <span>({fm2('ShareCollaboration.expirationClose')})</span>
                )}
              </div>
              {switchCheckedValid && (
                <div>
                  {fm2('ShareCollaboration.remaining')}
                  {remainingDays}
                  {fm2('ShareCollaboration.day')}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
