import axios from 'axios';

import { adaptor } from '@/utils/ShimoSDK/APIAdaptor';

import type { AttachLimit, CheckpointType } from './type';
import { UserFeature, UserQuota } from './type';

export interface AttachLimitType {
  /**
   * 是否含有该功能的权限
   * true: 有权限
   * false: 无权限
   */
  authorized: boolean;
  /**
   * 是否不检测
   * true：直接通过
   * false：需要进行检测具体数值是否满足要求
   */
  noCheck: boolean;

  /**
   * 单位（不参与计算，仅作显示）
   * minutes、MB
   */
  unit?: string;

  /**
   * 描述
   */
  desc?: string;

  /**
   * 是否采用范围
   */
  isRange?: boolean;

  /**
   * min/max int64 默认表示 MB/个/分钟/行
   */
  max?: number;
  min?: number;
}

export type CheckPermissionType = (key: string) => null | AttachLimitType;

export enum ShimoFileType {
  modoc = 'modoc',
  rdoc = 'rdoc',
  table = 'table',
  presentation = 'presentation',
  mosheet = 'mosheet',
  form = 'form',
}

const handleSpecialCases = (key: SpecialKey, data: CheckpointType, type: ShimoFileType): AttachLimitType => {
  let actualKey: string;
  const typeIsPresentation = type === ShimoFileType.presentation;
  const typeIsModoc = type === ShimoFileType.modoc;

  if (typeIsPresentation || typeIsModoc) {
    switch (key) {
      case UserQuota.AttachLimitAllSize:
        actualKey = typeIsPresentation ? UserQuota.AttachLimitPresentationSize : UserQuota.AttachLimitModocSize;
        break;
      case UserQuota.AttachLimitAllImgSize:
        actualKey = typeIsPresentation ? UserQuota.AttachLimitPresentationImgSize : UserQuota.AttachLimitModocImgSize;
        break;
      case UserQuota.AttachLimitAllVideoSize:
        actualKey = typeIsPresentation
          ? UserQuota.AttachLimitPresentationVideoSize
          : UserQuota.AttachLimitModocVideoSize;
        break;

      default:
        actualKey = key;
        break;
    }
  } else {
    actualKey = key;
  }
  return {
    ...data.quotas?.[actualKey],
    noCheck: !!data.quotas?.[actualKey]?.noCheck,
    authorized: true,
  };
};

/**
 * 返回检测key是否通过检测的函数
 * @returns
 */
export const createCheckPermission: (type: ShimoFileType) => Promise<CheckPermissionType> = async (type) => {
  try {
    if (!window.__RUNTIME_ENV__?.SDK_V2_SIGNATURE) {
      return (key: string) => {
        console.log(`${key}：未进行校验因为缺少必要参数`);
        return {
          authorized: false,
          noCheck: false,
        };
      };
    }
    const config = adaptor({
      url: '/sdk/v2/api/globals/checkpoint',
      method: 'GET',
      headers: {},
    });
    const {
      data: { data: checkpoint },
    } = await axios.get<{ data: CheckpointType }>(config.url, {
      headers: config.headers,
    });

    return (key: string) => {
      if (!checkpoint?.features || !checkpoint?.quotas) {
        return {
          authorized: false,
          noCheck: false,
        };
      }

      // TODO: sdk环境的UserFeatureWhole没用到暂时不用接入
      const allKnownKey: string[] = [...Object.values(UserQuota), ...Object.values(UserFeature)];

      // key不属于已知枚举
      const isUnknownKey = !allKnownKey.includes(key);

      if (isUnknownKey) {
        return null;
      }

      // 已知且存在
      const isFeatures = checkpoint.features.includes(key);
      const isQuotas = Object.keys(checkpoint.quotas).includes(key);

      const ranges = checkpoint.quotas[key]?.range;

      /**
       * 特殊处理附件上传相关
       */
      if (
        isQuotas &&
        (key === UserQuota.AttachLimitAllSize ||
          key === UserQuota.AttachLimitAllImgSize ||
          key === UserQuota.AttachLimitAllVideoSize)
      ) {
        return handleSpecialCases(key, checkpoint, type);
      }

      /**
       * sdk环境没有fileSize信息,不做前端卡点
       */
      if (
        isQuotas &&
        (key === UserQuota.EditLimitMosheetCollabNum ||
          key === UserQuota.EditLimitRdocCollabNum ||
          key === UserQuota.EditLimitModocCollabNum ||
          key === UserQuota.EditLimitPresentationCollabNum ||
          key === UserQuota.EditLimitTableCollabNum) &&
        ranges
      ) {
        return {
          authorized: true,
          noCheck: !!checkpoint.quotas[key]?.noCheck,
          unit: checkpoint.quotas[key]?.unit,
          desc: checkpoint.quotas[key]?.desc,
          isRange: false,
          max: ranges[ranges.length - 1].max,
        };
      }

      /**
       * Quotas的数据需要全部返回并手动补上authorized
       */
      if (isQuotas) {
        return {
          ...(checkpoint.quotas[key as UserQuota] as AttachLimit),
          authorized: true,
        };
      }

      /**
       * Features的数据
       */
      return {
        authorized: isFeatures,
        noCheck: isFeatures,
      };
    };
  } catch (error: unknown) {
    // 返回默认拒绝访问的检查函数
    return (key: string) => {
      console.log(`${key}：未进行校验因为校验接口异常`);
      return {
        authorized: false,
        noCheck: false,
      };
    };
  }
};

type SpecialKey = UserQuota.AttachLimitAllSize | UserQuota.AttachLimitAllImgSize | UserQuota.AttachLimitAllVideoSize;
