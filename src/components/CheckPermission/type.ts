interface RangeType {
  start: number;
  end: number;
  max: number;
  min: number;
}

export interface AttachLimit {
  /**
   * 是否不检测
   * true：直接通过
   * false：需要进行检测具体数值是否满足要求
   */
  noCheck: boolean;

  /**
   * 单位（不参与计算，仅作显示）
   * minutes、MB
   */
  unit: string;

  /**
   * 描述
   */
  desc: string;

  /**
   * 是否采用范围
   */
  isRange: boolean;

  /**
   * 范围
   */
  range?: Array<RangeType>;

  /**
   * min/max int64 默认表示 MB/个/分钟/行
   */
  max?: number;
  min?: number;
}

/**
 * 相关文档
 * https://shimo.im/docs/loqeMmO1BoCMw0qn
 * 2024-05-28
 * 2024-07-30(update)
 */
export enum UserQuota {
  /**
   * 单次批量下载文件个数
   */
  CloudDownloadFileNum = 'cloud_download_file_num',
  /**
   * 团队空间上传文件数量(单次)
   */
  CloudTeamSpaceUploadFileNum = 'cloud_team_space_upload_file_num',
  /**
   * 团队空间上传文件大小(单次)
   */
  CloudTeamSpaceUploadFileSize = 'cloud_team_space_upload_file_size',
  /**
   * 插入附件：图片附件大小限制
   */
  AttachLimitAllImgSize = 'attach_limit_all_img_size',
  /**
   * 插入附件：附件大小限制
   */
  AttachLimitAllSize = 'attach_limit_all_size',
  /**
   * 插入附件：传统文档附件大小限制
   */
  AttachLimitModocSize = 'attach_limit_modoc_size',
  /**
   * 插入附件：幻灯片附件大小限制
   */
  AttachLimitPresentationSize = 'attach_limit_presentation_size',
  /**
   * 插入附件：传统文档 图片附件大小限制
   */
  AttachLimitModocImgSize = 'attach_limit_modoc_img_size',
  /**
   * 插入附件：幻灯片 图片附件大小限制
   */
  AttachLimitPresentationImgSize = 'attach_limit_presentation_img_size',
  /**
   * 插入附件：传统文档 视频附件大小限制
   */
  AttachLimitModocVideoSize = 'attach_limit_modoc_video_size',
  /**
   * 插入附件：幻灯片 视频附件大小限制
   */
  AttachLimitPresentationVideoSize = 'attach_limit_presentation_video_size',
  /**
   * 插入附件：视频附件大小限制
   */
  AttachLimitAllVideoSize = 'attach_limit_all_video_size',
  /**
   * 插入附件：应用表格中单个图片大小限制
   */
  AttachLimitTableImgSize = 'attach_limit_table_img_size',
  /**
   * 插入附件：应用表格中单个附件大小限制
   */
  AttachLimitTableSize = 'attach_limit_table_size',
  /**
   * 协作->所有套件->单文件添加协作人数
   */
  EditLimitAllCollabNum = 'edit_limit_all_collab_num',
  /**
   * 协作->表格->单个文件的协作编辑人数
   */
  EditLimitMosheetCollabNum = 'edit_limit_mosheet_collab_num',
  /**
   * 协作->轻文档->单个文件的协作编辑人数
   */
  EditLimitRdocCollabNum = 'edit_limit_rdoc_collab_num',
  /**
   * 协作->传统文档->单个文件的协作编辑人数
   */
  EditLimitModocCollabNum = 'edit_limit_modoc_collab_num',
  /**
   * 协作->幻灯片->单个文件的协作编辑人数
   */
  EditLimitPresentationCollabNum = 'edit_limit_presentation_collab_num',
  /**
   * 协作->应用表格->单个文件的协作编辑人数
   */
  EditLimitTableCollabNum = 'edit_limit_table_collab_num',
  /**
   * 表单	可编辑文件体积上限
   */
  EditLimitFormSize = 'edit_limit_form_size',
  /**
   * 传统文档	可编辑文件体积上限
   */
  EditLimitModocSize = 'edit_limit_modoc_size',
  /**
   * 表格 公式计算模型
   */
  EditLimitMosheetFcModel = 'edit_limit_mosheet_fc_model',
  /**
   * 表格 单 sheet 单元格个数限制
   */
  EditLimitMosheetSheetCell = 'edit_limit_mosheet_sheet_cell',
  /**
   * 表格 单 sheet 公式数量限制（公式计算）
   */
  EditLimitMosheetSheetFc = 'edit_limit_mosheet_sheet_fc',
  /**
   * 表格 可编辑文件体积上限
   */
  EditLimitMosheetSize = 'edit_limit_mosheet_size',
  /**
   * 表格 可创建独立视图个数
   */
  EditLimitMosheetView = 'edit_limit_mosheet_view',
  /**
   * 幻灯片	可编辑文件体积上限
   */
  EditLimitPresentationSize = 'edit_limit_presentation_size',
  /**
   * 轻文档	可编辑文件体积上限
   */
  EditLimitRdocSize = 'edit_limit_rdoc_size',
  /**
   * 应用表格 每个数据表的锁定视图数量
   */
  EditLimitTableLockView = 'edit_limit_table_lock_view',
  /**
   * 应用表格 手动保存的版本数量
   */
  EditLimitTableManualVerison = 'edit_limit_table_manual_verison',
  /**
   * 应用表格 合并工作表引用 table 数量上限
   */
  EditLimitTableMergeTableRefer = 'edit_limit_table_merge_table_refer',
  /**
   * 应用表格 合并工作表每个文件汇总表数量上限
   */
  EditLimitTableMergeTableSummary = 'edit_limit_table_merge_table_summary',
  /**
   * 应用表格 每个数据表的个人视图数量
   */
  EditLimitTablePersonalView = 'edit_limit_table_personal_view',
  /**
   * 应用表格 可编辑文件体积上限
   */
  EditLimitTableSize = 'edit_limit_table_size',
  /**
   * 石墨格式 导出文件的边界条件
   * 思维导图
   */
  ExportLimitMindmapImgTheme = 'export_limit_mindmap_img_theme',
  /**
   * 石墨格式 导出文件的边界条件
   * 轻文档
   */
  ExportLimitRdocPixelHeight = 'export_limit_rdoc_pixel_height',
  /**
   * 所有套件	导入导出限时
   */
  ExportTimeout = 'export_timeout',
  /**
   * 历史版本 文件历史配额_按回溯时长计
   */
  HistoryLimitAllTime = 'history_limit_all_time',
  /**
   * 历史版本 文件版本配额
   */
  HistoryLimitAllVersionNum = 'history_limit_all_version_num',
  /**
   * 历史版本 单元格历史_按回溯时长计（表格）
   */
  HistoryLimitMosheetCellTime = 'history_limit_mosheet_cell_time',
  /**
   * 应用表格 全局历史
   */
  HistoryLimitTable = 'history_limit_table',
  /**
   * 应用表格 单元格历史
   */
  HistoryLimitTableCell = 'history_limit_table_cell',
  /**
   * 历史版本 单元格历史_按回溯时长计（应用表格）
   */
  HistoryLimitTableCellTime = 'history_limit_table_cell_time',
  /**
   * 应用表格 行动态历史
   */
  HistoryLimitTableRow = 'history_limit_table_row',
  /**
   * 历史版本 应用表格行动态_按回溯时长计
   */
  HistoryLimitTableRowTime = 'history_limit_table_row_time',
  /**
   * 传统文档 导入大小限制
   */
  ImportLimitModocSize = 'import_limit_modoc_size',
  /**
   * 传统文档 导入条件限制
   */
  ImportLimitModocWord = 'import_limit_modoc_word',
  /**
   * 表格 导入条件限制 （例：所有 Sheets 总和有效单元格数量 500W）
   */
  ImportLimitMosheetAllSheetCell = 'import_limit_mosheet_all_sheet_cell',
  /**
   * 表格 导入条件限制 （例：整体xml文件大小限制 300M）
   */
  ImportLimitMosheetAllXmlSize = 'import_limit_mosheet_all_xml_size',
  /**
   * 表格 导入条件限制 （例：转换后的石墨格式体积小于 100M）
   */
  ImportLimitMosheetConvertedSize = 'import_limit_mosheet_converted_size',
  /**
   * 表格 导入条件限制 （例：单个 Sheet 有效单元格数量 200W）
   */
  ImportLimitMosheetSingleSheetCell = 'import_limit_mosheet_single_sheet_cell',
  /**
   * 表格 导入条件限制 （例：单个xml文件大小限制 20M）
   */
  ImportLimitMosheetSingleXmlSize = 'import_limit_mosheet_single_xml_size',
  /**
   * 表格 导入大小限制
   */
  ImportLimitMosheetSize = 'import_limit_mosheet_size',
  /**
   * 幻灯片	导入条件限制 （例：2000 张 slides(页面)）
   */
  ImportLimitPresentationPage = 'import_limit_presentation_page',
  /**
   * 幻灯片	导入条件限制 （例：每个 slide 最多 500 个形状）
   */
  ImportLimitPresentationShape = 'import_limit_presentation_shape',
  /**
   * 幻灯片	导入大小限制
   */
  ImportLimitPresentationSize = 'import_limit_presentation_size',
  /**
   * 轻文档	导入大小限制
   */
  ImportLimitRdocSize = 'import_limit_rdoc_size',
  /**
   * 轻文档	导入条件限制 （例： 30W字符（character））
   */
  ImportLimitRdocWord = 'import_limit_rdoc_word',
  /**
   * kafka	第二层拦截：超过 Kafka 9M 限制
   */
  PasteLimit = 'paste_limit',
  /**
   * 复制粘贴 传统文档 （20w 文本（char ？））
   */
  PasteLimitModoc = 'paste_limit_modoc',
  /**
   * 复制粘贴 表格 （20w 文本（char ？））
   */
  PasteLimitMosheet = 'paste_limit_mosheet',
  /**
   * 复制粘贴 幻灯片 （20w 文本（char ？））
   */
  PasteLimitPresentation = 'paste_limit_presentation',
  /**
   * 复制粘贴 轻文档 （20w 文本（char ？））
   */
  PasteLimitRdoc = 'paste_limit_rdoc',
  /**
   * 复制粘贴 应用表格 （20w 文本（char ？））
   */
  PasteLimitTable = 'paste_limit_table',
  /**
   * 表格 跨表格引用卡点
   */
  EditLimitMosheetFuncRefer = 'edit_limit_mosheet_func_refer',
}

export enum UserFeature {
  /**
   * 批量下载
   */
  CloudBatchDownload = 'cloud_batch_download',
  /**
   * 解压缩文件
   */
  CloudUnzip = 'cloud_unzip',
  /**
   * 缩略图
   */
  CloudThumbnail = 'cloud_thumbnail',
  /**
   * PDF转word
   */
  OutputConversionToolboxPdf2word = 'output_conversion_toolbox_pdf2word',
  /**
   * PDF转excel
   */
  OutputConversionToolboxPdf2excel = 'output_conversion_toolbox_pdf2excel',
  /**
   * PDF转PPT
   */
  OutputConversionToolboxPdf2ppt = 'output_conversion_toolbox_pdf2ppt',
  /**
   * 效能看板
   */
  PerformancePanel = 'performance_panel',
  /**
   * 是否启用传统文档
   */
  SupportModoc = 'support_modoc',
  /**
   * 是否启用轻文档
   */
  SupportRdoc = 'support_rdoc',
  /**
   * 是否启用表格
   */
  SupportMosheet = 'support_mosheet',
  /**
   * 是否启用应用表格
   */
  SupportTable = 'support_table',
  /**
   * 是否启用幻灯片
   */
  SupportPresentation = 'support_presentation',
  /**
   * 是否启用表单
   */
  SupportForm = 'support_form',
  /**
   * 是否启用txt 预览/导入 为轻文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportRdocTxt = 'import_rdoc_txt',
  /**
   * 是否启用md 预览/导入 为轻文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportRdocMd = 'import_rdoc_md',
  /**
   * 是否启用doc 预览/导入 为轻文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportRdocDoc = 'import_rdoc_doc',
  /**
   * 是否启用docx 预览/导入 为轻文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportRdocDocx = 'import_rdoc_docx',
  /**
   * 是否启用doc 预览/导入 为传统文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportModocDoc = 'import_modoc_doc',
  /**
   * 是否启用docx 预览/导入 为传统文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportModocDocx = 'import_modoc_docx',
  /**
   * 是否启用wps 预览/导入 为传统文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportModocWps = 'import_modoc_wps',
  /**
   * 是否启用wpt 预览/导入 为传统文档
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportModocWpt = 'import_modoc_wpt',
  /**
   * 是否启用xlsm 预览/导入 为表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportMosheetXlsm = 'import_mosheet_xlsm',
  /**
   * 是否启用csv 预览/导入 为表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportMosheetCsv = 'import_mosheet_csv',
  /**
   * 是否启用xls 预览/导入 为表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportMosheetXls = 'import_mosheet_xls',
  /**
   * 是否启用xlsx 预览/导入 为表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportMosheetXlsx = 'import_mosheet_xlsx',
  /**
   * 是否启用ppt 预览/导入 为幻灯片
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportPresentationPpt = 'import_presentation_ppt',
  /**
   * 是否启用pptx 预览/导入 为幻灯片
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportPresentationPptx = 'import_presentation_pptx',
  /**
   * 是否启用xls 预览/导入 为应用表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportTableXls = 'import_table_xls',
  /**
   * 是否启用xlsx 预览/导入 为应用表格
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportTableXlsx = 'import_table_xlsx',
  /**
   * 是否启用xmind 预览/导入 为脑图
   *（云文件类型 预览/导入 为石墨格式）
   */
  ImportMindmapXmind = 'import_mindmap_xmind',
  /**
   * 是否支持传统文档导出为 docx
   * （石墨格式 下载/导出 文件类型）
   */
  ExportModocDocx = 'export_modoc_docx',
  /**
   * 是否支持传统文档导出为 wps
   * （石墨格式 下载/导出 文件类型）
   */
  ExportModocWps = 'export_modoc_wps',
  /**
   * 是否支持传统文档导出为 pdf
   * （石墨格式 下载/导出 文件类型）
   */
  ExportModocPdf = 'export_modoc_pdf',
  /**
   * 是否支持传统文档导出为 pdf纯图
   * （石墨格式 下载/导出 文件类型）
   */
  ExportModocPdfImg = 'export_modoc_pdf_img',
  /**
   * 是否支持传统文档导出为 图片
   * （石墨格式 下载/导出 文件类型）
   */
  ExportModocImg = 'export_modoc_img',
  /**
   * 是否支持轻文档导出为 md文件
   * （石墨格式 下载/导出 文件类型）
   */
  ExportRdocMd = 'export_rdoc_md',
  /**
   * 是否支持轻文档导出为 docx
   * （石墨格式 下载/导出 文件类型）
   */
  ExportRdocDocx = 'export_rdoc_docx',
  /**
   * 是否支持轻文档导出为 图片
   * （石墨格式 下载/导出 文件类型）
   */
  ExportRdocImg = 'export_rdoc_img',
  /**
   * 是否支持轻文档导出为 pdf
   * （石墨格式 下载/导出 文件类型）
   */
  ExportRdocPdf = 'export_rdoc_pdf',
  /**
   * 是否支持表格导出为 zip
   * （石墨格式 下载/导出 文件类型）
   */
  ExportMosheetZip = 'export_mosheet_zip',
  /**
   * 是否支持表格导出为 xls
   * （石墨格式 下载/导出 文件类型）
   */
  ExportMosheetXls = 'export_mosheet_xls',
  /**
   * 是否支持表格导出为 pdf纯图
   * （石墨格式 下载/导出 文件类型）
   */
  ExportMosheetPdfImg = 'export_mosheet_pdf_img',
  /**
   * 是否支持表格导出为 图片
   * （石墨格式 下载/导出 文件类型）
   */
  ExportMosheetImg = 'export_mosheet_img',
  /**
   * 是否支持幻灯片导出为 pptx
   * （石墨格式 下载/导出 文件类型）
   */
  ExportPresentationPptx = 'export_presentation_pptx',
  /**
   * 是否支持幻灯片导出为 pdf纯图
   * （石墨格式 下载/导出 文件类型）
   */
  ExportPresentationPdfImg = 'export_presentation_pdf_img',
  /**
   * 是否支持幻灯片导出为 图片
   * （石墨格式 下载/导出 文件类型）
   */
  ExportPresentationImg = 'export_presentation_img',
  /**
   * 是否支持幻灯片导出为 pdf
   * （石墨格式 下载/导出 文件类型）
   */
  ExportPresentationPdf = 'export_presentation_pdf',
  /**
   * 是否支持应用表格导出为 xlsx
   * （石墨格式 下载/导出 文件类型）
   */
  ExportTableXlsx = 'export_table_xlsx',
}

type Quotas = {
  [key in UserQuota | string]?: AttachLimit;
};

export interface CheckpointType {
  /**
   * 当features字段存在但在里面找不到key时则证明该功能点未开启
   */
  features: Array<keyof UserFeature | string>;
  /**
   * 所有的quotas都会被返回
   */
  quotas: Quotas;
}

export interface CheckpointRes {
  data: CheckpointType;
  requestId: string;
}
