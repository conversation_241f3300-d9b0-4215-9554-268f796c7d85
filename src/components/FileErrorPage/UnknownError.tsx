import { useFormatMessage } from '@/modules/Locale';

import ErrorPageLayouts from './ErrorPageLayouts';

export const UnknownError = ({ errorMessage }: { errorMessage?: string }) => {
  const unknownErrorTitle = useFormatMessage('Error.unknownErrorTitle');
  const unknownErrorSubTitle = useFormatMessage('Error.unknownErrorSubTitle');

  return <ErrorPageLayouts subTitle={errorMessage || unknownErrorSubTitle} title={unknownErrorTitle} />;
};
