import { AccessRestricted } from './AccessRestricted';
import { FileDelete } from './FileDelete';
import { FileNoPermission } from './FileNoPermission';
import { FileNotFound } from './FileNotFound';
import { LoginGuide } from './LoginGuide';
import { NoSeats } from './NoSeats';
import { UnknownError } from './UnknownError';

interface ErrorProps {
  guid?: string;
  errorCode?: number;
  errorMessage?: string;
  fileName?: string;
}

export const FileErrorPage = (props: ErrorProps) => {
  const { errorCode, fileName, errorMessage, guid } = props;
  switch (errorCode) {
    case 60093:
      return <LoginGuide />;
    case 60001:
      return <FileNotFound />;
    case 60002:
      return <FileDelete fileName={fileName} />;
    case 20025:
    case 403:
      return <FileNoPermission fileName={fileName} guid={guid} />;
    case 1:
      return <AccessRestricted errorMessage={errorMessage} />;
    case 31000:
      return <NoSeats />;

    default:
      return <UnknownError errorMessage={errorMessage} />;
  }
};
