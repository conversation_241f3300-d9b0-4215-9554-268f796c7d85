import { Button } from 'antd';
import React from 'react';

import styles from './ErrorPageLayouts.less';

interface ErrorPageLayoutsProps {
  title?: string;
  subTitle?: string;
  buttons?: {
    label: string;
    onClick: () => void;
    type?: 'primary' | 'default' | 'link' | 'text' | 'dashed';
  }[];
}

const ErrorPageLayouts: React.FC<ErrorPageLayoutsProps> = (props: ErrorPageLayoutsProps) => {
  const { title, subTitle, buttons } = props;
  return (
    <div className={styles.errorPageLayouts}>
      <h1>{title}</h1>
      <h2>{subTitle}</h2>
      {buttons?.map((button, index) => (
        <Button key={index} type={button.type} onClick={button.onClick}>
          {button.label}
        </Button>
      ))}
    </div>
  );
};

export default ErrorPageLayouts;
