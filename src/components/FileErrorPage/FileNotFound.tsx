import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import ErrorPageLayouts from './ErrorPageLayouts';

export const FileNotFound = () => {
  const meId = useMeStore((state) => state.me.id);
  const file404Title = useFormatMessage('Error.file404Title');
  const loginText = useFormatMessage('Error.loginText');
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');

  return (
    <ErrorPageLayouts
      buttons={[
        meId
          ? {
              label: goBackHomePage,
              onClick: () => {
                history.push('/');
              },
            }
          : {
              label: loginText,
              onClick: () => {
                history.push('/login');
              },
            },
      ]}
      subTitle={file404Title}
      title={'404'}
    />
  );
};
