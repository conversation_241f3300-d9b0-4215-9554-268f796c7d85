import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import ErrorPageLayouts from './ErrorPageLayouts';

export const FileDelete = ({ fileName }: { fileName?: string }) => {
  const meId = useMeStore((state) => state.me.id);
  const loginText = useFormatMessage('Error.loginText');
  const file = useFormatMessage('File.file');
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const fileDeleteTitle = useFormatMessage('Error.fileDeleteTitle');
  const fileDeleteSubTitle = useFormatMessage('Error.fileDeleteSubTitle', {
    filename: fileName ?? file,
  });

  return (
    <ErrorPageLayouts
      buttons={[
        meId
          ? {
              label: goBackHomePage,
              onClick: () => {
                history.push('/');
              },
            }
          : {
              label: loginText,
              onClick: () => {
                history.push('/login');
              },
            },
      ]}
      subTitle={fileDeleteSubTitle}
      title={fileDeleteTitle}
    />
  );
};
