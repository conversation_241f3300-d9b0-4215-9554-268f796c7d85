import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import ErrorPageLayouts from './ErrorPageLayouts';

export const FileNoPermission = ({ fileName, guid }: { fileName?: string; guid?: string }) => {
  const me = useMeStore((state) => state.me);
  const loginText = useFormatMessage('Error.loginText');
  const document = useFormatMessage('File.document');
  const fileNoPermissionTitle = useFormatMessage('Error.fileNoPermissionTitle');
  const fileNoPermissionSubTitle = useFormatMessage('Error.fileNoPermissionSubTitle', {
    name: me.name,
    // FIXME: guid只为测试，后期需要删除
    fileName: `${fileName ?? document} ${guid ?? ''}`,
  });
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const switchAccount = useFormatMessage('Error.switchAccount');

  return (
    <ErrorPageLayouts
      buttons={[
        me.id
          ? {
              label: goBackHomePage,
              type: 'primary',
              onClick: () => {
                history.push('/');
              },
            }
          : {
              label: loginText,
              onClick: () => {
                history.push('/login');
              },
            },
        {
          label: switchAccount,
          onClick: () => {
            history.push('/logout');
          },
        },
      ]}
      subTitle={fileNoPermissionSubTitle}
      title={fileNoPermissionTitle}
    />
  );
};
