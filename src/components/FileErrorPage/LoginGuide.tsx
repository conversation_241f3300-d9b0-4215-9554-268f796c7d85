import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';

import ErrorPageLayouts from './ErrorPageLayouts';

export const LoginGuide = () => {
  const loginGuideTitle = useFormatMessage('Error.loginGuideTitle');
  const loginText = useFormatMessage('Error.loginText');

  return (
    <ErrorPageLayouts
      buttons={[
        {
          label: loginText,
          onClick: () => {
            history.push('/login');
          },
        },
      ]}
      title={loginGuideTitle}
    />
  );
};
