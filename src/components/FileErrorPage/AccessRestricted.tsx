import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';

import ErrorPageLayouts from './ErrorPageLayouts';

export const AccessRestricted = ({ errorMessage }: { errorMessage?: string }) => {
  const loginText = useFormatMessage('Error.loginText');
  const accessRestrictedTitle = useFormatMessage('Error.accessRestrictedTitle');

  return (
    <ErrorPageLayouts
      buttons={[
        {
          label: loginText,
          onClick: () => {
            history.push('/login');
          },
        },
      ]}
      subTitle={errorMessage}
      title={accessRestrictedTitle}
    />
  );
};
