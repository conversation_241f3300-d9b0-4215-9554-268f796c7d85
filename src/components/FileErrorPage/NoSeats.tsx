import { history } from 'umi';

import { useFormatMessage } from '@/modules/Locale';

import ErrorPageLayouts from './ErrorPageLayouts';

export const NoSeats = () => {
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const noSeatsTitle = useFormatMessage('Error.noSeatsTitle');

  return (
    <ErrorPageLayouts
      buttons={[
        {
          label: goBackHomePage,
          onClick: () => {
            history.push('/');
          },
        },
      ]}
      subTitle={noSeatsTitle}
    />
  );
};
