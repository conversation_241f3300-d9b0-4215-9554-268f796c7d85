.commonItem {
  padding: 0 16px;
  height: 36px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }

  .itemContent {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: space-between;

    .itemIcon {
      display: flex;
      align-items: center;
      margin-left: auto;
    }

    span {
      color: var(--theme-text-color-default);
      font-size: 13px;
      font-weight: 400;
      line-height: 1;
    }
  }

  &.disabled {
    cursor: not-allowed;
    background-color: initial;

    .itemContent {
      span {
        color: var(--theme-text-color-disabled);
      }
    }
  }

  &.favorite {
    .itemContent {
      span {
        color: var(--theme-text-color-guidance);
      }
    }
  }

  &.danger {
    .itemContent {
      span {
        color: var(--theme-text-color-alert);
      }
    }
  }

  .submenuIcon {
    margin-left: 8px;
    font-size: 12px;
    color: var(--theme-text-color-secondary);
  }
}

.container {
  width: 220px;
  overflow: hidden;
  background-color: var(--theme-menu-color-bg-default);

  .section {
    padding: 4px 0;
    min-width: 200px;
  }

  .item {
    .commonItem();
  }

  .divider {
    height: 1px;
    border-top: 1px solid var(--theme-separator-color-lighter);
    margin: 4px 16px;
  }
}

/* 子菜单样式 */
.submenuContent {
  background-color: var(--theme-menu-color-bg-default);
  padding: 5px 0;
  width: 220px;

  .item {
    .commonItem();
  }
}
