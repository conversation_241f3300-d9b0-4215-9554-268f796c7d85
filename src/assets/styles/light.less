:root {
  --brand-color: #41464b;
  --red10: #af3a37;
  --red9: #c04140;
  --red8: #cd4747;
  --red7: #e0504e;
  --red6: #e95555;
  --red5: #e96465;
  --red4: #de7f83;
  --red3: #e9a2a5;
  --red2: #fad2d8;
  --red1: #fdedf0;
  --orange10: #ab3300;
  --orange9: #c24100;
  --orange8: #cf4900;
  --orange7: #dc5000;
  --orange6: #e65600;
  --orange5: #ed6b00;
  --orange4: #f3833a;
  --orange3: #faa573;
  --orange2: #fdc8ab;
  --orange1: #fde7e1;
  --yellow10: #d78539;
  --yellow9: #e0a849;
  --yellow8: #e5bf52;
  --yellow7: #e9d45c;
  --yellow6: #ebe361;
  --yellow5: #eee874;
  --yellow4: #f1ed8b;
  --yellow3: #f4f2aa;
  --yellow2: #f8f7cb;
  --yellow1: #fcfcea;
  --green10: #1e541d;
  --green9: #30732e;
  --green8: #3a8438;
  --green7: #459542;
  --green6: #4ea44b;
  --green5: #66b165;
  --green4: #80be7f;
  --green3: #a4d0a3;
  --green2: #c7e2c7;
  --green1: #e8f3e8;
  --cyan10: #006c72;
  --cyan9: #008fa0;
  --cyan8: #00a3b8;
  --cyan7: #00b9d4;
  --cyan6: #00c9e8;
  --cyan5: #00d2ec;
  --cyan4: #37d9f0;
  --cyan3: #7ae6f5;
  --cyan2: #b1eff8;
  --cyan1: #e0f9fd;
  --blue10: #2d448f;
  --blue9: #3861af;
  --blue8: #3e72c1;
  --blue7: #4583d4;
  --blue6: #5ba0e7;
  --blue5: #6da0e3;
  --blue4: #73b1eb;
  --blue3: #98c7f1;
  --blue2: #bfdcf7;
  --blue1: #e4f1fb;
  --purple10: #000a6d;
  --purple9: #10147b;
  --purple8: #221a83;
  --purple7: #31218b;
  --purple6: #3a2590;
  --purple5: #59439e;
  --purple4: #7562ab;
  --purple3: #9c8ec1;
  --purple2: #c3bbda;
  --purple1: #e7e3f0;
  --gray120: #2c3033;
  --gray110: #363b3e;
  --gray100: #41464b;
  --gray90: #54585d;
  --gray80: #676b6f;
  --gray70: #7a7e81;
  --gray60: #8d9093;
  --gray50: #a0a2a5;
  --gray40: #b3b5b7;
  --gray30: #c6c8c9;
  --gray20: #d9dadb;
  --gray10: #eceded;
  --gray9: #eee;
  --gray8: #f1f1f1;
  --gray7: #f2f2f2;
  --gray6: #f4f4f4;
  --gray5: #f6f6f6;
  --gray4: #f7f7f7;
  --gray3: #f9f9f9;
  --gray2: #fbfbfb;
  --gray1: #fdfdfd;
  --gray0: #fff;
  --transparency120: #2c3033;
  --transparency110: #363b3e;
  --transparency100: rgba(65, 70, 75, 100%);
  --transparency90: rgba(65, 70, 75, 90%);
  --transparency80: rgba(65, 70, 75, 80%);
  --transparency70: rgba(65, 70, 75, 70%);
  --transparency75: rgba(65, 70, 75, 75%);
  --transparency60: rgba(65, 70, 75, 60%);
  --transparency50: rgba(65, 70, 75, 50%);
  --transparency40: rgba(65, 70, 75, 40%);
  --transparency30: rgba(65, 70, 75, 30%);
  --transparency20: rgba(65, 70, 75, 20%);
  --transparency15: rgba(65, 70, 75, 15%);
  --transparency10: rgba(65, 70, 75, 10%);
  --transparency8: rgba(65, 70, 75, 8%);
  --transparency5: rgba(65, 70, 75, 5%);
  --transparency6: rgba(65, 70, 75, 6%);
  --transparency4: rgba(65, 70, 75, 4%);
  --transparency2: rgba(65, 70, 75, 2%);
  --theme-brand-color: var(--brand-color);
  --input-border-shadow: 0 2px 8px 0 rgba(0, 0, 0, 4%);

  // 语义化
  // 基本配色
  --theme-basic-color-primary: var(--brand-color);
  --theme-basic-color-notice: var(--blue5);
  --theme-basic-color-success: var(--green6);
  --theme-basic-color-warning: var(--yellow7);
  --theme-basic-color-alert: var(--red6);
  --theme-basic-color-guidance: var(--blue6);
  --theme-basic-color-lighter: var(--transparency10);
  --theme-basic-color-light: var(--transparency20);
  --theme-basic-color-black: var(--transparency30);
  --theme-basic-color-bg-default: var(--gray0);

  // decorated
  --theme-decorated-color-february: var(--green5);
  --theme-decorated-color-april: var(--purple3);
  --theme-decorated-color-june: var(--red4);
  --theme-decorated-color-july: var(--orange4);
  --theme-decorated-color-october: #c3ab7c;
  --theme-decorated-color-november: var(--cyan3);
  --theme-decorated-color-december: var(--blue9);

  // 文本 icon
  --theme-text-color-header: var(--transparency120);
  --theme-text-color-default: var(--transparency100);
  --theme-text-color-medium: var(--transparency80);
  --theme-text-color-secondary: var(--transparency60);
  --theme-text-color-disabled: var(--transparency30);
  --theme-text-color-white: var(--gray0);
  --theme-text-color-alert: var(--red6);
  --theme-text-color-warn: var(--orange3);
  --theme-text-color-succeed: var(--green6);
  --theme-text-color-guidance: var(--blue6);
  --theme-text-color-highlight-bg: var(--blue2);
  --theme-text-color-deep: var(--gray120);
  --theme-icon-info-color: var(--transparency30);

  // 按钮
  --theme-button-color-primary: var(--blue6);
  --theme-button-color-primary-hover: var(--blue7);
  --theme-button-color-primary-active: var(--blue8);
  --theme-button-color-disabled: var(--gray5);
  --theme-button-color-default: var(--gray5);
  --theme-button-color-alert: var(--red6);
  --theme-button-color-alert-hover: var(--red7);
  --theme-button-color-alert-active: var(--red8);
  --theme-link-button-color: var(--blue5);
  --theme-link-button-color-hover: var(--blue7);
  --theme-link-button-color-active: var(--blue8);
  --theme-link-button-color-disabled: var(--transparency30);
  --theme-drive-button-color: var(--transparency100);
  --theme-drive-button-color-hover: var(--transparency80);
  --theme-drive-button-color-active: var(--transparency120);
  --theme-text-button-color-hover: var(--transparency5);
  --theme-text-button-color-active: var(--transparency10);

  // 分割线
  --theme-separator-color-black: var(--transparency30);
  --theme-separator-color-light: var(--transparency20);
  --theme-separator-color-lighter: var(--transparency10);
  --theme-separator-color-guidance: var(--blue6);

  // 遮罩
  --theme-mask-layer-color-light: var(--transparency50);
  --theme-mask-layer-color-dark: var(--transparency80);

  // 菜单 列表 类
  --theme-menu-color-bg-default: var(--gray0);
  --theme-menu-color-bg-hover: var(--transparency5);
  --theme-menu-color-bg-active: var(--transparency8);

  // 信息card
  --theme-card-info-bg-hover: var(--gray4);
  --theme-card-info-bg-active: var(--transparency10);
  --theme-card-info-bg-hover-border: var(--transparency10);
  --theme-card-info-bg-active-border: var(--transparency20);

  // 表格
  --theme-table-color-header-bg: var(--gray0);
  --theme-table-color-header-gray-bg: var(--gray8);
  --theme-table-color-body-bg: var(--gray0);

  // checkbox
  --theme-checkbox-color: var(--brand-color);
  --theme-checkbox-color-check: var(--gray0);

  // layout 布局容器背景
  --theme-layout-color-bg-white: var(--gray0);
  --theme-layout-color-bg-new-page: var(--gray3);
  --theme-layout-color-bg-editor: var(--gray4);
  --theme-layout-color-bg-black: #000;

  // date picker
  --theme-datepicker-color-cell-active-bg: var(--blue6);
  --theme-datepicker-color-cell-active-range-bg: var(--blue1);

  // box-shadow
  --theme-box-shadow-color-level10: var(--transparency10);
  --theme-box-shadow-color-level8: var(--transparency8);
  --theme-box-shadow-color-level6: var(--transparency6);
  --theme-box-shadow-color-level4: var(--transparency4);

  // status
  --theme-status-color-bg-disabled: var(--transparency10);
  --theme-status-color-bg-notice: var(--blue2);
  --theme-status-color-bg-success: var(--green2);
  --theme-status-color-bg-warning: var(--yellow2);
  --theme-status-color-bg-alert: var(--red2);
  --theme-status-color-bg-guidance: var(--blue2);

  // chart
  --theme-chart-color-blue: var(--blue3);
  --theme-chart-color-yellow: var(--yellow3);
  --theme-chart-color-green: var(--green2);
  --theme-chart-color-gray: var(--gray8);
  --theme-chart-color-cyan: var(--cyan2);
  --theme-chart-color-orange: var(--orange2);
  --theme-chart-color-red: var(--red3);
  --theme-chart-color-purple: var(--purple2);
  --theme-chart-color-bg: var(--gray4);
  --theme-chart-panel-color-bg-gray: var(--gray3);
  --theme-chart-panel-color-over-bg-red: var(--red1);
  --theme-chart-tip-color-bg: var(--gray0);
  --theme-chart-tip-color-text: var(--gray100);

  // select 组件
  --theme-select-color-selected-bg: var(--transparency10);
  --theme-select-color-active-bg: var(--transparency5);
  --theme-select-color-active-border: var(--transparency30);
  --theme-select-color-active-shadow: var(--input-border-shadow);

  // input 组件
  --theme-input-color-active-border: var(--transparency30);
  --theme-input-color-active-shadow: var(--input-border-shadow);
  --theme-input-color-error-active-border: var(--red6);
  --theme-input-color-error-active-shadow: var(--red1);

  // input number 组件
  --theme-input-number-color-active-border: var(--transparency30);
  --theme-input-number-color-active-shadow: var(--input-border-shadow);
  --theme-input-number-color-error-active-border: var(--red6);
  --theme-input-number-color-error-active-shadow: var(--red1);

  // radio
  --theme-radio-color: var(--gray100);

  // breadcrumb
  --theme-breadcrumb-color-active: var(--gray120);

  // tooltip
  --theme-tooltip-color-bg: var(--gray100);
  --theme-tooltip-color-text: var(--gray0);

  // avatar
  --theme-avatar-color-bg: var(--gray60);

  // notice
  --theme-notice-color-bg: var(--blue1);

  // progress
  --theme-progress-color-bg: var(--blue6);
  --theme-progress-color-warn: var(--red6);

  // card
  --theme-card-guidance: var(--blue1);
  --theme-card-default: var(--gray4);

  // tag
  --theme-red-tag-color-bg: var(--red1);
  --theme-red-tag-color-border: var(--red2);
  --theme-red-tag-color-text: var(--red6);

  // efficiency
  --theme-efficiency-card-text-color: var(--transparency60);
  --theme-efficiency-card-unit-color: var(--transparency80);
  --theme-efficiency-card-ratio-color: var(--gray100);
  --theme-efficiency-card-color-bg: linear-gradient(272deg, #f1f5ff 0%, #f8faff 100%);
  --theme-efficiency-button-color-bg: linear-gradient(0deg, var(--gray120) -0.09%, var(--gray90) 100%);

  // image
  --user-center-bg: url('../images/common/<EMAIL>');
}
