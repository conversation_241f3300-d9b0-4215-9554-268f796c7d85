/**
 * 详解见 ./poof.ts
 */
// 第一部分：分解零宽数据,把零宽数据分割为3部分

// 第一部分：分解零宽数据,把零宽数据分割为3部分
const poofContents: string[] = [];

poofContents.push(
  '‭‫‪‬‎‏‭‫‬‭‫‭‭‫‪‭‪‬‪‏‬‬‭‫‪‫‎‪‏‬‪‏‬‫‪‭‫‫‭‫‫‭‫‫‭‫‫‭‫‫‭‫‏‪‫‏‭‫‏‏‬‪‫‬‫‪‪‏‬‬‫‬‬‬‫‫‏‪‬‪‎‬‪‫‫‏‫‪‏‬‬‪‭‫‏‭‬‬‏‫‫‭‫‫‭‫‫‭‫‫‭‫‫‭‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‬‪‏‬‪‫‬‪‫‫‏‪‬‪‫‬‏‎‫‎‏‬‫‪‫‏‪‬‏‫‬‏‏‭‪‏‬‏‬‬‏‏‬‏‭‫‏‏‫‭‭‭‫‏‫‬‪‫‏‪‫‎‏‬‫‭‫‏‭‫‏‎‫‎‏‫‎‏‬‫‫‫‏‫‫‎‏‬‫‭‫‭‬‫‎‏‬‪‏‬‪‫‬‪‫‫‏‪‫‏‫‬‏‫‬‪‭‫‏‫‫‎‏‬‫‭‫‏‭‫‎‏‫‬‬‫‬‫‫‭‭‫‭‪‫‬‫‭‫‭‭‬‪‬‫‏‬‬‏‭‫‏‬‬‬‭‪‭‫‬‫‬‫‏‫‫‫‫‬‪‫‬‏‬‭‪‬‪‎‭‪‫‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‭‪‪‭‪‎‫‬‪‫‏‭‭‪‭‫‭‭‬‭‪‫‬‪‫‬‏‫‭‫‬‬‎‭‪‎‭‬‬‫‫‫‬‪‪‭‫‭‭‫‪‬‬‭‭‪‪‬‫‏‬‪‪‫‭‪‭‪‎‬‪‬‫‏‬‫‬‭‬‪‏‫‏‬‬‪‪‭‫‪‬‬‬‭‬‪‬‎‎‫‬‎‬‏‪‫‏‫‬‪‬‬‬‬‬‫‫‬‏‏‬‪‪‫‫‏‭‪‎‫‬‫‫‬‫‭‪‬‬‏‫‬‏‎‬‏‬‭‬‬‬‪‎‭‪‪‬‪‭‫‏‏‭‬‪‬‫‭‫‏‭‫‬‬‭‪',
);

poofContents.push(
  '‬‬‏‫‭‬‫‬‪‎‭‫‪‫‬‫‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‫‫‫‫‭‪‫‫‫‬‫‪‬‏‫‬‎‬‫‏‫‬‫‬‫‏‬‬‪‫‭‪‏‬‫‭‬‪‎‭‬‫‬‎‎‬‏‭‬‫‬‫‏‭‫‭‬‫‭‬‫‏‏‬‫‬‭‪‫‫‫‫‬‬‭‫‎‏‫‏‬‭‪‪‫‏‫‬‪‭‫‬‬‫‬‎‬‪‭‫‏‪‭‬‫‫‬‫‬‎‬‭‪‪‭‬‬‬‏‫‬‫‬‫‬‪‬‏‭‬‫‬‫‏‬‫‏‫‬‬‪‬‬‭‫‏‭‬‪‎‬‏‏‭‫‫‭‫‭‬‪‫‭‫‬‭‪‎‭‪‭‬‬‏‭‫‫‭‫‭‬‬‏‫‭‪‬‫‫‬‎‫‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‬‎‫‬‫‎‫‎‏‬‎‫‭‬‪‬‪‫‬‎‫‬‏‬‬‪‬‭‪‫‬‬‬‭‫‫‫‭‭‬‏‫‫‎‏‫‏‎‬‬‭‭‬‪‭‪‪‬‬‎‭‬‫‭‫‪‬‭‪‭‫‎‫‬‏‭‪‫‬‭‪‫‏‭‬‏‪‬‫‪‭‬‫‬‎‎‭‫‭‫‏‭‬‏‎‫‏‪‭‬‫‬‏‬‭‪‎‫‎‏‭‫‭‬‪‏‬‎‏‫‬‫‬‏‎‬‫‫‬‫‫‭‪‬‫‏‫‬‎‭‫‬‫‭‬‪‬‬‏‫‬‪‭‬‪‬‬‬‫‏‏‫‏‫‬‏‫‬‬‪‬‬‏‬‎‫‫‭‪‭‪‭‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‫‏‎‬‪‏‭‫‫‫‬‪‫‭‪‫‬‪‭‪‬‬‫‫‫‎‏‬‎‫‬‫‭‬‎‫‬‪‪‭‫‏‫‭‭‫‬‎‭‬‬‬‪‪‬‫‎‫‏‏‬‎‭‫',
);

poofContents.push(
  '‭‬‫‭‬‬‎‭‬‪‭‬‏‫‬‬‫‭‫‫‭‫‪‭‪‬‬‫‏‫‏‏‬‫‫‬‫‪‭‪‭‫‭‬‫‫‏‭‪‎‫‏‬‬‏‭‭‪‏‫‬‭‬‫‭‬‫‏‬‪‏‬‎‎‫‬‭‬‎‬‬‪‬‬‪‏‭‫‎‬‬‭‬‎‭‫‫‫‭‫‏‬‪‭‭‫‏‬‫‏‭‬‪‬‬‭‬‭‪‫‏‏‫‬‭‫‬‏‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‫‭‫‬‬‬‬‬‎‫‬‎‫‬‭‫‬‏‬‎‏‬‬‏‫‭‫‭‪‫‬‬‪‫‭‫‬‎‎‫‭‪‫‏‎‬‎‎‭‫‬‭‫‪‭‬‪‭‪‬‬‪‭‬‪‬‫‏‎‫‬‭‬‎‭‬‪‭‬‫‭‫‏‪‬‪‎‬‏‭‫‏‬‫‭‬‬‏‫‫‬‫‫‎‏‭‪‬‬‬‭‫‬‎‬‭‪‬‫‏‫‭‬‬‫‪‬‏‬‭‫‫‭‫‫‫‬‏‬‏‏‬‎‬‭‪‭‭‪‏‬‎‎‬‎‏‭‪‪‬‭‪‭‫‎‬‎‫‬‭‪‫‏‫‭‫‪‬‏‪‭‪‭‬‬‬‫‏‪‬‬‎‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‫‏‬‬‫‭‬‪‫‫‏‬‫‎‏‬‫‭‫‎‏‫‏‪‫‪‭‫‫‬‪‫‎‪‏‬‪‏‬‫‪‭‫‫‭‫‫‭‫‫‭‫‫‭‫‫‭‫‏‭‬‫‪‫‏‬‪‏‬‬‫‬‬‬‫‫‏‪‬‪‎‬‪‫‫‏‫‪‏‬‬‪‭‫‏‭‬‬‏‫‫‭‫‫‭‫‫‭‫‫‭‫‫‭‫‪‭‫‫‬‪‫‎‬‭‭‫‫‎‬‏‎‭‪‭‬‏‭‭‪‬‫‪‎‫‪‭‬‭‬‭‪‬‫‪‭‫‪‏‫‭‏',
);

export { poofContents };
