/**
 * 下面代码通过 https://magicismight.github.io/poofjs/ 加密隐藏，
 * 原始代码为
return [
  '-----BEGIN PUBLIC KEY-----',
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArO/dNeieApO1AaM8fZYp',
  'xdi+vPyLazJtsGesymdzdH+lS97z+qPKGKMiDMwNykYsn3Ni2qPIa275YQoLrxvV',
  's1R0FCZoIDENNqHBBAfhRC4PRpubVnh+Wvy7c3t/YHbPyDRCDcIokydw7mrvSR28',
  'znTR05MN/kvU3LjSVRhCLxDQKnmdFSfw9OO1bM3m7pLP0mLLYkwpp/Noe44082st',
  'x87pQ1vKgCRcTMgn2/jIpk2mJFE9JJQH4gxOyd+oz0uxNaDUVkRnjeh2ptYC3ouu',
  'Jo98UD324+tqMQmpnZJ2SjrcrbLqNHSBiWdqmyIFW9tLrt47qjKtwnUj9v63qExM',
  'HwIDAQAB',
  '-----END PUBLIC KEY-----',
].join('\n');
*/

import { poofDecode } from './poofDecode';

// 第四部分：执行代码
export function getPublicKey(): string {
  return window.atob[
    [2, -10, -9, -14, -15, -13, -16, 2, -15, -10, -13]
      .map((code) => {
        return String.fromCharCode(~code + 102);
      })
      .join('') as 'constructor'
  ](poofDecode())();
}
