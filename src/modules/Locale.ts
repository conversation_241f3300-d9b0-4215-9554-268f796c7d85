/**
 * 处理国际化相关的逻辑
 */
import cookies from 'js-cookie';
import { createIntl, createIntlCache } from 'react-intl';
import { useIntl } from 'umi';

import enUS from '@/locales/en-US';
import zhCN from '@/locales/zh-CN';

export function setCookieLocale(lang: string) {
  cookies.set('locale', lang);
}

export function setStoredLocale(lang: string) {
  localStorage.setItem('locale', lang);
}

export function getStoredLocale(): string | null {
  return localStorage.getItem('locale');
}

export function removeStoredLocale() {
  localStorage.removeItem('locale');
}

export function useFormatMessage(
  id: string,
  values?: Record<string, string | number | boolean | null | undefined | Date>,
): string {
  const intl = useIntl();
  return intl.formatMessage({ id }, values);
}

export const fm = useFormatMessage;

const cache = createIntlCache();
const langMap = {
  'zh-CN': zhCN,
  'en-US': enUS,
};

// 非Hook版本，可以在组件外使用
export function fm2(id: string, values?: Record<string, string | number | boolean | null | undefined | Date>): string {
  const locale = getStoredLocale() || 'zh-CN';
  const intl = createIntl(
    {
      locale,
      messages: langMap[locale as keyof typeof langMap],
    },
    cache,
  );

  return intl.formatMessage({ id }, values);
}
