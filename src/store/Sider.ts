import { createStore, useStore } from 'zustand';

import type { SiderMenuItem } from '@/model/SiderMenuItem';
interface SelectedMenuItemState {
  selectedMenuItem: SiderMenuItem;
}

interface SelectedMenuItemAction {
  setSelectedMenuItem: (selectedMenuItem: SiderMenuItem) => void;
}

type SelectedMenuItemStore = SelectedMenuItemState & SelectedMenuItemAction;
export const selectedMenuItemStore = createStore<SelectedMenuItemStore>((set) => ({
  selectedMenuItem: {
    key: 'recent',
  },
  setSelectedMenuItem: (selectedMenuItem: SiderMenuItem) => set(() => ({ selectedMenuItem })),
}));

export const useSelectedMenuItemStore = <U>(selector: (state: SelectedMenuItemStore) => U) => {
  return useStore(selectedMenuItemStore, selector);
};
