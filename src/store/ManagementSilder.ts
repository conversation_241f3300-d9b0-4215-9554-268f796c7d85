import { useStore } from 'zustand';
import { createWithEqualityFn } from 'zustand/traditional';
import { shallow } from 'zustand/vanilla/shallow';

import type { MenuItem } from '@/model/Menus';
interface SelectedMenuStore {
  menus: MenuItem[];
  selectedMenuItem: Partial<MenuItem>;
  menusMap: Map<string, MenuItem[]>;
  setMenus: (menus: MenuItem[]) => void;
  setMenusMap: (menusMap: Map<string, MenuItem[]>) => void;
  setSelectedMenuItem: (selectedMenuItems: Partial<MenuItem>) => void;
}

export const menuStore = createWithEqualityFn<SelectedMenuStore>()(
  (set) => ({
    menus: [],
    menusMap: new Map(),
    selectedMenuItem: [],
    setMenus: (menus: MenuItem[]) => set(() => ({ menus })),
    setMenusMap: (menusMap) => set(() => ({ menusMap })),
    setSelectedMenuItem: (selectedMenuItem: Partial<MenuItem>) => set(() => ({ selectedMenuItem })),
  }),
  shallow,
);

export const useMenuStore = <U>(selector: (state: SelectedMenuStore) => U) => {
  return useStore(menuStore, selector);
};
