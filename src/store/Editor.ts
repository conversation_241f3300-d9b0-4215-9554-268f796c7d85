import { createStore, useStore } from 'zustand';

import type { FileStatusType } from '@/model/Editor';
import { SaveStatusType } from '@/model/Editor';

interface EditorState {
  saveStatusType: FileStatusType;
  fileType: string;
}

type EditorUpdate = Partial<EditorState>;

interface EditorStore extends EditorState {
  update: (state: EditorUpdate) => void;
}

export const editorStore = createStore<EditorStore>((set) => ({
  saveStatusType: SaveStatusType.ONLINE,
  fileType: '',
  update: (newState) => set((state) => ({ ...state, ...newState })),
}));

export const useEditorStore = <U>(selector: (state: EditorStore) => U) => {
  return useStore(editorStore, selector);
};
