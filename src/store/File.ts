import { createStore, useStore } from 'zustand';

import type { File } from '@/model/File';

const InitialFile: File = {
  guid: '',
  name: '',
  type: '',
};

interface FileState {
  file: File;
  files: File[];
}

interface FileAction {
  setFile: (file: File) => void;
  setFiles: (files: File[]) => void;
}

type FileStore = FileState & FileAction;

export const fileStore = createStore<FileStore>((set) => ({
  file: InitialFile,
  files: [],
  setFile: (file: File) => set(() => ({ file })),
  setFiles: (files: File[]) => set(() => ({ files })),
}));

export const useFileStore = <U>(selector: (state: FileStore) => U) => {
  return useStore(fileStore, selector);
};
