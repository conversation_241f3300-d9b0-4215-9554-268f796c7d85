import { createStore, useStore } from 'zustand';

interface Theme {
  isDark: boolean;
}

const InitialTheme: Theme = {
  isDark: false,
};

interface ThemeState {
  theme: Theme;
}

interface ThemeAction {
  setTheme: (theme: Theme) => void;
}

type ThemeStore = ThemeState & ThemeAction;

export const themeStore = createStore<ThemeStore>((set) => ({
  theme: InitialTheme,
  setTheme: (theme: Theme) => set(() => ({ theme })),
}));

export const useThemeStore = <U>(selector: (state: ThemeStore) => U) => {
  return useStore(themeStore, selector);
};
