import { createStore, useStore } from 'zustand';

import type { Me } from '@/model/Me';

const InitialMe: Me = {
  id: null,
  name: '',
  avatar: '',
  email: '',
  team: { name: '' },
};

const InitialFeatures: string[] = [];

interface MeState {
  me: Me;
  features: string[];
}

interface MeAction {
  setMe: (me: Me) => void;
  setFeatures: (features: string[]) => void;
}

type MeStore = MeState & MeAction;

export const meStore = createStore<MeStore>((set) => ({
  me: InitialMe,
  features: InitialFeatures,
  setMe: (me: Me) => set(() => ({ me })),
  setFeatures: (features: string[]) => set(() => ({ features })),
}));

export const useMeStore = <U>(selector: (state: MeStore) => U) => {
  return useStore(meStore, selector);
};
