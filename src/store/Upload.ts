import { createStore, useStore } from 'zustand';

export type StatusType = 'success' | 'fail' | 'cancel' | 'uploading' | 'waiting';
export interface UploadItem {
  message?: string;
  type: string;
  size: number;
  file: File;
  generateTempId: string | number;
  name: string;
  parentGuid: string;
  guid?: string;
  done?: boolean;
  progress?: number;
  status?: StatusType;
  controller: AbortController;
  // [key: string]: any;
}

interface UploadState {
  isShowBoard: boolean;
  selfCallback: any;
  uploadList: UploadItem[];
  controller: AbortController | null;
}

interface UploadAction {
  setIsShowBoard: (isShowBoard: boolean) => void;
  setUploadList: (uploadList: UploadItem[]) => void;
  setSelfCallback: (selfCallback: any) => void;
  setController: (controller: AbortController) => void;
}

type UploadStore = UploadState & UploadAction;

export const uploadStore = createStore<UploadStore>((set) => ({
  isShowBoard: false,
  uploadList: [],
  selfCallback: {},
  controller: null,
  setIsShowBoard: (isShowBoard: boolean) => set(() => ({ isShowBoard })),
  setUploadList: (uploadList: UploadItem[]) => set(() => ({ uploadList })),
  setSelfCallback: (selfCallback) => set(() => ({ selfCallback })),
  setController: (controller) => set(() => ({ controller })),
}));

export const useUploadStore = <U>(selector: (state: UploadStore) => U) => {
  return useStore(uploadStore, selector);
};
