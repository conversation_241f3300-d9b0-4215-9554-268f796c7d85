import Layout from 'antd/es/layout';
import { Content } from 'antd/es/layout/layout';
import type { PropsWithChildren } from 'react';

import SiderMenu from '@/components/Desktop/SiderMenu/index';
import DesktopHeader from '@/components/DesktopHeader';
import { useLoginGuard } from '@/hooks/Authorization';
import { useMeStore } from '@/store/Me';

import styles from './DesktopLayout.less';

export default function DesktopLayout(props: PropsWithChildren) {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;
  useLoginGuard();

  return notInitial ? null : (
    <div className={styles.layoutContainer}>
      <DesktopHeader />
      <Layout className={styles.styledLayout}>
        <SiderMenu />
        <Layout>
          <Content className={styles.styledContent}>{props.children}</Content>
        </Layout>
      </Layout>
    </div>
  );
}
