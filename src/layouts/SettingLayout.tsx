import './SettingLayout.less';

import { Button, Layout } from 'antd';
import { Content, Header } from 'antd/es/layout/layout';
import type { PropsWithChildren } from 'react';
import { history } from 'umi';

import { ReactComponent as ArrowLeftIcon } from '@/assets/images/svg/arrowLeft.svg';
import SettingSiderMenu from '@/components/AccountInfo';
import { useLoginGuard } from '@/hooks/Authorization';
import { useFormatMessage as $t } from '@/modules/Locale';
export default function SettingLayout({ children }: PropsWithChildren<object>) {
  const i18n = $t('Profile.title');
  useLoginGuard();
  const handleJumpToDesktop = () => {
    history.push('/desktop'); // 跳转到 desktop 页面
  };
  return (
    <Layout className="settingLayoutContainer">
      <Header className="settingHeader">
        <Button className="btnText" icon={<ArrowLeftIcon />} type="text" onClick={handleJumpToDesktop} />
        <div className="styleTitle">{i18n}</div>
      </Header>
      <Layout className="styledLayout">
        <SettingSiderMenu />
        <Content className="styledContent">{children}</Content>
      </Layout>
    </Layout>
  );
}
