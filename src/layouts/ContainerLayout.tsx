import Layout from 'antd/es/layout';
import { Content } from 'antd/es/layout/layout';
import type { PropsWithChildren } from 'react';

import { Header } from '@/components/ManagementDesktop/Header';
import { SideBar } from '@/components/ManagementDesktop/SiderMenu/index';
import { useLoginGuard } from '@/hooks/Authorization';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
/**
 * 桌面布局组件
 *
 * @param props 包含子组件的属性
 * @returns 渲染后的桌面布局组件
 */
export const ContainerLayout = (props: PropsWithChildren) => {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;
  useLoginGuard();

  return notInitial ? null : (
    <Layout>
      <Header title={$t('Management.enterpriseManagementSystem')} />
      <Layout>
        <SideBar />
        <Content>{props.children}</Content>
      </Layout>
    </Layout>
  );
};
