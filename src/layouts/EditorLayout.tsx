import type { PropsWithChildren } from 'react';

import { EditorHeader } from '@/components/EditorHeader';
import { useMeStore } from '@/store/Me';

import styles from './EditorLayout.less';

export default function EditorLayout(props: PropsWithChildren) {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;

  return notInitial ? null : (
    <div className={styles.myLayout}>
      <EditorHeader />
      <div className={styles.content} id="editor-content">
        {props.children}
      </div>
    </div>
  );
}
