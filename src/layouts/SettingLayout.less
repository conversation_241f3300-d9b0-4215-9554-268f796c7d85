.settingLayoutContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.settingHeader {
  background: var(--theme-layout-color-bg-white);
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 24px;
  box-shadow: 0 -1px 0 0 var(--theme-separator-color-lighter) inset;

  .ant-btn.ant-btn-icon-only {
    width: 24px;
    height: 24px;
    background-color: var(--theme-text-button-color-hover);
  }
}

.styleTitle {
  margin-left: 16px;
  font-weight: 500;
}

.styledLayout {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.styledContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  overflow: auto;
  max-width: 100%;
}

.ant-layout .ant-layout-content {
  background: var(--theme-layout-color-bg-new-page);
}
