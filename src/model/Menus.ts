import type { BreadcrumbItemType, BreadcrumbSeparatorType } from 'antd/es/breadcrumb/Breadcrumb';
export enum MenuKey {
  efficiency = 'efficiency',
  members = 'members',
  audit = 'audit',
  packages = 'packages',
  whitelist = 'whitelist',
  settings = 'settings',
  jump = 'jump',
}
export type MenuItem = {
  title: string;
  label: string;
  key: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
};

/**
 * GetItem 获取当前菜单
  GetAllItem 获取当前所有层级菜单
 */
export enum GetMenuTypeEnum {
  GetAllMenuItem,
  GetMenuItem,
}
export type BreadcrumbModel = Partial<BreadcrumbItemType & BreadcrumbSeparatorType>;
