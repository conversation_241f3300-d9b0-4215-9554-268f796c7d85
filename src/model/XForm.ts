import type { FormInstance, Rule } from 'antd/es/form';
import type { ColProps } from 'antd/es/grid';
// 定义表单项类型
export type XFormItemType = 'input' | 'select' | 'datePicker' | 'number' | 'switch' | 'textarea';

// 定义列配置接口
export interface XColumnItem {
  type?: XFormItemType;
  attrs?: Record<string, string | number | boolean | ((values: any) => void)>;
  name?: string;
  component?: React.ComponentType<any> | React.ReactNode;
  label?: string;
  rules?: Rule[];
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[]; // select 的选项
  colProps?: ColProps; // 响应式布局配置
  disabled?: boolean;
  hidden?: boolean;
  [key: string]: any; // 其他配置项
}

// 组件 Props 接口
export interface XFormProps {
  columns: XColumnItem[];
  initialValues?: Record<string, any>;
  onFinish?: (values: any) => void;
  onReset?: () => void;
  form?: FormInstance;
  layout?: 'horizontal' | 'vertical' | 'inline';
  disabled?: boolean;
  labelWidth?: number; // label 宽度，单位为 px
  labelPosition?: 'left' | 'right' | 'top'; // label 位置：左、右、上
}
