/**
 * 保存状态，主要区分两种场景：离线和在线。
 * ! 协作模块里对于「离线」和「在线」的定义和设备的网络状态无关，只代表和同步服务器的连接状态。
 * ! 在线（连接正常）不代表此时服务器状态一定正常，如果服务器此时处于大面积 5xx 状态但连接没有问题，也会认为是在线状态。
 * ! 离线（连接异常）不代表此时设备一定没有网络，只代表和同步服务器的连接异常。
 */
export enum SaveStatusType {
  /**
   * 初始化状态，没有产生任何提交或是没有收到任何变更推送前都属于此状态
   */
  /**
   * 在线，初始化时的默认状态，协作模块默认预计当前网络没有问题，
   * 只能从 OFFLINE 切换到该状态
   */
  ONLINE = 'online',
  /**
   * 离线，只能从 ONLINE 切换到该状态
   */
  OFFLINE = 'offline',
  /**
   * 离线场景下的保存状态
   */
  /**
   * 禁止离线编写，此时应将编辑器设置为只读，等待网络恢复后才能继续编辑
   */
  OFFLINE_DISABLED = 'offlineDisabled',
  /**
   * 离线保存中，在离线状态下收到编辑器变更后会立刻触发
   */
  OFFLINE_SAVING = 'offlineSaving',
  /**
   * 离线保存成功，此时已将离线保存的内容持久化到本地
   */
  OFFLINE_PERSIST_SUCCEED = 'offlinePersistSucceed',
  /**
   * 离线保存失败，此时应该将编辑器设置为只读，等待网络恢复后才能继续编辑
   */
  OFFLINE_PERSIST_FAILED = 'offlinePersistFailed',
  /**
   * 在线场景下的保存状态
   */
  /**
   * 正在保存到服务器
   */
  ONLINE_SAVING = 'onlineSaving',
  /**
   * 收到服务器消息积压事件，当前服务器保存内容出现拥塞
   */
  SAVE_PENDING = 'savePending',
  /**
   * 内容提交过程中出现致命错误，此时应该将编辑器设置为只读，并禁止后续的编辑操作
   */
  SAVE_FAILED = 'saveFailed',
  /**
   * 上一次提交保存成功，但本地还存在未提交的内容
   */
  SAVE_ACCEPTED = 'saveAccepted',
  /**
   * 保存成功，此时服务器已确认保存成功
   */
  SAVE_SUCCEED = 'saveSucceed',
  /**
   * 保存超时，主要是由于连接不稳定或服务器出现 429 5xx 导致，SAVE_TIMEOUT 的状态应该在收到服务器 accept 之后重置
   */
  SAVE_TIMEOUT = 'saveTimeout',
  /**
   * 等待确认超时，此时应该将编辑器设置为只读，并禁止后续的编辑操作
   * ! 这里的超时只针对等待确认这个场景：在 compose 请求成功发送后，并且和服务器连接状态正常的情况下，超过一定时间后仍然没有收到 accept 确认信息，此时这条内容可能在服务器端处理时出错，并且服务器端也没有正确的返回错误信息。这种情况下这部分本地数据很有可能再也无法正常合并到服务器上，应该禁止用户继续编辑，以免用户后续产生内容都无法保存
   */
  ACCEPT_TIMEOUT = 'acceptTimeout',
  /**
   * 他人的推送信息
   */
  /**
   * 应用他人变更成功
   */
  APPLY_SUCCEED = 'applySucceed',
  /**
   * 应用他人变更失败
   */
  APPLY_FAILED = 'applyFailed',
  /**
   * 本地版本过期，发生在大幅落后于服务器版本的情况下，如果不存在未保存成功的本地数据，这时应该终止所有操作，引导用户刷新页面
   */
  REV_EXCEED_LIMITATION = 'revExceedLimitation',
}

/**
 * 离线同步状态
 */
export enum SyncStatusType {
  /**
   * 初始化时的状态
   */
  SYNC_PENDING = 'syncPending',
  /**
   * 离线同步保存中
   */
  SYNC_SAVING = 'syncSaving',
  /**
   * 离线同步成功
   */
  SYNC_SUCCEED = 'syncSucceed',
  /**
   * 离线同步失败
   */
  SYNC_FAILED = 'syncFailed',
  /**
   * 正在更新版本数据版本
   */
  REV_UPDATING = 'revUpdating',
  /**
   * 更新成功
   */
  UPDATE_SUCCEED = 'updateSucceed',
  /**
   * 更新失败
   */
  UPDATE_FAILED = 'updateFailed',
}

/**
 * 文件状态类型的联合类型，用于支持保存状态和同步状态
 */
export type FileStatusType = SaveStatusType | SyncStatusType;
