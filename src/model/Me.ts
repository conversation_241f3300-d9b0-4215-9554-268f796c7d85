export interface Me {
  id: null | number;
  name: string;
  name_pinyin?: string;
  email?: string;
  avatar?: string;
  status?: 1 | 0;
  teamRole?: string;
  teamTime?: string;
  team_time?: string;
  team_role?: 'creator' | 'admin' | '';
  isSeat?: 1 | 0;
  is_seat?: 1 | 0;
  createdAt?: string;
  merged_into?: null;
  mergedInto?: null;
  teamId?: number;
  team_id?: number;
  mobile?: string;
  mobileAccount?: string;
  hasPassword?: boolean;
  accountMetadata?: {
    isExpired: boolean;
    isDingtalk: boolean;
    isWework: boolean;
    isEnterprise: boolean;
    isFreeEnterprise: boolean;
    expiredAt: {
      seconds: number;
      nanos: number;
    };
    isTrial: boolean;
    isPersonalPremium: boolean;
    isEnterprisePremium: boolean;
    isEnterpriseLight: boolean;
    editionId: number;
  };
  team?: {
    name: string;
    type?: string;
  };
  /**
   * 这个字段需要考虑国际化问题
   */
  editionName?: string;
  requiresIdentityVerification?: boolean;
}
