import { debounce } from 'lodash';
import { useEffect, useState } from 'react';

/**
 * 实时获取浏览器视口高度的自定义 Hook
 * @returns 当前视口高度（像素值）
 */
export const useViewportHeight = () => {
  // 处理 SSR 场景
  const isClient = typeof window !== 'undefined';

  // 初始化高度状态
  const [height, setHeight] = useState(() => {
    return isClient ? window.innerHeight : 0;
  });

  useEffect(() => {
    if (!isClient) return;

    let visualViewportHandler: ((e: Event) => void) | null = null;

    // 核心更新逻辑
    const updateHeight = () => {
      const newHeight = window.visualViewport?.height || window.innerHeight;
      setHeight(Math.floor(newHeight));
    };

    // 事件处理器选择
    const handleResize = debounce(updateHeight, 100);

    // 优先使用 visualViewport（移动端）
    if (window.visualViewport) {
      visualViewportHandler = handleResize;
      window.visualViewport.addEventListener('resize', visualViewportHandler);
    } else {
      window.addEventListener('resize', handleResize);
    }

    // 初始化时主动更新一次
    updateHeight();

    // 清理函数
    return () => {
      if (visualViewportHandler) {
        window.visualViewport?.removeEventListener('resize', visualViewportHandler);
      } else {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, [isClient]);

  return height;
};
