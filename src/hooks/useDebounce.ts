import { useCallback, useRef } from 'react';

// 自定义防抖 Hooks
const useDebounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number,
) => {
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 使用 useCallback 包裹返回的函数，避免每次渲染时创建新的函数
  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      // 如果已有定时器，清除它
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      // 设置新的定时器
      timerRef.current = setTimeout(() => {
        // 延迟时间过后执行回调函数
        callback(...args);
      }, delay);
    },
    [callback, delay],
  );

  return debouncedCallback;
};

export default useDebounce;
