import { countChars, countCharsWithoutSpaces, countWords } from '@shimo/text-counter';
import { Descriptions, Modal, Statistic, Typography } from 'antd';
import type { DescriptionsItemType } from 'antd/es/descriptions';
import { get } from 'lodash';
import { useCallback } from 'react';

import { fileDetail } from '@/api/File';
import { fm } from '@/modules/Locale';
import { formatFileSizeHuman, useFormatTime } from '@/utils/file';

type Itype = 'newdoc' | 'modoc' | 'mosheet' | 'table' | 'presentation' | 'form';

export const useFileDetail = () => {
  const { formatTime } = useFormatTime();
  const i18nText = {
    name: fm('useFileDetail.creator'),
    createdAt: fm('File.createdAt'),
    fileSize: fm('File.fileSize'),
    statisticWordCount: fm('useFileDetail.statisticWordCount'),
    charCount: fm('useFileDetail.charCount'),
    charCountWithoutSpaces: fm('useFileDetail.charCountWithoutSpaces'),
    page: fm('useFileDetail.page'),
    total: fm('useFileDetail.total'),
    paragraphCount: fm('useFileDetail.paragraphCount'),
    sections: fm('useFileDetail.sections'),
    views: fm('useFileDetail.views'),
    times: fm('useFileDetail.times'),
    okText: fm('UseFileUpload.noSpaceOkText'),
  };

  const typeTitleI18nText = {
    modoc: fm('useFileDetail.modocTitle'),
    newdoc: fm('useFileDetail.modocTitle'),
    mosheet: fm('useFileDetail.mosheetTitle'),
    table: fm('useFileDetail.tableTitle'),
    presentation: fm('useFileDetail.pptTitle'),
    form: fm('useFileDetail.formTitle'),
  };

  const openFileDetail = useCallback(({ guid, type = 'newdoc' }: { guid: string | undefined; type: Itype }) => {
    if (!guid) return;
    const editor = window.__SM__?.editor;

    /** 总字数 */
    let statisticWordCount = 0;
    /** 页数 */
    let pageCount = 0;
    /** 段落数 */
    let paragraphCount = 0;
    /** 字符数(计空格) */
    let charCount = 0;
    /** 字符数(不计空格) */
    let charCountWithoutSpaces = 0;
    /** 节数 */
    let sections = 0;
    /** 总字数(轻文档) */
    let statisticWordCount_newdoc = '';
    /** 字符数(计空格) (轻文档)*/
    let charCount_newdoc = '';
    /** 字符数(不计空格) (轻文档) */
    let charCountWithoutSpaces_newdoc = '';

    if (type === 'modoc') {
      const statisticPlugin = editor.docsApi.pluginManager.getInstance('Statistics');

      editor.attachEvent(
        'ON_DOC_INFO',
        (docDetails: {
          pageCount: number;
          paragraphCount: number;
          charCount: number;
          charCountWithoutSpaces: number;
        }) => {
          statisticWordCount = statisticPlugin?.statisticWordCount();

          pageCount = docDetails.pageCount;

          paragraphCount = docDetails.paragraphCount;

          charCount = docDetails.charCountWithoutSpaces;

          charCountWithoutSpaces = docDetails.charCount;
        },
      );

      // 开始统计，统计完了会触发上面的事件
      statisticPlugin?.startStatistics();

      /** 节数 */
      sections = editor?.docsApi.controller.docModel.SectionInfo.sections.length || 1;
    }

    if (type === 'newdoc') {
      const toFriendlyNumber = (num: number): string => {
        return num >= 1e10 ? num.toExponential(2) : num.toLocaleString();
      };
      let text = '';
      const win = window as any;
      if (get(win, 'pad.quill.getTextByDelta')) {
        text = win.pad.quill.getTextByDelta(win.pad.quill.getContents());
      } else {
        text = win.pad.quill.getTextByDelta(win.pad.quill.getContents());
      }
      statisticWordCount_newdoc = toFriendlyNumber(countWords(text));

      charCount_newdoc = toFriendlyNumber(countChars(text));

      charCountWithoutSpaces_newdoc = toFriendlyNumber(countCharsWithoutSpaces(text));
    }
    fileDetail(guid).then((res) => {
      if (res.status === 200) {
        const items = (): DescriptionsItemType[] => {
          const record = res.data;

          const itemMap: { [key: string]: DescriptionsItemType[] } = {
            newdoc: [
              {
                key: 'statisticWordCount',
                label: i18nText.statisticWordCount,
                span: 'filled',
                children: <Statistic value={statisticWordCount_newdoc} valueStyle={{ fontSize: '14px' }} />,
              },
              {
                key: 'charCount',
                label: i18nText.charCount,
                span: 'filled',
                children: <Statistic value={charCount_newdoc} valueStyle={{ fontSize: '14px' }} />,
              },
              {
                key: 'charCountWithoutSpaces',
                label: i18nText.charCountWithoutSpaces,
                span: 'filled',
                children: <Statistic value={charCountWithoutSpaces_newdoc} valueStyle={{ fontSize: '14px' }} />,
              },
            ],
            modoc: [
              {
                key: 'page',
                label: i18nText.page,
                span: 'filled',
                children: pageCount,
              },
              {
                key: 'total',
                label: i18nText.total,
                span: 'filled',
                children: <Statistic value={statisticWordCount} valueStyle={{ fontSize: '14px' }} />,
              },
              {
                key: 'charCount',
                label: i18nText.charCount,
                span: 'filled',
                children: <Statistic value={charCount} valueStyle={{ fontSize: '14px' }} />,
              },
              {
                key: 'characterCount',
                label: i18nText.charCountWithoutSpaces,
                span: 'filled',
                children: <Statistic value={charCountWithoutSpaces} valueStyle={{ fontSize: '14px' }} />,
              },
              {
                key: 'paragraphCount',
                label: i18nText.paragraphCount,
                span: 'filled',
                children: paragraphCount,
              },
              {
                key: 'sections',
                label: i18nText.sections,
                span: 'filled',
                children: sections,
              },
            ],
            mosheet: [],
            table: [],
            presentation: [],
            form: [],
          };
          return [
            {
              key: 'name',
              label: i18nText.name,
              span: 'filled',
              children: (
                <Typography.Text ellipsis={{ tooltip: record.user.name }} style={{ maxWidth: 260 }}>
                  {record.user.name}
                </Typography.Text>
              ),
            },
            {
              key: 'createdAt',
              label: i18nText.createdAt,
              span: 'filled',
              children: formatTime(record.createdAt * 1000, 'detail'),
            },
            {
              key: 'fileSize',
              label: i18nText.fileSize,
              span: 'filled',
              children: formatFileSizeHuman(record.fileSize),
            },
            ...itemMap[type],
            {
              key: 'views',
              label: i18nText.views,
              span: 1,
              children: `${record.views} ${i18nText.times}`,
            },
          ];
        };
        Modal.info({
          title: typeTitleI18nText[type],
          content: <Descriptions items={items()} />,
          okText: i18nText.okText,
          okButtonProps: {
            variant: 'solid',
            color: 'default',
            type: 'default',
          },
          centered: true,
          icon: null,
          closable: true,
        });
      }
    });
  }, []);

  return {
    openFileDetail,
  };
};
