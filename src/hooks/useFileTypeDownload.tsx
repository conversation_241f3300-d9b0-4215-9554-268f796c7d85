import { message } from 'antd';

import { getDownloadUrl } from '@/api/File';
import { fm } from '@/modules/Locale';
import { exportFileType, exportTableFile } from '@/utils/file';

export type DownLoadFileProps = {
  type: string;
  guid: string;
  fileName: string;
};

/** 转换为指定的文档类型（仅用于创建的） */
export const downloadType = ['jpeg', 'xmind', 'pptx', 'pdf', 'wps', 'word', 'md'];

export const useFileTypeDownload = () => {
  const i18nText = {
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
    loading: fm('Common.loadingText'),
  };

  const downloadDiffFile = ({ type, guid, fileName }: DownLoadFileProps) => {
    const custDownload = (guid: string) => {
      getDownloadUrl(guid).catch(() => {
        message.error(i18nText.downloadError);
      });
    };

    if (type === 'downloadOther') {
      // 下载自定义上传的文件
      custDownload(guid);
      return;
    }

    if (downloadType.includes(type)) {
      exportFileType({ guid: guid, data: { type: type }, name: fileName })
        .then(() => {
          message.success(i18nText.downloadSuccess);
        })
        .catch((error) => {
          message.error(error.data.msg || i18nText.downloadError);
        });
    } else if (['xlsx'].includes(type)) {
      exportTableFile({ guid: guid, name: fileName })
        .then(() => {
          message.success(i18nText.downloadSuccess);
        })
        .catch(() => {
          message.error(i18nText.downloadError);
        });
    } else {
      custDownload(guid);
    }
  };
  return {
    downloadDiffFile,
  };
};
