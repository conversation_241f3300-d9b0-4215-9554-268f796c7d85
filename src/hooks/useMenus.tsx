import { cloneDeep } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';

import { ReactComponent as AuditIcon } from '@/assets/images/management/sidebar/audit-icon.svg';
import { ReactComponent as MemberIcon } from '@/assets/images/management/sidebar/members-icon.svg';
import { ReactComponent as PackagesIcon } from '@/assets/images/management/sidebar/packages-icon.svg';
import { ReactComponent as PerformanceIcon } from '@/assets/images/management/sidebar/performance-icon.svg';
import { ReactComponent as SettingsIcon } from '@/assets/images/management/sidebar/settings-icon.svg';
import { ReactComponent as SeatsIcon } from '@/assets/images/management/sidebar/whitelist-icon.svg';
import type { BreadcrumbModel, MenuItem } from '@/model/Menus';
import { GetMenuTypeEnum, MenuKey } from '@/model/Menus';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMenuStore } from '@/store/ManagementSilder';
export default function useMenus() {
  const menus = useMenuStore((state) => state.menus);
  const setMenus = useMenuStore((state) => state.setMenus);
  // 菜单数据预缓存
  const menusMap = useMenuStore((state) => state.menusMap);
  const setMenusMap = useMenuStore((state) => state.setMenusMap);

  const setSelectedMenuItem = useMenuStore((state) => state.setSelectedMenuItem);
  const selectedMenuItem = useMenuStore((state) => state.selectedMenuItem);

  const menusStatic = [
    {
      key: MenuKey.efficiency,
      icon: <PerformanceIcon />,
      label: $t('Management.board'),
      title: $t('Management.board'),
    },
    {
      key: MenuKey.members,
      icon: <MemberIcon />,
      label: $t('Management.memberList'),
      title: $t('Management.memberList'),
    },
    {
      key: MenuKey.audit,
      icon: <AuditIcon />,
      label: $t('Management.auditLog'),
      title: $t('Management.auditLog'),
    },
    {
      key: MenuKey.packages,
      icon: <PackagesIcon />,
      label: $t('Management.kitValuePack'),
      title: $t('Management.kitValuePack'),
    },
    {
      key: MenuKey.whitelist,
      icon: <SeatsIcon />,
      label: $t('Management.onlineSeatWhiteList'),
      title: $t('Management.onlineSeatWhiteList'),
    },
    {
      key: MenuKey.settings,
      icon: <SettingsIcon />,
      label: $t('Management.settings'),
      title: $t('Management.settings'),
    },
  ];
  /**
   * 根据 key 查找菜单项
   * type 查所有层0级，查当前1
   */
  const findMenuItemByKey = useCallback(
    (
      key: string,
      type: GetMenuTypeEnum = GetMenuTypeEnum.GetMenuItem,
    ): Partial<MenuItem>[] | Partial<MenuItem> | undefined => {
      const allMenuValues = Array.from(menusMap.values());
      const currentData = allMenuValues.find((items) => items.some((item) => item.key === key));
      if (type) {
        return currentData ? currentData.find((item) => item.key === key) : undefined;
      } else {
        if (currentData && currentData.length) {
          const resultData = (currentData as Partial<MenuItem>[]).find((item) => item.key === key);
          return currentData.length > 1 ? [currentData[0], resultData as Partial<MenuItem>] : currentData;
        } else {
          return [];
        }
      }
    },
    [menusMap],
  );

  /**
   * 初始化菜单
   * @returns
   */
  function initMenus(): MenuItem[] {
    return menusStatic;
  }
  /**
   * 删除菜单项
   * @param key 菜单项的 key
   * @param menus 菜单项数组
   */
  const deleteFormMenus = useCallback(
    ({ key, menus }: { key: string; menus: MenuItem[] }) => {
      const index = menus.findIndex((item) => item?.key === key);
      if (index > -1) {
        menus.splice(index, 1);
      }
    },
    [menus],
  );

  // 菜单数据预处理
  const setMenusCache = useCallback(
    (menus: MenuItem[]) => {
      const newMenusMap = new Map();
      menus.forEach((item) => {
        if (item.children && item.children.length > 0) {
          const current = cloneDeep(item);
          delete current.children;
          delete current.icon;
          newMenusMap.set(item.key, [current, ...item.children]);
        } else {
          newMenusMap.set(item.key, [item]);
        }
      });
      setMenusMap(newMenusMap);
    },
    [menus],
  );

  const breadcrumbList = useMemo<BreadcrumbModel[]>(() => {
    if (selectedMenuItem) {
      const menusList = findMenuItemByKey(selectedMenuItem.key as string, GetMenuTypeEnum.GetAllMenuItem);
      if (menusList) {
        return (menusList as Required<MenuItem>[]).map((item) => ({
          title: item.title,
          path: item.key,
        }));
      } else {
        return [];
      }
    }
    return [];
  }, [selectedMenuItem]);

  useEffect(() => {
    const sliderMenus = initMenus();
    // 设置菜单权限的代码暂时停止
    setMenusCache(sliderMenus);
    setMenus(sliderMenus);
  }, []);
  return {
    initMenus,
    menus,
    menusMap,
    setMenus,
    deleteFormMenus,
    setSelectedMenuItem,
    selectedMenuItem,
    findMenuItemByKey,
    breadcrumbList,
  };
}
