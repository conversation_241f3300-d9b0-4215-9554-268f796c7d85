import { debounce } from 'lodash';
import { useCallback, useEffect, useState } from 'react';

export const useTableMaxHeight = ({ class_name = 'auto-height-table-container' }: { class_name?: string }) => {
  const [tableHeight, setTableHeight] = useState(0);

  // 计算高度核心逻辑
  const calculateHeight = useCallback(() => {
    try {
      // 获取页面顶部元素（根据实际情况调整选择器）
      const header = document.querySelector('.page-header') as HTMLDivElement;
      // 获取表格容器元素
      const container = document.querySelector(`.${class_name}`);

      // 获取底部元素（根据实际情况调整选择器）
      const footer = document.querySelector('.page-footer') as HTMLDivElement;

      if (!container) return;

      // 计算可用高度
      const { top } = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const margin = 16; // 安全边距

      const thead = 60; // 表头

      let occupiedHeight = margin;

      // 如果有头部元素
      if (header) {
        occupiedHeight += header.offsetHeight;
      }

      // 如果有底部元素
      if (footer) {
        occupiedHeight += footer.offsetHeight;
      }

      // 最终高度计算
      const finalHeight = windowHeight - top - occupiedHeight - thead;

      // 设置最小高度防止异常
      setTableHeight(Math.max(finalHeight, 200));
    } catch (error) {
      console.error('高度计算失败:', error);
    }
  }, []);

  // 带防抖的版本
  const debouncedCalculate = useCallback(
    debounce(() => {
      calculateHeight();
    }, 200),
    [calculateHeight],
  );

  useEffect(() => {
    // 初始化计算（延迟确保 DOM 就绪）
    const initTimer = setTimeout(calculateHeight, 50);

    // 监听窗口变化
    window.addEventListener('resize', debouncedCalculate);

    // 使用 ResizeObserver 监听容器变化
    let observer: ResizeObserver | null = null;
    const container = document.querySelector('.auto-height-table-container');
    if (container) {
      observer = new ResizeObserver(debouncedCalculate);
      observer.observe(container);
    }

    return () => {
      clearTimeout(initTimer);
      window.removeEventListener('resize', debouncedCalculate);
      if (observer) observer.disconnect();
    };
  }, [debouncedCalculate]);

  return {
    tableMaxHeight: tableHeight,
    tableClassName: class_name,
  };
};
