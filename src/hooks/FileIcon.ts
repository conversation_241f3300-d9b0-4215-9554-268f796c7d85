import { useMemo } from 'react';

// 导入需要的图标
import cloudSrc from '@/assets/images/fileIcon/<EMAIL>';
import excelSrc from '@/assets/images/fileIcon/<EMAIL>';
import folderSrc from '@/assets/images/fileIcon/<EMAIL>';
import formSrc from '@/assets/images/fileIcon/<EMAIL>';
import modocSrc from '@/assets/images/fileIcon/<EMAIL>';
import mosheetSrc from '@/assets/images/fileIcon/<EMAIL>';
import mp3Src from '@/assets/images/fileIcon/<EMAIL>';
import mp4Src from '@/assets/images/fileIcon/<EMAIL>';
import newdocSrc from '@/assets/images/fileIcon/<EMAIL>';
import pdfSrc from '@/assets/images/fileIcon/<EMAIL>';
import picSrc from '@/assets/images/fileIcon/<EMAIL>';
import pptSrc from '@/assets/images/fileIcon/<EMAIL>';
import presentationSrc from '@/assets/images/fileIcon/<EMAIL>';
import tableSrc from '@/assets/images/fileIcon/<EMAIL>';
import wordSrc from '@/assets/images/fileIcon/<EMAIL>';
import wpsSrc from '@/assets/images/fileIcon/<EMAIL>';
import zipSrc from '@/assets/images/fileIcon/<EMAIL>';

type FileIconMapType = {
  [key: string]: string;
};

/**
 * 根据文件类型获取对应的图标
 */
export function useFileIcon() {
  const fileIconMap = useMemo<FileIconMapType>(
    () => ({
      // 文档
      newdoc: newdocSrc,
      modoc: modocSrc,
      docx: wordSrc,
      doc: wordSrc,
      word: wordSrc,
      pdf: pdfSrc,
      wps: wpsSrc,

      // 表格，表单
      form: formSrc,
      mosheet: mosheetSrc,
      table: tableSrc,
      excel: excelSrc,
      xlsx: excelSrc,
      xls: excelSrc,
      csv: excelSrc,

      // 幻灯片
      presentation: presentationSrc,
      ppt: presentationSrc,
      pptx: pptSrc,

      // 音视频
      mp3: mp3Src,
      mp4: mp4Src,
      mov: mp4Src,
      qt: mp4Src,

      // 图片
      img: picSrc,
      jpg: picSrc,
      jpeg: picSrc,
      png: picSrc,
      gif: picSrc,
      tiff: picSrc,

      // 压缩文件
      zip: zipSrc,
      rar: zipSrc,

      // 文件夹
      folder: folderSrc,

      // 默认
      default: cloudSrc,
    }),
    [],
  );

  /**
   * 获取文件图标
   * @param type 文件类型
   * @returns 对应类型的图标源
   */
  const getFileIcon = (type: string) => {
    if (!type) return fileIconMap.default;

    // 返回对应类型的图标或默认图标
    return fileIconMap[type.toLowerCase()] || fileIconMap.default;
  };

  return { getFileIcon, fileIconMap };
}
