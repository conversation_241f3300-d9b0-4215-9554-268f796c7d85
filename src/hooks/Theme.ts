import { useCallback, useEffect } from 'react';

import { getStoredTheme, setStoredTheme } from '@/modules/Theme';
import { useThemeStore } from '@/store/Theme';

const darkModeMediaMatch = matchMedia('(prefers-color-scheme: dark)');

/**
 * 设置主题
 * @param value - 主题
 */
export const useRecordTheme = () => {
  const setTheme = useThemeStore((state) => state.setTheme);

  const recordTheme = useCallback(
    (value: string) => {
      if (value === 'System') {
        const isDark = !!darkModeMediaMatch.matches;
        setTheme({ isDark });
      } else {
        setTheme({ isDark: value === 'Dark' });
      }
      setStoredTheme(value);
    },
    [setTheme],
  );

  return { recordTheme };
};

/**
 * 初始化页面的主题，需要获取 localStorage 中的主题设置，系统的外观模式设置,
 */
export const useInitTheme = () => {
  const setTheme = useThemeStore((state) => state.setTheme);
  const commonThemeFun = useCallback(
    (darkModeMedia: MediaQueryList) => {
      const newStoredTheme = getStoredTheme();
      if (!newStoredTheme || newStoredTheme === 'System') {
        setTheme({ isDark: darkModeMedia.matches });
      } else {
        setTheme({ isDark: newStoredTheme === 'Dark' });
      }
    },
    [setTheme],
  );

  useEffect(() => {
    commonThemeFun(darkModeMediaMatch);
    const handleDarkModeChange = () => {
      commonThemeFun(darkModeMediaMatch);
    };
    darkModeMediaMatch.addEventListener('change', handleDarkModeChange);
    return () => {
      darkModeMediaMatch.removeEventListener('change', handleDarkModeChange);
    };
  }, [commonThemeFun]);
};
