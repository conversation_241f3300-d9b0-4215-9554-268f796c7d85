module.exports = {
  // Umi Max 项目
  extends: [require.resolve('@umijs/max/eslint')],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: './tsconfig.json',
    tsconfigRootDir: './',
  },
  plugins: ['simple-import-sort'],
  settings: {
    'import/resolver': {
      typescript: {
        project: './tsconfig.json',
      },
    },
  },
  ignorePatterns: ['/externals'],
  rules: {
    // ==========================================
    // 字符串和模板字面量规则
    // ==========================================
    'prefer-template': 'error', // 推荐使用模板字符串而不是字符串拼接
    'no-useless-concat': 'error', // 禁止不必要的字符串字面量或模板字面量的连接

    // ==========================================
    // 变量声明和赋值规则
    // ==========================================
    'prefer-const': 'warn', // 优先使用 const 声明不重新分配的变量
    'no-var': 'error', // 禁止使用 var 声明变量
    'no-param-reassign': 'error', // 禁止修改函数参数

    // ==========================================
    // 函数和回调规则
    // ==========================================
    'prefer-arrow-callback': ['error', { allowNamedFunctions: true, allowUnboundThis: true }], // 推荐使用箭头函数作为回调
    'new-cap': ['error', { capIsNew: false, newIsCap: true, properties: true }], // 构造函数必须以大写字母开头

    // ==========================================
    // 控制台和调试规则
    // ==========================================
    'no-console': ['warn', { allow: ['info', 'warn', 'error', 'debug'] }], // 限制 console 使用，只允许特定方法
    'no-restricted-syntax': [
      'error',
      'DebuggerStatement', // 禁止 debugger 语句
      'LabeledStatement', // 禁止标签语句
      'WithStatement', // 禁止 with 语句
      'TSEnumDeclaration[const=true]', // 禁止 const enum
      'TSExportAssignment', // 禁止 TypeScript export = 语法
    ],

    // ==========================================
    // Import/Export 规则
    // ==========================================
    'simple-import-sort/imports': 'error', // 自动排序 import 语句
    'simple-import-sort/exports': 'error', // 自动排序 export 语句

    // ==========================================
    // TypeScript 相关规则
    // ==========================================
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }], // 禁止未使用的变量，但允许以下划线开头的参数
    '@typescript-eslint/consistent-type-imports': ['error', { prefer: 'type-imports' }], // 强制使用 type-only 导入
    '@typescript-eslint/no-explicit-any': 'warn', // 避免使用 any 类型
    '@typescript-eslint/prefer-optional-chain': 'warn', // 推荐使用可选链操作符
    '@typescript-eslint/ban-ts-comment': ['warn', { 'ts-expect-error': 'allow-with-description' }], // 限制 TypeScript 注释的使用

    // ==========================================
    // React Hooks 规则
    // ==========================================
    'react-hooks/rules-of-hooks': 'error', // 确保 Hooks 只在函数组件和自定义 Hooks 的顶层调用
    'react-hooks/exhaustive-deps': 'warn', // 确保 useEffect 等 Hook 的依赖数组包含所有依赖项

    // ==========================================
    // React 组件和 JSX 规则
    // ==========================================
    // JSX 语法规则
    'react/jsx-no-duplicate-props': 'error', // 禁止重复的属性
    'react/jsx-pascal-case': 'error', // 组件名称必须使用 PascalCase
    'react/jsx-sort-props': [
      'error',
      {
        callbacksLast: true, // 回调函数放在最后
        shorthandFirst: true, // 简写属性放在前面
        ignoreCase: true, // 忽略大小写
        reservedFirst: true, // 保留属性（如 key）放在最前面
      },
    ],
    'react/jsx-no-constructed-context-values': 'error', // 避免在 JSX 中创建新的对象作为 Context 值
    'react/jsx-no-useless-fragment': 'warn', // 避免不必要的 React Fragment
    'react/self-closing-comp': 'error', // 没有子元素的组件应该自闭合

    // React 组件规则
    'react/no-array-index-key': 'warn', // 避免使用数组索引作为 key
    'react/no-danger': 'error', // 禁止使用 dangerouslySetInnerHTML
    'react/no-deprecated': 'error', // 禁止使用已废弃的 React API
    'react/no-direct-mutation-state': 'error', // 禁止直接修改 state
    'react/no-find-dom-node': 'error', // 禁止使用 findDOMNode
    'react/no-is-mounted': 'error', // 禁止使用 isMounted
    'react/no-render-return-value': 'error', // 禁止使用 render 方法的返回值
    'react/no-string-refs': 'error', // 禁止使用字符串 refs
    'react/no-unknown-property': 'error', // 禁止使用未知的 DOM 属性
    'react/no-unused-prop-types': 'warn', // 检测未使用的 prop 类型
    'react/require-render-return': 'error', // 要求 render 方法有返回值
    'react/void-dom-elements-no-children': 'error', // 禁止给 void 元素添加子元素

    // ==========================================
    // 代码格式化和风格规则
    // ==========================================
    // 空格和换行
    'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }], // 限制连续空行数量
    'no-trailing-spaces': 'error', // 禁止行尾空格
    'eol-last': 'error', // 文件末尾需要空行
    'object-curly-spacing': ['error', 'always'], // 对象字面量的大括号内要求空格
    'array-bracket-spacing': ['error', 'never'], // 数组括号内不允许空格

    // 标点符号
    'comma-dangle': ['error', 'always-multiline'], // 多行时要求尾随逗号
    semi: ['error', 'always'], // 要求语句末尾使用分号

    // ==========================================
    // 代码质量和最佳实践规则
    // ==========================================
    // 条件和逻辑
    'no-unneeded-ternary': 'error', // 禁止不必要的三元表达式
    'no-constant-condition': 'error', // 禁止在条件语句中使用常量
    'no-duplicate-case': 'error', // 禁止 switch 语句中的重复 case

    // 语句和表达式
    'no-return-assign': 'error', // 禁止在返回语句中赋值
    'no-sequences': 'error', // 禁止使用逗号操作符
    'no-unused-expressions': 'warn', // 禁止未使用的表达式
    'no-void': 'error', // 禁止使用 void 操作符

    // 函数和对象
    'no-useless-constructor': 'error', // 禁止不必要的构造函数
    'no-useless-rename': 'error', // 禁止不必要的重命名
    'operator-assignment': ['error', 'always'], // 要求使用操作符简写

    // ES6+ 特性
    'prefer-spread': 'error', // 推荐使用扩展运算符
    'prefer-rest-params': 'error', // 推荐使用剩余参数
    'prefer-object-spread': 'error', // 推荐使用对象扩展运算符

    // 不必要的代码
    'no-useless-escape': 'error', // 禁止不必要的转义
    'no-useless-return': 'error', // 禁止不必要的 return
    'no-useless-catch': 'error', // 禁止不必要的 catch 子句
    'no-useless-computed-key': 'error', // 禁止不必要的计算属性
    'no-useless-call': 'error', // 禁止不必要的 .call() 和 .apply()

    // 错误处理
    'no-empty': ['error', { allowEmptyCatch: true }], // 禁止空块语句，但允许空的 catch 子句
    'no-extra-boolean-cast': 'error', // 禁止不必要的布尔类型转换
    'no-extra-semi': 'error', // 禁止多余的分号

    // 异步代码
    'no-async-promise-executor': 'error', // 禁止在 Promise 构造函数中使用 async

    // ==========================================
    // 注释掉的规则（可选启用）
    // ==========================================
    // 'no-await-in-loop': 'error', // 禁止在循环中使用 await
    // 'require-await': 'error', // 要求 async 函数中有 await
  },
};
