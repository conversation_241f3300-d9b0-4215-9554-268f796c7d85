var __extends,
  __assign,
  __rest,
  __decorate,
  __param,
  __metadata,
  __awaiter,
  __generator,
  __exportStar,
  __values,
  __read,
  __spread,
  __spreadArrays,
  __spreadArray,
  __await,
  __asyncGenerator,
  __asyncDelegator,
  __asyncValues,
  __makeTemplateObject,
  __importStar,
  __importDefault,
  __classPrivateFieldGet,
  __classPrivateFieldSet,
  __createBinding;
!(function (t) {
  var a = 'object' == typeof global ? global : 'object' == typeof self ? self : 'object' == typeof this ? this : {};
  function r(r, n) {
    return (
      r !== a &&
        ('function' == typeof Object.create
          ? Object.defineProperty(r, '__esModule', { value: !0 })
          : (r.__esModule = !0)),
      function (e, t) {
        return (r[e] = n ? n(e, t) : t);
      }
    );
  }
  'function' == typeof define && define.amd
    ? define('tslib', ['exports'], function (e) {
        t(r(a, r(e)));
      })
    : 'object' == typeof module && 'object' == typeof module.exports
    ? t(r(a, r(module.exports)))
    : t(r(a));
})(function (e) {
  var n =
    Object.setPrototypeOf ||
    ({ __proto__: [] } instanceof Array &&
      function (e, t) {
        e.__proto__ = t;
      }) ||
    function (e, t) {
      for (var r in t) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
    };
  (__extends = function (e, t) {
    if ('function' != typeof t && null !== t)
      throw new TypeError('Class extends value ' + String(t) + ' is not a constructor or null');
    function r() {
      this.constructor = e;
    }
    n(e, t), (e.prototype = null === t ? Object.create(t) : ((r.prototype = t.prototype), new r()));
  }),
    (__assign =
      Object.assign ||
      function (e) {
        for (var t, r = 1, n = arguments.length; r < n; r++)
          for (var a in (t = arguments[r])) Object.prototype.hasOwnProperty.call(t, a) && (e[a] = t[a]);
        return e;
      }),
    (__rest = function (e, t) {
      var r = {};
      for (a in e) Object.prototype.hasOwnProperty.call(e, a) && t.indexOf(a) < 0 && (r[a] = e[a]);
      if (null != e && 'function' == typeof Object.getOwnPropertySymbols)
        for (var n = 0, a = Object.getOwnPropertySymbols(e); n < a.length; n++)
          t.indexOf(a[n]) < 0 && Object.prototype.propertyIsEnumerable.call(e, a[n]) && (r[a[n]] = e[a[n]]);
      return r;
    }),
    (__decorate = function (e, t, r, n) {
      var a,
        o = arguments.length,
        i = o < 3 ? t : null === n ? (n = Object.getOwnPropertyDescriptor(t, r)) : n;
      if ('object' == typeof Reflect && 'function' == typeof Reflect.decorate) i = Reflect.decorate(e, t, r, n);
      else
        for (var _ = e.length - 1; 0 <= _; _--) (a = e[_]) && (i = (o < 3 ? a(i) : 3 < o ? a(t, r, i) : a(t, r)) || i);
      return 3 < o && i && Object.defineProperty(t, r, i), i;
    }),
    (__param = function (r, n) {
      return function (e, t) {
        n(e, t, r);
      };
    }),
    (__metadata = function (e, t) {
      if ('object' == typeof Reflect && 'function' == typeof Reflect.metadata) return Reflect.metadata(e, t);
    }),
    (__awaiter = function (e, i, _, c) {
      return new (_ = _ || Promise)(function (r, t) {
        function n(e) {
          try {
            o(c.next(e));
          } catch (e) {
            t(e);
          }
        }
        function a(e) {
          try {
            o(c.throw(e));
          } catch (e) {
            t(e);
          }
        }
        function o(e) {
          var t;
          e.done
            ? r(e.value)
            : ((t = e.value) instanceof _
                ? t
                : new _(function (e) {
                    e(t);
                  })
              ).then(n, a);
        }
        o((c = c.apply(e, i || [])).next());
      });
    }),
    (__generator = function (r, n) {
      var a,
        o,
        i,
        _ = {
          label: 0,
          sent: function () {
            if (1 & i[0]) throw i[1];
            return i[1];
          },
          trys: [],
          ops: [],
        },
        e = { next: t(0), throw: t(1), return: t(2) };
      return (
        'function' == typeof Symbol &&
          (e[Symbol.iterator] = function () {
            return this;
          }),
        e
      );
      function t(t) {
        return function (e) {
          return (function (t) {
            if (a) throw new TypeError('Generator is already executing.');
            for (; _; )
              try {
                if (
                  ((a = 1),
                  o &&
                    (i = 2 & t[0] ? o.return : t[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) &&
                    !(i = i.call(o, t[1])).done)
                )
                  return i;
                switch (((o = 0), i && (t = [2 & t[0], i.value]), t[0])) {
                  case 0:
                  case 1:
                    i = t;
                    break;
                  case 4:
                    return _.label++, { value: t[1], done: !1 };
                  case 5:
                    _.label++, (o = t[1]), (t = [0]);
                    continue;
                  case 7:
                    (t = _.ops.pop()), _.trys.pop();
                    continue;
                  default:
                    if (!(i = 0 < (i = _.trys).length && i[i.length - 1]) && (6 === t[0] || 2 === t[0])) {
                      _ = 0;
                      continue;
                    }
                    if (3 === t[0] && (!i || (t[1] > i[0] && t[1] < i[3]))) {
                      _.label = t[1];
                      break;
                    }
                    if (6 === t[0] && _.label < i[1]) {
                      (_.label = i[1]), (i = t);
                      break;
                    }
                    if (i && _.label < i[2]) {
                      (_.label = i[2]), _.ops.push(t);
                      break;
                    }
                    i[2] && _.ops.pop(), _.trys.pop();
                    continue;
                }
                t = n.call(r, _);
              } catch (e) {
                (t = [6, e]), (o = 0);
              } finally {
                a = i = 0;
              }
            if (5 & t[0]) throw t[1];
            return { value: t[0] ? t[1] : void 0, done: !0 };
          })([t, e]);
        };
      }
    }),
    (__exportStar = function (e, t) {
      for (var r in e) 'default' === r || Object.prototype.hasOwnProperty.call(t, r) || __createBinding(t, e, r);
    }),
    (__createBinding = Object.create
      ? function (e, t, r, n) {
          void 0 === n && (n = r),
            Object.defineProperty(e, n, {
              enumerable: !0,
              get: function () {
                return t[r];
              },
            });
        }
      : function (e, t, r, n) {
          void 0 === n && (n = r), (e[n] = t[r]);
        }),
    (__values = function (e) {
      var t = 'function' == typeof Symbol && Symbol.iterator,
        r = t && e[t],
        n = 0;
      if (r) return r.call(e);
      if (e && 'number' == typeof e.length)
        return {
          next: function () {
            return e && n >= e.length && (e = void 0), { value: e && e[n++], done: !e };
          },
        };
      throw new TypeError(t ? 'Object is not iterable.' : 'Symbol.iterator is not defined.');
    }),
    (__read = function (e, t) {
      var r = 'function' == typeof Symbol && e[Symbol.iterator];
      if (!r) return e;
      var n,
        a,
        o = r.call(e),
        i = [];
      try {
        for (; (void 0 === t || 0 < t--) && !(n = o.next()).done; ) i.push(n.value);
      } catch (e) {
        a = { error: e };
      } finally {
        try {
          n && !n.done && (r = o.return) && r.call(o);
        } finally {
          if (a) throw a.error;
        }
      }
      return i;
    }),
    (__spread = function () {
      for (var e = [], t = 0; t < arguments.length; t++) e = e.concat(__read(arguments[t]));
      return e;
    }),
    (__spreadArrays = function () {
      for (var e = 0, t = 0, r = arguments.length; t < r; t++) e += arguments[t].length;
      for (var n = Array(e), a = 0, t = 0; t < r; t++)
        for (var o = arguments[t], i = 0, _ = o.length; i < _; i++, a++) n[a] = o[i];
      return n;
    }),
    (__spreadArray = function (e, t) {
      for (var r = 0, n = t.length, a = e.length; r < n; r++, a++) e[a] = t[r];
      return e;
    }),
    (__await = function (e) {
      return this instanceof __await ? ((this.v = e), this) : new __await(e);
    }),
    (__asyncGenerator = function (e, t, r) {
      if (!Symbol.asyncIterator) throw new TypeError('Symbol.asyncIterator is not defined.');
      var a = r.apply(e, t || []),
        o = [],
        i = {};
      return (
        n('next'),
        n('throw'),
        n('return'),
        (i[Symbol.asyncIterator] = function () {
          return this;
        }),
        i
      );
      function n(n) {
        a[n] &&
          (i[n] = function (r) {
            return new Promise(function (e, t) {
              1 < o.push([n, r, e, t]) || _(n, r);
            });
          });
      }
      function _(e, t) {
        try {
          (r = a[e](t)).value instanceof __await ? Promise.resolve(r.value.v).then(c, l) : u(o[0][2], r);
        } catch (e) {
          u(o[0][3], e);
        }
        var r;
      }
      function c(e) {
        _('next', e);
      }
      function l(e) {
        _('throw', e);
      }
      function u(e, t) {
        e(t), o.shift(), o.length && _(o[0][0], o[0][1]);
      }
    }),
    (__asyncDelegator = function (n) {
      var a,
        e = {};
      return (
        t('next'),
        t('throw', function (e) {
          throw e;
        }),
        t('return'),
        (e[Symbol.iterator] = function () {
          return this;
        }),
        e
      );
      function t(t, r) {
        e[t] = n[t]
          ? function (e) {
              return (a = !a) ? { value: __await(n[t](e)), done: 'return' === t } : r ? r(e) : e;
            }
          : r;
      }
    }),
    (__asyncValues = function (i) {
      if (!Symbol.asyncIterator) throw new TypeError('Symbol.asyncIterator is not defined.');
      var e,
        t = i[Symbol.asyncIterator];
      return t
        ? t.call(i)
        : ((i = 'function' == typeof __values ? __values(i) : i[Symbol.iterator]()),
          (e = {}),
          r('next'),
          r('throw'),
          r('return'),
          (e[Symbol.asyncIterator] = function () {
            return this;
          }),
          e);
      function r(o) {
        e[o] =
          i[o] &&
          function (a) {
            return new Promise(function (e, t) {
              var r, n;
              (a = i[o](a)),
                (r = e),
                (e = t),
                (n = a.done),
                (t = a.value),
                Promise.resolve(t).then(function (e) {
                  r({ value: e, done: n });
                }, e);
            });
          };
      }
    }),
    (__makeTemplateObject = function (e, t) {
      return Object.defineProperty ? Object.defineProperty(e, 'raw', { value: t }) : (e.raw = t), e;
    });
  var a = Object.create
    ? function (e, t) {
        Object.defineProperty(e, 'default', { enumerable: !0, value: t });
      }
    : function (e, t) {
        e.default = t;
      };
  (__importStar = function (e) {
    if (e && e.__esModule) return e;
    var t = {};
    if (null != e)
      for (var r in e) 'default' !== r && Object.prototype.hasOwnProperty.call(e, r) && __createBinding(t, e, r);
    return a(t, e), t;
  }),
    (__importDefault = function (e) {
      return e && e.__esModule ? e : { default: e };
    }),
    (__classPrivateFieldGet = function (e, t, r, n) {
      if ('a' === r && !n) throw new TypeError('Private accessor was defined without a getter');
      if ('function' == typeof t ? e !== t || !n : !t.has(e))
        throw new TypeError('Cannot read private member from an object whose class did not declare it');
      return 'm' === r ? n : 'a' === r ? n.call(e) : n ? n.value : t.get(e);
    }),
    (__classPrivateFieldSet = function (e, t, r, n, a) {
      if ('m' === n) throw new TypeError('Private method is not writable');
      if ('a' === n && !a) throw new TypeError('Private accessor was defined without a setter');
      if ('function' == typeof t ? e !== t || !a : !t.has(e))
        throw new TypeError('Cannot write private member to an object whose class did not declare it');
      return 'a' === n ? a.call(e, r) : a ? (a.value = r) : t.set(e, r), r;
    }),
    e('__extends', __extends),
    e('__assign', __assign),
    e('__rest', __rest),
    e('__decorate', __decorate),
    e('__param', __param),
    e('__metadata', __metadata),
    e('__awaiter', __awaiter),
    e('__generator', __generator),
    e('__exportStar', __exportStar),
    e('__createBinding', __createBinding),
    e('__values', __values),
    e('__read', __read),
    e('__spread', __spread),
    e('__spreadArrays', __spreadArrays),
    e('__spreadArray', __spreadArray),
    e('__await', __await),
    e('__asyncGenerator', __asyncGenerator),
    e('__asyncDelegator', __asyncDelegator),
    e('__asyncValues', __asyncValues),
    e('__makeTemplateObject', __makeTemplateObject),
    e('__importStar', __importStar),
    e('__importDefault', __importDefault),
    e('__classPrivateFieldGet', __classPrivateFieldGet),
    e('__classPrivateFieldSet', __classPrivateFieldSet);
});
