/**
 * 处理外部依赖挂载方式的逻辑，主要是为了兼容 lizard-vendor 里面的写法
 * ⚠️ 注意：这个文件的内容修改后，需要用 git mv 修改文件名中的版本号，同时还要修改 externals-plugin.ts 和 .umirc.ts 中的对应引用
 */
(() => {
  window.$01 = window.React;
  window.$02 = window.ReactDOM;
  window.$05 = window.Redux;
  window.$06 = window.ReactRedux;
  window.$07 = window.styled;
  window.$16 = window.dayjs;
  // mock @shimo/lo-sensors
  window.$0d = {
    ShimoSensor: function () {
      return {
        login: function () {},
        trackNew: function () {},
        autoTrack: function () {},
      };
    },
  };
})();
