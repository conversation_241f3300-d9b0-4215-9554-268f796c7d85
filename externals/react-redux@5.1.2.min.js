!(function (e, t) {
  'object' == typeof exports && 'undefined' != typeof module
    ? t(exports, require('react'), require('redux'))
    : 'function' == typeof define && define.amd
    ? define(['exports', 'react', 'redux'], t)
    : t((e.ReactRedux = {}), e.React, e.Redux);
})(this, function (e, g, r) {
  'use strict';
  var t = 'default' in g ? g.default : g;
  function O(e, t) {
    (e.prototype = Object.create(t.prototype)), ((e.prototype.constructor = e).__proto__ = t);
  }
  function n(e) {
    return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, 'default') ? e.default : e;
  }
  function o(e, t) {
    return e((t = { exports: {} }), t.exports), t.exports;
  }
  function i(e) {
    return function () {
      return e;
    };
  }
  var s = function () {};
  (s.thatReturns = i),
    (s.thatReturnsFalse = i(!1)),
    (s.thatReturnsTrue = i(!0)),
    (s.thatReturnsNull = i(null)),
    (s.thatReturnsThis = function () {
      return this;
    }),
    (s.thatReturnsArgument = function (e) {
      return e;
    });
  var u = s;
  var c = function (e, t, r, n, o, i, s, u) {
      if (!e) {
        var c;
        if (void 0 === t)
          c = Error(
            'Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.',
          );
        else {
          var a = [r, n, o, i, s, u],
            p = 0;
          (c = Error(
            t.replace(/%s/g, function () {
              return a[p++];
            }),
          )).name = 'Invariant Violation';
        }
        throw ((c.framesToPop = 1), c);
      }
    },
    a = o(function (e) {
      e.exports = (function () {
        function e(e, t, r, n, o, i) {
          'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED' !== i &&
            c(
              !1,
              'Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types',
            );
        }
        function t() {
          return e;
        }
        var r = {
          array: (e.isRequired = e),
          bool: e,
          func: e,
          number: e,
          object: e,
          string: e,
          symbol: e,
          any: e,
          arrayOf: t,
          element: e,
          instanceOf: t,
          node: e,
          objectOf: t,
          oneOf: t,
          oneOfType: t,
          shape: t,
          exact: t,
        };
        return (r.checkPropTypes = u), (r.PropTypes = r);
      })();
    }),
    C = a.shape({
      trySubscribe: a.func.isRequired,
      tryUnsubscribe: a.func.isRequired,
      notifyNestedSubs: a.func.isRequired,
      isSubscribed: a.func.isRequired,
    }),
    w = a.shape({ subscribe: a.func.isRequired, dispatch: a.func.isRequired, getState: a.func.isRequired });
  function p(o) {
    var e;
    void 0 === o && (o = 'store');
    var r = o + 'Subscription',
      t = (function (n) {
        O(t, n);
        var e = t.prototype;
        function t(e, t) {
          var r;
          return ((r = n.call(this, e, t) || this)[o] = e.store), r;
        }
        return (
          (e.getChildContext = function () {
            var e;
            return ((e = {})[o] = this[o]), (e[r] = null), e;
          }),
          (e.render = function () {
            return g.Children.only(this.props.children);
          }),
          t
        );
      })(g.Component);
    return (
      (t.propTypes = { store: w.isRequired, children: a.element.isRequired }),
      (t.childContextTypes = (((e = {})[o] = w.isRequired), (e[r] = C), e)),
      t
    );
  }
  var f = p();
  function $(e) {
    if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return e;
  }
  function x() {
    return (x =
      Object.assign ||
      function (e) {
        for (var t = 1; t < arguments.length; t++) {
          var r = arguments[t];
          for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (e[n] = r[n]);
        }
        return e;
      }).apply(this, arguments);
  }
  function T(e, t) {
    if (null == e) return {};
    var r,
      n,
      o = {},
      i = Object.keys(e);
    for (n = 0; n < i.length; n++) t.indexOf((r = i[n])) < 0 && (o[r] = e[r]);
    return o;
  }
  var d = o(function (e, t) {
    Object.defineProperty(t, '__esModule', { value: !0 });
    var r = 'function' == typeof Symbol && Symbol.for,
      n = r ? Symbol.for('react.element') : 60103,
      o = r ? Symbol.for('react.portal') : 60106,
      i = r ? Symbol.for('react.fragment') : 60107,
      s = r ? Symbol.for('react.strict_mode') : 60108,
      u = r ? Symbol.for('react.profiler') : 60114,
      c = r ? Symbol.for('react.provider') : 60109,
      a = r ? Symbol.for('react.context') : 60110,
      p = r ? Symbol.for('react.async_mode') : 60111,
      f = r ? Symbol.for('react.concurrent_mode') : 60111,
      d = r ? Symbol.for('react.forward_ref') : 60112,
      l = r ? Symbol.for('react.suspense') : 60113,
      y = r ? Symbol.for('react.suspense_list') : 60120,
      h = r ? Symbol.for('react.memo') : 60115,
      m = r ? Symbol.for('react.lazy') : 60116,
      b = r ? Symbol.for('react.fundamental') : 60117,
      v = r ? Symbol.for('react.responder') : 60118,
      S = r ? Symbol.for('react.scope') : 60119;
    function P(e) {
      if ('object' == typeof e && null !== e) {
        var t = e.$$typeof;
        switch (t) {
          case n:
            switch ((e = e.type)) {
              case p:
              case f:
              case i:
              case u:
              case s:
              case l:
                return e;
              default:
                switch ((e = e && e.$$typeof)) {
                  case a:
                  case d:
                  case c:
                    return e;
                  default:
                    return t;
                }
            }
          case m:
          case h:
          case o:
            return t;
        }
      }
    }
    function g(e) {
      return P(e) === f;
    }
    (t.typeOf = P),
      (t.AsyncMode = p),
      (t.ConcurrentMode = f),
      (t.ContextConsumer = a),
      (t.ContextProvider = c),
      (t.Element = n),
      (t.ForwardRef = d),
      (t.Fragment = i),
      (t.Lazy = m),
      (t.Memo = h),
      (t.Portal = o),
      (t.Profiler = u),
      (t.StrictMode = s),
      (t.Suspense = l),
      (t.isValidElementType = function (e) {
        return (
          'string' == typeof e ||
          'function' == typeof e ||
          e === i ||
          e === f ||
          e === u ||
          e === s ||
          e === l ||
          e === y ||
          ('object' == typeof e &&
            null !== e &&
            (e.$$typeof === m ||
              e.$$typeof === h ||
              e.$$typeof === c ||
              e.$$typeof === a ||
              e.$$typeof === d ||
              e.$$typeof === b ||
              e.$$typeof === v ||
              e.$$typeof === S))
        );
      }),
      (t.isAsyncMode = function (e) {
        return g(e) || P(e) === p;
      }),
      (t.isConcurrentMode = g),
      (t.isContextConsumer = function (e) {
        return P(e) === a;
      }),
      (t.isContextProvider = function (e) {
        return P(e) === c;
      }),
      (t.isElement = function (e) {
        return 'object' == typeof e && null !== e && e.$$typeof === n;
      }),
      (t.isForwardRef = function (e) {
        return P(e) === d;
      }),
      (t.isFragment = function (e) {
        return P(e) === i;
      }),
      (t.isLazy = function (e) {
        return P(e) === m;
      }),
      (t.isMemo = function (e) {
        return P(e) === h;
      }),
      (t.isPortal = function (e) {
        return P(e) === o;
      }),
      (t.isProfiler = function (e) {
        return P(e) === u;
      }),
      (t.isStrictMode = function (e) {
        return P(e) === s;
      }),
      (t.isSuspense = function (e) {
        return P(e) === l;
      });
  });
  n(d);
  var l = o(function (e) {
      e.exports = d;
    }),
    y = {
      childContextTypes: !0,
      contextType: !0,
      contextTypes: !0,
      defaultProps: !0,
      displayName: !0,
      getDefaultProps: !0,
      getDerivedStateFromError: !0,
      getDerivedStateFromProps: !0,
      mixins: !0,
      propTypes: !0,
      type: !0,
    },
    h = { name: !0, length: !0, prototype: !0, caller: !0, callee: !0, arguments: !0, arity: !0 },
    m = { $$typeof: !0, compare: !0, defaultProps: !0, displayName: !0, propTypes: !0, type: !0 },
    b = {};
  function v(e) {
    return l.isMemo(e) ? m : b[e.$$typeof] || y;
  }
  b[l.ForwardRef] = { $$typeof: !0, render: !0, defaultProps: !0, displayName: !0, propTypes: !0 };
  var S = Object.defineProperty,
    P = Object.getOwnPropertyNames,
    M = Object.getOwnPropertySymbols,
    R = Object.getOwnPropertyDescriptor,
    E = Object.getPrototypeOf,
    N = Object.prototype;
  var _ = function e(t, r, n) {
      if ('string' == typeof r) return t;
      if (N) {
        var o = E(r);
        o && o !== N && e(t, o, n);
      }
      var i = P(r);
      M && (i = i.concat(M(r)));
      for (var s = v(t), u = v(r), c = 0; c < i.length; ++c) {
        var a = i[c];
        if (!(h[a] || (n && n[a]) || (u && u[a]) || (s && s[a]))) {
          var p = R(r, a);
          try {
            S(t, a, p);
          } catch (e) {}
        }
      }
      return t;
    },
    j = function (e, t, r, n, o, i, s, u) {
      if (!e) {
        var c;
        if (void 0 === t)
          c = Error(
            'Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.',
          );
        else {
          var a = [r, n, o, i, s, u],
            p = 0;
          (c = Error(
            t.replace(/%s/g, function () {
              return a[p++];
            }),
          )).name = 'Invariant Violation';
        }
        throw ((c.framesToPop = 1), c);
      }
    },
    q = o(function (e, t) {
      Object.defineProperty(t, '__esModule', { value: !0 });
      var r = 'function' == typeof Symbol && Symbol.for,
        n = r ? Symbol.for('react.element') : 60103,
        o = r ? Symbol.for('react.portal') : 60106,
        i = r ? Symbol.for('react.fragment') : 60107,
        s = r ? Symbol.for('react.strict_mode') : 60108,
        u = r ? Symbol.for('react.profiler') : 60114,
        c = r ? Symbol.for('react.provider') : 60109,
        a = r ? Symbol.for('react.context') : 60110,
        p = r ? Symbol.for('react.concurrent_mode') : 60111,
        f = r ? Symbol.for('react.forward_ref') : 60112,
        d = r ? Symbol.for('react.suspense') : 60113,
        l = r ? Symbol.for('react.memo') : 60115,
        y = r ? Symbol.for('react.lazy') : 60116;
      function h(e) {
        if ('object' == typeof e && null !== e) {
          var t = e.$$typeof;
          switch (t) {
            case n:
              switch ((e = e.type)) {
                case p:
                case i:
                case u:
                case s:
                  return e;
                default:
                  switch ((e = e && e.$$typeof)) {
                    case a:
                    case f:
                    case c:
                      return e;
                    default:
                      return t;
                  }
              }
            case o:
              return t;
          }
        }
      }
      function m(e) {
        return h(e) === p;
      }
      (t.typeOf = h),
        (t.AsyncMode = p),
        (t.ConcurrentMode = p),
        (t.ContextConsumer = a),
        (t.ContextProvider = c),
        (t.Element = n),
        (t.ForwardRef = f),
        (t.Fragment = i),
        (t.Profiler = u),
        (t.Portal = o),
        (t.StrictMode = s),
        (t.isValidElementType = function (e) {
          return (
            'string' == typeof e ||
            'function' == typeof e ||
            e === i ||
            e === p ||
            e === u ||
            e === s ||
            e === d ||
            ('object' == typeof e &&
              null !== e &&
              (e.$$typeof === y || e.$$typeof === l || e.$$typeof === c || e.$$typeof === a || e.$$typeof === f))
          );
        }),
        (t.isAsyncMode = function (e) {
          return m(e);
        }),
        (t.isConcurrentMode = m),
        (t.isContextConsumer = function (e) {
          return h(e) === a;
        }),
        (t.isContextProvider = function (e) {
          return h(e) === c;
        }),
        (t.isElement = function (e) {
          return 'object' == typeof e && null !== e && e.$$typeof === n;
        }),
        (t.isForwardRef = function (e) {
          return h(e) === f;
        }),
        (t.isFragment = function (e) {
          return h(e) === i;
        }),
        (t.isProfiler = function (e) {
          return h(e) === u;
        }),
        (t.isPortal = function (e) {
          return h(e) === o;
        }),
        (t.isStrictMode = function (e) {
          return h(e) === s;
        });
    });
  n(q);
  var D = o(function (e) {
      e.exports = q;
    }).isValidElementType,
    U = { notify: function () {} };
  var F = (function () {
      function e(e, t, r) {
        (this.store = e),
          (this.parentSub = t),
          (this.onStateChange = r),
          (this.unsubscribe = null),
          (this.listeners = U);
      }
      var t = e.prototype;
      return (
        (t.addNestedSub = function (e) {
          return this.trySubscribe(), this.listeners.subscribe(e);
        }),
        (t.notifyNestedSubs = function () {
          this.listeners.notify();
        }),
        (t.isSubscribed = function () {
          return !!this.unsubscribe;
        }),
        (t.trySubscribe = function () {
          var r, n;
          this.unsubscribe ||
            ((this.unsubscribe = this.parentSub
              ? this.parentSub.addNestedSub(this.onStateChange)
              : this.store.subscribe(this.onStateChange)),
            (this.listeners =
              ((r = []),
              (n = []),
              {
                clear: function () {
                  r = n = null;
                },
                notify: function () {
                  for (var e = (r = n), t = 0; t < e.length; t++) e[t]();
                },
                get: function () {
                  return n;
                },
                subscribe: function (e) {
                  var t = !0;
                  return (
                    n === r && (n = r.slice()),
                    n.push(e),
                    function () {
                      t && null !== r && ((t = !1), n === r && (n = r.slice()), n.splice(n.indexOf(e), 1));
                    }
                  );
                },
              })));
        }),
        (t.tryUnsubscribe = function () {
          this.unsubscribe &&
            (this.unsubscribe(), (this.unsubscribe = null), this.listeners.clear(), (this.listeners = U));
        }),
        e
      );
    })(),
    I = void 0 !== t.forwardRef,
    W = 0,
    A = {};
  function k() {}
  function H(s, e) {
    var t, r;
    void 0 === e && (e = {});
    var n = e.getDisplayName,
      u =
        void 0 === n
          ? function (e) {
              return 'ConnectAdvanced(' + e + ')';
            }
          : n,
      o = e.methodName,
      c = void 0 === o ? 'connectAdvanced' : o,
      i = e.renderCountProp,
      a = void 0 === i ? void 0 : i,
      p = e.shouldHandleStateChanges,
      f = void 0 === p || p,
      d = e.storeKey,
      l = void 0 === d ? 'store' : d,
      y = e.withRef,
      h = void 0 !== y && y,
      m = T(e, ['getDisplayName', 'methodName', 'renderCountProp', 'shouldHandleStateChanges', 'storeKey', 'withRef']),
      b = l + 'Subscription',
      v = W++,
      S = (((t = {})[l] = w), (t[b] = C), t),
      P = (((r = {})[b] = C), r);
    return function (r) {
      j(D(r), 'You must pass a component to the function returned by ' + c + '. Instead received ' + JSON.stringify(r));
      var e = r.displayName || r.name || 'Component',
        o = u(e),
        i = x({}, m, {
          getDisplayName: u,
          methodName: c,
          renderCountProp: a,
          shouldHandleStateChanges: f,
          storeKey: l,
          withRef: h,
          displayName: o,
          wrappedComponentName: e,
          WrappedComponent: r,
        }),
        t = (function (n) {
          function e(e, t) {
            var r;
            return (
              ((r = n.call(this, e, t) || this).version = v),
              (r.state = {}),
              (r.renderCount = 0),
              (r.store = e[l] || t[l]),
              (r.propsMode = !!e[l]),
              (r.setWrappedInstance = r.setWrappedInstance.bind($($(r)))),
              j(
                r.store,
                'Could not find "' +
                  l +
                  '" in either the context or props of "' +
                  o +
                  '". Either wrap the root component in a <Provider>, or explicitly pass "' +
                  l +
                  '" as a prop to "' +
                  o +
                  '".',
              ),
              r.initSelector(),
              r.initSubscription(),
              r
            );
          }
          O(e, n);
          var t = e.prototype;
          return (
            (t.getChildContext = function () {
              var e;
              return ((e = {})[b] = (this.propsMode ? null : this.subscription) || this.context[b]), e;
            }),
            (t.componentDidMount = function () {
              f &&
                (this.subscription.trySubscribe(),
                this.selector.run(this.props),
                this.selector.shouldComponentUpdate && this.forceUpdate());
            }),
            (t.componentWillReceiveProps = function (e) {
              this.selector.run(e);
            }),
            (t.shouldComponentUpdate = function () {
              return this.selector.shouldComponentUpdate;
            }),
            (t.componentWillUnmount = function () {
              this.subscription && this.subscription.tryUnsubscribe(),
                (this.subscription = null),
                (this.notifyNestedSubs = k),
                (this.store = null),
                (this.selector.run = k),
                (this.selector.shouldComponentUpdate = !1);
            }),
            (t.getWrappedInstance = function () {
              return (
                j(
                  h,
                  'To access the wrapped instance, you need to specify { withRef: true } in the options argument of the ' +
                    c +
                    '() call.',
                ),
                this.wrappedInstance
              );
            }),
            (t.setWrappedInstance = function (e) {
              this.wrappedInstance = e;
            }),
            (t.initSelector = function () {
              var r,
                n,
                o,
                e = s(this.store.dispatch, i);
              (this.selector =
                ((r = e),
                (n = this.store),
                (o = {
                  run: function (e) {
                    try {
                      var t = r(n.getState(), e);
                      (t !== o.props || o.error) && ((o.shouldComponentUpdate = !0), (o.props = t), (o.error = null));
                    } catch (e) {
                      (o.shouldComponentUpdate = !0), (o.error = e);
                    }
                  },
                }))),
                this.selector.run(this.props);
            }),
            (t.initSubscription = function () {
              f &&
                ((this.subscription = new F(
                  this.store,
                  (this.propsMode ? this.props : this.context)[b],
                  this.onStateChange.bind(this),
                )),
                (this.notifyNestedSubs = this.subscription.notifyNestedSubs.bind(this.subscription)));
            }),
            (t.onStateChange = function () {
              this.selector.run(this.props),
                this.selector.shouldComponentUpdate
                  ? ((this.componentDidUpdate = this.notifyNestedSubsOnComponentDidUpdate), this.setState(A))
                  : this.notifyNestedSubs();
            }),
            (t.notifyNestedSubsOnComponentDidUpdate = function () {
              (this.componentDidUpdate = void 0), this.notifyNestedSubs();
            }),
            (t.isSubscribed = function () {
              return !!this.subscription && this.subscription.isSubscribed();
            }),
            (t.addExtraProps = function (e) {
              if (!(h || a || (this.propsMode && this.subscription))) return e;
              var t = x({}, e);
              return (
                h && (t.ref = this.setWrappedInstance),
                a && (t[a] = this.renderCount++),
                this.propsMode && this.subscription && (t[b] = this.subscription),
                t
              );
            }),
            (t.render = function () {
              var e = this.selector;
              if (((e.shouldComponentUpdate = !1), e.error)) throw e.error;
              return g.createElement(r, this.addExtraProps(e.props));
            }),
            e
          );
        })(g.Component);
      return (
        I &&
          ((t.prototype.UNSAFE_componentWillReceiveProps = t.prototype.componentWillReceiveProps),
          delete t.prototype.componentWillReceiveProps),
        (t.WrappedComponent = r),
        (t.displayName = o),
        (t.childContextTypes = P),
        (t.contextTypes = S),
        (t.propTypes = S),
        _(t, r)
      );
    };
  }
  var V = Object.prototype.hasOwnProperty;
  function z(e, t) {
    return e === t ? 0 !== e || 0 !== t || 1 / e == 1 / t : e != e && t != t;
  }
  function L(e, t) {
    if (z(e, t)) return !0;
    if ('object' != typeof e || null === e || 'object' != typeof t || null === t) return !1;
    var r = Object.keys(e);
    if (r.length !== Object.keys(t).length) return !1;
    for (var n = 0; n < r.length; n++) if (!V.call(t, r[n]) || !z(e[r[n]], t[r[n]])) return !1;
    return !0;
  }
  function K(o) {
    return function (e, t) {
      var r = o(e, t);
      function n() {
        return r;
      }
      return (n.dependsOnOwnProps = !1), n;
    };
  }
  function Y(e) {
    return null != e.dependsOnOwnProps ? !!e.dependsOnOwnProps : 1 !== e.length;
  }
  function B(o, e) {
    return function (e, t) {
      var n = function (e, t) {
        return n.dependsOnOwnProps ? n.mapToProps(e, t) : n.mapToProps(e);
      };
      return (
        (n.dependsOnOwnProps = !0),
        (n.mapToProps = function (e, t) {
          (n.mapToProps = o), (n.dependsOnOwnProps = Y(o));
          var r = n(e, t);
          return 'function' == typeof r && ((n.mapToProps = r), (n.dependsOnOwnProps = Y(r)), (r = n(e, t))), r;
        }),
        n
      );
    };
  }
  function J(e, t, r) {
    return x({}, r, e, t);
  }
  function G(r, n, o, i) {
    return function (e, t) {
      return o(r(e, t), n(i, t), t);
    };
  }
  function Q(s, u, c, a, e) {
    var p,
      f,
      d,
      l,
      y,
      h = e.areStatesEqual,
      m = e.areOwnPropsEqual,
      b = e.areStatePropsEqual,
      r = !1;
    function n(e, t) {
      var r,
        n,
        o = !m(t, f),
        i = !h(e, p);
      return (
        (p = e),
        (f = t),
        o && i
          ? ((d = s(p, f)), u.dependsOnOwnProps && (l = u(a, f)), (y = c(d, l, f)))
          : o
          ? (s.dependsOnOwnProps && (d = s(p, f)), u.dependsOnOwnProps && (l = u(a, f)), (y = c(d, l, f)))
          : (i && ((r = s(p, f)), (n = !b(r, d)), (d = r), n && (y = c(d, l, f))), y)
      );
    }
    return function (e, t) {
      return r ? n(e, t) : ((d = s((p = e), (f = t))), (l = u(a, f)), (y = c(d, l, f)), (r = !0), y);
    };
  }
  function X(e, t) {
    var r = t.initMapStateToProps,
      n = t.initMapDispatchToProps,
      o = t.initMergeProps,
      i = T(t, ['initMapStateToProps', 'initMapDispatchToProps', 'initMergeProps']),
      s = r(e, i),
      u = n(e, i),
      c = o(e, i);
    return (i.pure ? Q : G)(s, u, c, e, i);
  }
  function Z(r, e, n) {
    for (var t = e.length - 1; 0 <= t; t--) {
      var o = e[t](r);
      if (o) return o;
    }
    return function (e, t) {
      throw Error(
        'Invalid value of type ' +
          typeof r +
          ' for ' +
          n +
          ' argument when connecting component ' +
          t.wrappedComponentName +
          '.',
      );
    };
  }
  function ee(e, t) {
    return e === t;
  }
  var te,
    re,
    ne,
    oe,
    ie,
    se,
    ue,
    ce,
    ae,
    pe,
    fe,
    de,
    le =
      ((oe = void 0 === (ne = (re = void 0 === te ? {} : te).connectHOC) ? H : ne),
      (se =
        void 0 === (ie = re.mapStateToPropsFactories)
          ? [
              function (e) {
                return 'function' == typeof e ? B(e) : void 0;
              },
              function (e) {
                return e
                  ? void 0
                  : K(function () {
                      return {};
                    });
              },
            ]
          : ie),
      (ce =
        void 0 === (ue = re.mapDispatchToPropsFactories)
          ? [
              function (e) {
                return 'function' == typeof e ? B(e) : void 0;
              },
              function (e) {
                return e
                  ? void 0
                  : K(function (e) {
                      return { dispatch: e };
                    });
              },
              function (t) {
                return t && 'object' == typeof t
                  ? K(function (e) {
                      return r.bindActionCreators(t, e);
                    })
                  : void 0;
              },
            ]
          : ue),
      (pe =
        void 0 === (ae = re.mergePropsFactories)
          ? [
              function (e) {
                return 'function' == typeof e
                  ? ((c = e),
                    function (e, t) {
                      var o,
                        i = t.pure,
                        s = t.areMergedPropsEqual,
                        u = !1;
                      return function (e, t, r) {
                        var n = c(e, t, r);
                        return u ? (i && s(n, o)) || (o = n) : ((u = !0), (o = n)), o;
                      };
                    })
                  : void 0;
                var c;
              },
              function (e) {
                return e
                  ? void 0
                  : function () {
                      return J;
                    };
              },
            ]
          : ae),
      (de = void 0 === (fe = re.selectorFactory) ? X : fe),
      function (e, t, r, n) {
        void 0 === n && (n = {});
        var o = n.pure,
          i = void 0 === o || o,
          s = n.areStatesEqual,
          u = void 0 === s ? ee : s,
          c = n.areOwnPropsEqual,
          a = void 0 === c ? L : c,
          p = n.areStatePropsEqual,
          f = void 0 === p ? L : p,
          d = n.areMergedPropsEqual,
          l = void 0 === d ? L : d,
          y = T(n, ['pure', 'areStatesEqual', 'areOwnPropsEqual', 'areStatePropsEqual', 'areMergedPropsEqual']),
          h = Z(e, se, 'mapStateToProps'),
          m = Z(t, ce, 'mapDispatchToProps'),
          b = Z(r, pe, 'mergeProps');
        return oe(
          de,
          x(
            {
              methodName: 'connect',
              getDisplayName: function (e) {
                return 'Connect(' + e + ')';
              },
              shouldHandleStateChanges: !!e,
              initMapStateToProps: h,
              initMapDispatchToProps: m,
              initMergeProps: b,
              pure: i,
              areStatesEqual: u,
              areOwnPropsEqual: a,
              areStatePropsEqual: f,
              areMergedPropsEqual: l,
            },
            y,
          ),
        );
      });
  (e.Provider = f),
    (e.createProvider = p),
    (e.connectAdvanced = H),
    (e.connect = le),
    Object.defineProperty(e, '__esModule', { value: !0 });
});
